﻿namespace AutoUpdater
{
    internal static class Program
    {
        /// <summary>
        ///  The main entry point for the application.
        /// </summary>
        private static readonly Mutex mutex = new(true, "AutoUpdaterQAUtility");
        [STAThread]
        private static void Main()
        {
            // To customize application configuration such as set high DPI settings or default font,
            // see https://aka.ms/applicationconfiguration.
            if (mutex.WaitOne(TimeSpan.Zero, true))
            {
                ApplicationConfiguration.Initialize();
                Application.Run(new Form1());

                mutex.ReleaseMutex();
            }
            else
            {
                _ = MessageBox.Show("Uygulama zaten çalışıyor!", "<PERSON>yarı", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
            }
        }
    }
}