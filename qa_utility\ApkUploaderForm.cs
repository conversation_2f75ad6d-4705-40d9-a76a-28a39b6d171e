using System;
using System.Drawing;
using System.IO;
using System.Windows.Forms;
using System.Linq;

namespace qa_utility
{
    public partial class ApkUploaderForm : Form
    {
        public string UploadedFilePath { get; private set; }
        public bool UploadSuccess { get; private set; }

        // Form1'in reflection ile çağrılması yerine event kullanalım.
        public event EventHandler<string> ApkReadyToInstall;

        public ApkUploaderForm()
        {
            InitializeComponent();
            SetupForm();
        }

        private void SetupForm()
        {
            // Form setup
            this.Text = "APK Yükleyici";
            this.Size = new Size(500, 350);
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.StartPosition = FormStartPosition.CenterScreen;
            this.AllowDrop = true;

            // Create drop zone panel
            Panel dropZonePanel = new Panel
            {
                BorderStyle = BorderStyle.FixedSingle,
                BackColor = Color.LightGray,
                Dock = DockStyle.Fill,
                Margin = new Padding(20),
                AllowDrop = true
            };

            // Create label for instructions
            Label instructionsLabel = new Label
            {
                Text = "APK dosyasını bu alana sür<PERSON>kleyip bırakın",
                AutoSize = false,
                TextAlign = ContentAlignment.MiddleCenter,
                Dock = DockStyle.Fill,
                Font = new Font("Arial", 14)
            };
            dropZonePanel.Controls.Add(instructionsLabel);

            // Create status label
            Label statusLabel = new Label
            {
                Text = "",
                Dock = DockStyle.Bottom,
                Height = 30,
                TextAlign = ContentAlignment.MiddleCenter
            };

            // Create upload button
            Button uploadButton = new Button
            {
                Text = "Yükle",
                Dock = DockStyle.Bottom,
                Height = 40,
                Enabled = false
            };

            // Add controls to form
            this.Controls.Add(dropZonePanel);
            this.Controls.Add(statusLabel);
            this.Controls.Add(uploadButton);

            // Events
            this.DragEnter += (s, e) => {
                if (e.Data.GetDataPresent(DataFormats.FileDrop))
                    e.Effect = DragDropEffects.Copy;
            };

            dropZonePanel.DragEnter += (s, e) => {
                if (e.Data.GetDataPresent(DataFormats.FileDrop))
                    e.Effect = DragDropEffects.Copy;
            };

            this.DragDrop += (s, e) => HandleFileDrop(e, statusLabel, uploadButton);
            dropZonePanel.DragDrop += (s, e) => HandleFileDrop(e, statusLabel, uploadButton);

            uploadButton.Click += (s, e) => {
                if (!string.IsNullOrEmpty(UploadedFilePath))
                {
                    UploadFile(statusLabel);
                    uploadButton.Enabled = false;
                }
            };
        }

        private void HandleFileDrop(DragEventArgs e, Label statusLabel, Button uploadButton)
        {
            string[] files = (string[])e.Data.GetData(DataFormats.FileDrop);
            if (files.Length > 0)
            {
                string file = files[0];
                if (Path.GetExtension(file).ToLower() == ".apk")
                {
                    UploadedFilePath = file;
                    statusLabel.Text = $"Dosya hazır: {Path.GetFileName(file)}";
                    statusLabel.ForeColor = Color.Green;
                    uploadButton.Enabled = true;
                }
                else
                {
                    statusLabel.Text = "Lütfen sadece APK dosyası yükleyin";
                    statusLabel.ForeColor = Color.Red;
                    uploadButton.Enabled = false;
                }
            }
        }

        private void UploadFile(Label statusLabel)
        {
            try
            {
                statusLabel.Text = "APK Yükleniyor...";
                statusLabel.ForeColor = Color.Blue;

                // ApkReadyToInstall event'ini tetikle. Form1 bu event'i dinleyecek.
                ApkReadyToInstall?.Invoke(this, UploadedFilePath);

                statusLabel.Text = "Yükleme başladı!";
                statusLabel.ForeColor = Color.Green;
                UploadSuccess = true;

                // Close form after successful upload (with delay)
                timer1.Interval = 1500;
                timer1.Tick += (s, e) => {
                    timer1.Stop();
                    this.DialogResult = DialogResult.OK;
                    this.Close();
                };
                timer1.Start();
            }
            catch (Exception ex)
            {
                statusLabel.Text = $"Yükleme hatası: {ex.Message}";
                statusLabel.ForeColor = Color.Red;
                UploadSuccess = false;
            }
        }

        // This method will need to be generated by the Designer
        private void InitializeComponent()
        {
            components = new System.ComponentModel.Container();
            timer1 = new System.Windows.Forms.Timer(components);
            SuspendLayout();
            // 
            // ApkUploaderForm
            // 
            ClientSize = new Size(500, 350);
            Name = "ApkUploaderForm";
            ResumeLayout(false);
        }
        private System.Windows.Forms.Timer timer1;
        private System.ComponentModel.IContainer components;
    }
}