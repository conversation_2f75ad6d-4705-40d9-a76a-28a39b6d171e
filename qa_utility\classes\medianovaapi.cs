﻿using System;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json;

namespace MedianovaApi
{
    public class MedianovaApiClient
    {
        private readonly HttpClient _httpClient;
        private readonly string _apiKey;
        private readonly string _apiSecret;
        private string _accessToken;

        // Authentication endpoint: cURL örneğinde verilen URL.
        private const string AuthEndpoint = "https://cloud.medianova.com/api/v1/auth/login/token";

        public MedianovaApiClient(string apiKey, string apiSecret)
        {
            _apiKey = apiKey;
            _apiSecret = apiSecret;
            _httpClient = new HttpClient();
        }

        /// <summary>
        /// Authentication işlemini gerçekleştirir.
        /// cURL örneğinde yer alan "api_key" ve "api_secret" parametreleri kullanılır.
        /// </summary>
        public async Task<bool> AuthenticateAsync()
        {
            var authRequest = new
            {
                api_key = _apiKey,
                api_secret = _apiSecret
            };

            var json = JsonConvert.SerializeObject(authRequest);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            try
            {
                var response = await _httpClient.PostAsync(AuthEndpoint, content);
                if (response.IsSuccessStatusCode)
                {
                    var responseJson = await response.Content.ReadAsStringAsync();
                    dynamic result = JsonConvert.DeserializeObject(responseJson);
                    // Token alanı "token" olarak varsayılmıştır; dönen JSON'a göre düzenleyiniz.
                    _accessToken = result.token;

                    // Sonraki isteklerde kullanmak üzere Authorization header'ı ayarlanıyor.
                    _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", _accessToken);
                    return true;
                }
                else
                {
                    Console.WriteLine($"Authentication başarısız. Durum kodu: {response.StatusCode}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Authentication sırasında hata: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Belirtilen organization ve resource UUID değerleri için purge isteği gönderir.
        /// Varsayılan olarak "file_path" değeri "/*" gönderilmektedir.
        /// </summary>
        /// <param name="organizationUuid">Organization UUID değeri</param>
        /// <param name="resourceUuid">Resource UUID değeri</param>
        /// <param name="filePaths">Purge edilecek dosya yolları (opsiyonel, varsayılan: "/*")</param>
        public async Task<bool> PurgeCacheAsync(string organizationUuid, string resourceUuid, string[] filePaths = null)
        {
            if (string.IsNullOrEmpty(_accessToken))
            {
                throw new InvalidOperationException("Authentication yapılmadı. Lütfen önce AuthenticateAsync() metodunu çağırın.");
            }

            // Varsayılan dosya yolu değeri
            filePaths = filePaths ?? new string[] { "/*" };

            // cURL örneğinde verilen URL şablonuna göre endpoint oluşturuluyor.
            string purgeEndpoint = $"https://cloud.medianova.com/api/v1/cdn/{organizationUuid}/job/{resourceUuid}/purge";

            // JSON payload hazırlanıyor.
            var purgeRequest = new
            {
                file_path = filePaths
            };

            var json = JsonConvert.SerializeObject(purgeRequest);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            try
            {
                // Purge isteği gönderiliyor.
                var response = await _httpClient.PostAsync(purgeEndpoint, content);
                if (response.IsSuccessStatusCode)
                {
                    var body = await response.Content.ReadAsStringAsync();
                    Console.WriteLine("Purge Response: " + body);
                    return true;
                }
                else
                {
                    Console.WriteLine($"Purge isteği başarısız. Durum kodu: {response.StatusCode}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Purge isteği sırasında hata: {ex.Message}");
                return false;
            }
        }
    }
}
