version: 2
updates:
  # NuGet paketleri için günce<PERSON>er
  - package-ecosystem: "nuget"
    directory: "/"
    schedule:
      interval: "weekly"
      day: "monday"
    open-pull-requests-limit: 10
    target-branch: "develop"
    labels:
      - "dependencies"
      - "nuget"
    assignees:
      - "joygame-bot"
    reviewers:
      - "joygame/qa-utility-maintainers"
    commit-message:
      prefix: "deps"
      include: "scope"

  # GitHub Actions için güncellemeler
  - package-ecosystem: "github-actions"
    directory: "/"
    schedule:
      interval: "weekly"
      day: "monday"
    open-pull-requests-limit: 5
    target-branch: "develop"
    labels:
      - "dependencies"
      - "github-actions"
    assignees:
      - "joygame-bot"
    reviewers:
      - "joygame/qa-utility-devops"
    commit-message:
      prefix: "ci"
      include: "scope"

  # npm paketleri i<PERSON><PERSON> (WebView2 içeriği için)
  - package-ecosystem: "npm"
    directory: "/qa_utility/Resources"
    schedule:
      interval: "weekly"
      day: "monday"
    open-pull-requests-limit: 5
    target-branch: "develop"
    labels:
      - "dependencies"
      - "npm"
    assignees:
      - "joygame-bot"
    reviewers:
      - "joygame/qa-utility-frontend"
    commit-message:
      prefix: "deps"
      include: "scope"