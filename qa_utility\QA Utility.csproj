﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net8.0-windows7.0</TargetFramework>
    <Nullable>enable</Nullable>
    <UseWindowsForms>true</UseWindowsForms>
    <ImplicitUsings>enable</ImplicitUsings>
    <FileVersion>1.0.0</FileVersion>
    <AssemblyVersion>1.0.0</AssemblyVersion>
    <PackageIcon>logo.png</PackageIcon>
    <StartupObject>QA_Utility.Program</StartupObject>
    <!--versiyon bilgisini değ<PERSON>şkenden al--> 
    <Version>2.75</Version> 
    <ApplicationIcon>logo.ico</ApplicationIcon> 
    <UseWPF>True</UseWPF> 
        
  </PropertyGroup>

  <ItemGroup>
    <None Remove="Resources\index.html" />
  </ItemGroup>

  <ItemGroup>
    <Content Include="Resources\index.html">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
  </ItemGroup>

  <ItemGroup>
    <Content Include="logo.ico" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Google.Apis" Version="1.69.0" />
    <PackageReference Include="Google.Apis.Auth" Version="1.69.0" />
    <PackageReference Include="Google.Apis.Core" Version="1.69.0" />
    <PackageReference Include="Google.Apis.Drive.v3" Version="1.69.0.3703" />
    <PackageReference Include="Google.Apis.Oauth2.v2" Version="1.68.0.1869" />
    <PackageReference Include="Microsoft.Web.WebView2" Version="1.0.2903.40" />
    <PackageReference Include="MySql.Data" Version="9.0.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="redmine-api" Version="4.6.5" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\AutoUpdater\AutoUpdater.csproj">
      <TreatAsUsed>true</TreatAsUsed>
    </ProjectReference>
  </ItemGroup>

  <ItemGroup>
    <Compile Update="Properties\Resources.Designer.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Update="Properties\Settings.Designer.cs">
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
    </Compile>
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Update="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
    </EmbeddedResource>
  </ItemGroup>

  <ItemGroup>
    <None Update="client_secret.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="flashlight\flashlight.exe">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="ioslog\bz2.dll">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="ioslog\getopt.dll">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="ioslog\iconv-2.dll">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="ioslog\ideviceactivation.dll">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="ioslog\ideviceactivation.exe">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="ioslog\idevicebackup.exe">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="ioslog\idevicebackup2.exe">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="ioslog\idevicecrashreport.exe">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="ioslog\idevicedate.exe">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="ioslog\idevicedebug.exe">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="ioslog\idevicedebugserverproxy.exe">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="ioslog\idevicediagnostics.exe">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="ioslog\ideviceenterrecovery.exe">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="ioslog\ideviceimagemounter.exe">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="ioslog\ideviceinfo.exe">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="ioslog\ideviceinstaller.exe">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="ioslog\idevicename.exe">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="ioslog\idevicenotificationproxy.exe">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="ioslog\idevicepair.exe">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="ioslog\ideviceprovision.exe">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="ioslog\idevicerestore.exe">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="ioslog\idevicescreenshot.exe">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="ioslog\idevicesyslog.exe">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="ioslog\idevice_id.exe">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="ioslog\imobiledevice-net-lighthouse.dll">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="ioslog\imobiledevice.dll">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="ioslog\ios_webkit_debug_proxy.exe">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="ioslog\iproxy.exe">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="ioslog\irecovery.dll">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="ioslog\irecovery.exe">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="ioslog\libcrypto-1_1-x64.dll">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="ioslog\libcurl.dll">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="ioslog\libssl-1_1-x64.dll">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="ioslog\libusb-1.0.dll">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="ioslog\libusb0.dll">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="ioslog\libxml2.dll">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="ioslog\lzma.dll">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="ioslog\pcre.dll">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="ioslog\pcreposix.dll">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="ioslog\plist.dll">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="ioslog\plistutil.exe">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="ioslog\plist_cmp.exe">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="ioslog\plist_test.exe">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="ioslog\pthreadVC3.dll">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="ioslog\readline.dll">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="ioslog\usbmuxd.dll">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="ioslog\usbmuxd.exe">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="ioslog\vcruntime140.dll">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="ioslog\zip.dll">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="ioslog\zlib1.dll">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="platform-tools\adb.exe">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="platform-tools\AdbWinApi.dll">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="platform-tools\AdbWinUsbApi.dll">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="platform-tools\dmtracedump.exe">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="platform-tools\etc1tool.exe">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="platform-tools\fastboot.exe">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="platform-tools\hprof-conv.exe">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="platform-tools\libwinpthread-1.dll">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="platform-tools\make_f2fs.exe">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="platform-tools\make_f2fs_casefold.exe">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="platform-tools\mke2fs.conf">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="platform-tools\mke2fs.exe">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="platform-tools\NOTICE.txt">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="platform-tools\source.properties">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="platform-tools\sqlite3.exe">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
    <None Update="Resources\arriving.mp3">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="Resources\contextMenuHelper.js">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="Resources\scripts.js">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="Resources\styles.css">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="Resources\testrail.js">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
  </ItemGroup>

  <ItemGroup>
    <Folder Include="flashlight\" />
  </ItemGroup>

</Project>