# Joygame QA Utility - <PERSON><PERSON><PERSON><PERSON> So<PERSON>ru<PERSON> (SSS)

## İçindekiler

1. [<PERSON><PERSON>](#1-genel-sorular)
2. [<PERSON><PERSON><PERSON> ve Yapılandırma](#2-kurulum-ve-yapılandırma)
3. [<PERSON><PERSON> Cihaz Bağlantısı](#3-mobil-cihaz-bağlantısı)
4. [APK Yükleme ve Yönetimi](#4-apk-yükleme-ve-yönetimi)
5. [<PERSON><PERSON><PERSON> Kayd<PERSON> ve Görüntüsü](#5-ekran-kaydı-ve-görüntüsü)
6. [Log Yönetimi](#6-log-yönetimi)
7. [TestRail Entegrasyonu](#7-testrail-entegrasyonu)
8. [Redmine Entegrasyonu](#8-redmine-entegrasyonu)
9. [Google API Entegrasyonu](#9-google-api-entegrasyonu)
10. [<PERSON><PERSON><PERSON><PERSON><PERSON> ve Bakım](#10-güncelleme-ve-bakım)
11. [<PERSON><PERSON>](#11-sorun-gider<PERSON>)
12. [<PERSON><PERSON><PERSON><PERSON>](#12-g<PERSON><PERSON><PERSON>)

## 1. Genel Sorular

### 1.1 Joygame QA Utility nedir?

Joygame QA Utility, mobil oyun ve uygulamaların test edilmesi için tasarlanmış kapsamlı bir QA (Kalite Güvence) aracıdır. Android ve iOS cihazlarla etkileşim kurma, APK yükleme, ekran kaydı alma, log yönetimi ve TestRail, Redmine gibi servislerle entegrasyon sağlar.

### 1.2 Joygame QA Utility hangi platformları destekler?

Joygame QA Utility, Windows işletim sisteminde çalışır ve Android ve iOS mobil cihazlarla etkileşim kurabilir.

### 1.3 Joygame QA Utility'nin ana özellikleri nelerdir?

- Android ve iOS cihaz yönetimi
- APK yükleme ve kaldırma
- Ekran kaydı ve ekran görüntüsü alma
- Log kayıt ve analizi
- TestRail entegrasyonu
- Redmine entegrasyonu
- Google API entegrasyonu
- Otomatik güncelleme sistemi

### 1.4 Joygame QA Utility'yi kimler kullanabilir?

Joygame QA Utility, öncelikle QA ekipleri, test mühendisleri, oyun geliştiricileri ve mobil uygulama testçileri için tasarlanmıştır. Ancak, mobil uygulamaları test etmek isteyen herkes tarafından kullanılabilir.

### 1.5 Joygame QA Utility açık kaynaklı mıdır?

Hayır, Joygame QA Utility kapalı kaynaklı bir uygulamadır ve Joygame tarafından geliştirilmektedir.

## 2. Kurulum ve Yapılandırma

### 2.1 Joygame QA Utility'yi nasıl kurarım?

Kurulum için şu adımları izleyin:

1. Kurulum paketini indirin
2. Kurulum dosyasını çalıştırın ve ekrandaki talimatları izleyin
3. Gerekli bağımlılıkların (.NET Framework, WebView2 Runtime) yüklü olduğundan emin olun
4. Kurulum tamamlandıktan sonra uygulamayı başlatın

Detaylı kurulum talimatları için [Kurulum Kılavuzu](installation_guide.md) belgesine bakın.

### 2.2 Joygame QA Utility için sistem gereksinimleri nelerdir?

- **İşletim Sistemi**: Windows 10 veya üzeri (64-bit önerilir)
- **İşlemci**: Intel Core i3 veya dengi (2.0 GHz veya üzeri)
- **RAM**: En az 4 GB (8 GB önerilir)
- **Disk Alanı**: En az 500 MB boş alan
- **Ekran Çözünürlüğü**: 1280x720 veya üzeri
- **.NET Framework**: 4.7.2 veya üzeri
- **WebView2 Runtime**: En son sürüm

### 2.3 Joygame QA Utility'yi nasıl yapılandırırım?

Uygulama yapılandırması `App.config` dosyasında bulunur. Bu dosyada veritabanı bağlantı dizesi, API anahtarları ve diğer ayarları yapılandırabilirsiniz. Ayrıca, uygulama içindeki ayarlar menüsünden de bazı yapılandırmaları değiştirebilirsiniz.

### 2.4 Joygame QA Utility'yi birden fazla bilgisayara nasıl kurarım?

Kurulum paketini her bilgisayara ayrı ayrı yükleyebilirsiniz. Ayrıca, merkezi bir yapılandırma için, `App.config` dosyasını bir şablon olarak kullanabilir ve her kurulumda bu dosyayı güncelleyebilirsiniz.

### 2.5 Joygame QA Utility'yi kaldırmak için ne yapmalıyım?

Windows Denetim Masası'ndan "Program Ekle veya Kaldır" seçeneğini kullanarak uygulamayı kaldırabilirsiniz. Alternatif olarak, kurulum klasöründeki kaldırma programını da kullanabilirsiniz.

## 3. Mobil Cihaz Bağlantısı

### 3.1 Android cihazımı Joygame QA Utility'ye nasıl bağlarım?

1. Android cihazınızda USB hata ayıklama modunu etkinleştirin:
   - Ayarlar > Geliştirici Seçenekleri > USB Hata Ayıklama
2. Cihazı USB kablosuyla bilgisayarınıza bağlayın
3. Cihazda "Bu bilgisayara güveniyor musunuz?" uyarısını onaylayın
4. Joygame QA Utility'de "Android" sekmesini seçin
5. "Cihazları Yenile" düğmesine tıklayın

### 3.2 iOS cihazımı Joygame QA Utility'ye nasıl bağlarım?

1. iTunes'un bilgisayarınızda yüklü olduğundan emin olun
2. iOS cihazı USB kablosuyla bilgisayarınıza bağlayın
3. Cihazda "Bu bilgisayara güveniyor musunuz?" uyarısını onaylayın
4. Joygame QA Utility'de "iOS" sekmesini seçin
5. "Cihazları Yenile" düğmesine tıklayın

### 3.3 Cihazım algılanmıyorsa ne yapmalıyım?

Android cihazlar için:
1. USB hata ayıklama modunun etkin olduğunu kontrol edin
2. USB kablosunu değiştirin
3. Farklı bir USB portu deneyin
4. ADB'yi yeniden başlatın: `adb kill-server` ve `adb start-server`
5. Cihaz sürücülerinin yüklü olduğunu kontrol edin

iOS cihazlar için:
1. iTunes'un yüklü olduğunu kontrol edin
2. USB kablosunu değiştirin
3. Cihazın bilgisayara güvendiğini kontrol edin
4. iOS cihaz sürücülerini yeniden yükleyin

### 3.4 Birden fazla cihazı aynı anda bağlayabilir miyim?

Evet, Joygame QA Utility birden fazla Android ve iOS cihazını aynı anda yönetebilir. Cihazlar listesinden istediğiniz cihazı seçebilirsiniz.

### 3.5 Kablosuz olarak cihaz bağlayabilir miyim?

Android cihazlar için, ADB'nin kablosuz modunu kullanarak Wi-Fi üzerinden bağlantı kurabilirsiniz:

1. Cihazı USB ile bağlayın
2. ADB kablosuz modunu etkinleştirin: `adb tcpip 5555`
3. Cihazın IP adresini öğrenin
4. USB kablosunu çıkarın
5. Kablosuz bağlantı kurun: `adb connect <IP-adresi>:5555`

iOS cihazlar için kablosuz bağlantı şu anda desteklenmemektedir.

## 4. APK Yükleme ve Yönetimi

### 4.1 APK dosyasını nasıl yüklerim?

1. "APK Yükle" düğmesine tıklayın veya APK dosyasını uygulama penceresine sürükleyip bırakın
2. APK dosyasını seçin
3. Bağlı bir Android cihaz seçin
4. "Yükle" düğmesine tıklayın

### 4.2 Yüklenen uygulamayı nasıl başlatırım?

1. Cihazlar listesinden bir Android cihaz seçin
2. "Uygulamalar" listesinden yüklenen uygulamayı bulun
3. "Başlat" düğmesine tıklayın

### 4.3 Bir uygulamayı nasıl kaldırırım?

1. Cihazlar listesinden bir Android cihaz seçin
2. "Uygulamalar" listesinden kaldırmak istediğiniz uygulamayı bulun
3. "Kaldır" düğmesine tıklayın

### 4.4 Uygulama verilerini nasıl temizlerim?

1. Cihazlar listesinden bir Android cihaz seçin
2. "Uygulamalar" listesinden bir uygulama seçin
3. "Verileri Temizle" düğmesine tıklayın

### 4.5 APK dosyasının içeriğini nasıl analiz ederim?

1. "APK Analiz" sekmesine gidin
2. APK dosyasını seçin veya sürükleyip bırakın
3. "Analiz Et" düğmesine tıklayın
4. Manifest dosyası, izinler, aktiviteler ve diğer bilgiler görüntülenecektir

## 5. Ekran Kaydı ve Görüntüsü

### 5.1 Mobil cihazdan ekran görüntüsü nasıl alırım?

1. Cihazlar listesinden bir cihaz seçin
2. "Ekran Görüntüsü" düğmesine tıklayın
3. Ekran görüntüsü alınacak ve belirtilen klasöre kaydedilecektir

### 5.2 Ekran kaydını nasıl başlatırım?

1. Cihazlar listesinden bir cihaz seçin
2. "Ekran Kaydı" sekmesine gidin
3. Kayıt ayarlarını yapılandırın (çözünürlük, kare hızı, vb.)
4. "Kaydı Başlat" düğmesine tıklayın
5. Kayıt başlayacaktır

### 5.3 Ekran kaydını nasıl durdurabilirim?

1. "Kaydı Durdur" düğmesine tıklayın
2. Kayıt dosyası belirtilen klasöre kaydedilecektir

### 5.4 Ekran kayıtları ve görüntüleri nereye kaydedilir?

Varsayılan olarak, ekran kayıtları ve görüntüleri `Belgeler\Joygame QA Utility\Screenshots` ve `Belgeler\Joygame QA Utility\Recordings` klasörlerine kaydedilir. Bu konumları uygulama ayarlarından değiştirebilirsiniz.

### 5.5 NVIDIA ShadowPlay'i nasıl kullanabilirim?

1. NVIDIA GeForce Experience'ın yüklü olduğundan emin olun
2. ShadowPlay'in etkin olduğunu kontrol edin
3. Joygame QA Utility'de "Ayarlar" sekmesine gidin
4. "NVIDIA ShadowPlay Kullan" seçeneğini etkinleştirin
5. Ekran kaydı için ShadowPlay ayarlarını yapılandırın

## 6. Log Yönetimi

### 6.1 Android cihazdan logları nasıl alabilirim?

1. Cihazlar listesinden bir Android cihaz seçin
2. "Loglar" sekmesine gidin
3. "Logcat Başlat" düğmesine tıklayın
4. Loglar gerçek zamanlı olarak görüntülenecektir
5. "Logları Kaydet" düğmesine tıklayarak logları bir dosyaya kaydedebilirsiniz

### 6.2 iOS cihazdan logları nasıl alabilirim?

1. Cihazlar listesinden bir iOS cihaz seçin
2. "Loglar" sekmesine gidin
3. "iOS Log Başlat" düğmesine tıklayın
4. Loglar gerçek zamanlı olarak görüntülenecektir
5. "Logları Kaydet" düğmesine tıklayarak logları bir dosyaya kaydedebilirsiniz

### 6.3 Logları nasıl filtreleyebilirim?

1. "Loglar" sekmesinde "Filtrele" alanına filtreleme kriterlerini girin
2. Belirli bir paket adı, log seviyesi veya metin için filtreleme yapabilirsiniz
3. "Uygula" düğmesine tıklayın

### 6.4 Log dosyalarını nasıl analiz edebilirim?

1. "Log Analizi" sekmesine gidin
2. Bir log dosyası seçin veya sürükleyip bırakın
3. "Analiz Et" düğmesine tıklayın
4. Hata ve uyarı istatistikleri, zaman çizelgesi ve diğer analizler görüntülenecektir

### 6.5 Uygulama loglarını nerede bulabilirim?

Joygame QA Utility'nin kendi logları `logs` klasöründe bulunur. Bu loglar, uygulama sorunlarını gidermek için kullanılabilir.

## 7. TestRail Entegrasyonu

### 7.1 TestRail entegrasyonunu nasıl yapılandırırım?

1. "Ayarlar" sekmesine gidin
2. "TestRail" bölümünde aşağıdaki bilgileri girin:
   - TestRail URL'si
   - Kullanıcı adı
   - API anahtarı
3. "Bağlantıyı Test Et" düğmesine tıklayın
4. Bağlantı başarılıysa, ayarları kaydedin

### 7.2 Test durumlarını nasıl görüntülerim?

1. "TestRail" sekmesine gidin
2. Bir proje seçin
3. Bir test paketi seçin
4. Test durumları listelenecektir

### 7.3 Test sonuçlarını nasıl gönderebilirim?

1. "TestRail" sekmesinde bir test durumu seçin
2. Test sonucunu seçin (Geçti, Başarısız, Engellendi, vb.)
3. Yorum ekleyin
4. Ekran görüntüleri veya loglar ekleyin
5. "Sonucu Gönder" düğmesine tıklayın

### 7.4 Test planlarını nasıl yönetebilirim?

1. "TestRail" sekmesinde "Test Planları" sekmesine gidin
2. Bir proje seçin
3. Mevcut test planları listelenecektir
4. Bir test planı seçerek detaylarını görüntüleyebilirsiniz

### 7.5 TestRail'e otomatik olarak ekran görüntüleri nasıl ekleyebilirim?

1. "Ayarlar" sekmesinde "TestRail" bölümüne gidin
2. "Ekran Görüntülerini Otomatik Ekle" seçeneğini etkinleştirin
3. Bir test sonucu gönderdiğinizde, son alınan ekran görüntüleri otomatik olarak eklenecektir

## 8. Redmine Entegrasyonu

### 8.1 Redmine entegrasyonunu nasıl yapılandırırım?

1. "Ayarlar" sekmesine gidin
2. "Redmine" bölümünde aşağıdaki bilgileri girin:
   - Redmine URL'si
   - API anahtarı
3. "Bağlantıyı Test Et" düğmesine tıklayın
4. Bağlantı başarılıysa, ayarları kaydedin

### 8.2 Redmine'da nasıl sorun oluşturabilirim?

1. "Redmine" sekmesine gidin
2. "Yeni Sorun" düğmesine tıklayın
3. Aşağıdaki bilgileri girin:
   - Proje
   - İzleyici (Hata, Özellik, vb.)
   - Konu
   - Açıklama
   - Öncelik
4. Ekran görüntüleri veya loglar ekleyin
5. "Oluştur" düğmesine tıklayın

### 8.3 Mevcut sorunları nasıl görüntüleyebilirim?

1. "Redmine" sekmesine gidin
2. Bir proje seçin
3. "Sorunları Getir" düğmesine tıklayın
4. Sorunlar listelenecektir

### 8.4 Bir sorunu nasıl güncelleyebilirim?

1. "Redmine" sekmesinde bir sorun seçin
2. "Düzenle" düğmesine tıklayın
3. Bilgileri güncelleyin
4. "Güncelle" düğmesine tıklayın

### 8.5 Redmine'a otomatik olarak logları nasıl ekleyebilirim?

1. "Ayarlar" sekmesinde "Redmine" bölümüne gidin
2. "Logları Otomatik Ekle" seçeneğini etkinleştirin
3. Bir sorun oluşturduğunuzda veya güncellediğinizde, son kaydedilen loglar otomatik olarak eklenecektir

## 9. Google API Entegrasyonu

### 9.1 Google API entegrasyonunu nasıl yapılandırırım?

1. Google Cloud Console'da bir proje oluşturun
2. Gerekli API'leri etkinleştirin (Drive, Sheets, vb.)
3. OAuth 2.0 kimlik bilgilerini oluşturun
4. `client_secret.json` dosyasını indirin
5. Bu dosyayı Joygame QA Utility uygulama klasörüne yerleştirin
6. Uygulamayı yeniden başlatın
7. İlk kullanımda, Google hesabınızla oturum açmanız istenecektir

### 9.2 Dosyaları Google Drive'a nasıl yükleyebilirim?

1. "Google Drive" sekmesine gidin
2. Yüklemek istediğiniz dosyaları seçin veya sürükleyip bırakın
3. Hedef klasörü seçin
4. "Yükle" düğmesine tıklayın

### 9.3 Ekran görüntülerini otomatik olarak Google Drive'a nasıl yükleyebilirim?

1. "Ayarlar" sekmesinde "Google Drive" bölümüne gidin
2. "Ekran Görüntülerini Otomatik Yükle" seçeneğini etkinleştirin
3. Hedef klasörü seçin
4. Bir ekran görüntüsü aldığınızda, otomatik olarak Google Drive'a yüklenecektir

### 9.4 Google Sheets verilerini nasıl görüntüleyebilirim?

1. "Google Sheets" sekmesine gidin
2. Elektronik tablo ID'sini girin veya bir elektronik tablo seçin
3. "Verileri Getir" düğmesine tıklayın
4. Elektronik tablo verileri görüntülenecektir

### 9.5 Google Play Store verilerine nasıl erişebilirim?

1. "Google Play" sekmesine gidin
2. Bir uygulama seçin
3. "Verileri Getir" düğmesine tıklayın
4. Uygulama verileri, derecelendirmeler ve yorumlar görüntülenecektir

## 10. Güncelleme ve Bakım

### 10.1 Joygame QA Utility'yi nasıl güncellerim?

Uygulama, AutoUpdater bileşeni sayesinde otomatik olarak güncellenebilir:

1. Uygulama başlatıldığında, güncellemeler otomatik olarak kontrol edilir
2. Yeni bir güncelleme varsa, size bildirilir
3. "Güncelle" düğmesine tıklayarak güncellemeyi başlatabilirsiniz
4. Güncelleme tamamlandıktan sonra uygulama yeniden başlatılır

### 10.2 Güncellemeleri manuel olarak nasıl kontrol edebilirim?

1. "Yardım" menüsünden "Güncellemeleri Kontrol Et" seçeneğini tıklayın
2. Uygulama, mevcut güncellemeleri kontrol edecektir

### 10.3 Eski log dosyalarını nasıl temizleyebilirim?

1. "Ayarlar" sekmesine gidin
2. "Log Yönetimi" bölümünde "Eski Logları Temizle" düğmesine tıklayın
3. Belirli bir tarihten önceki logları temizlemeyi seçebilirsiniz

### 10.4 Uygulama verilerini nasıl yedekleyebilirim?

1. "Ayarlar" sekmesinde "Yedekleme" bölümüne gidin
2. "Ayarları Yedekle" düğmesine tıklayın
3. Yedek dosyasını kaydetmek için bir konum seçin

### 10.5 Yedekten nasıl geri yükleyebilirim?

1. "Ayarlar" sekmesinde "Yedekleme" bölümüne gidin
2. "Yedekten Geri Yükle" düğmesine tıklayın
3. Yedek dosyasını seçin
4. "Geri Yükle" düğmesine tıklayın
5. Uygulama yeniden başlatılacaktır

## 11. Sorun Giderme

### 11.1 Uygulama başlatılamıyorsa ne yapmalıyım?

1. .NET Framework'ün doğru sürümünün yüklü olduğunu kontrol edin
2. WebView2 Runtime'ın yüklü olduğunu kontrol edin
3. Log dosyalarını kontrol edin (`logs` klasöründe)
4. Uygulamayı yönetici olarak çalıştırmayı deneyin
5. Başka bir örneğin zaten çalışıp çalışmadığını kontrol edin

### 11.2 WebView2 içeriği görüntülenmiyorsa ne yapmalıyım?

1. WebView2 Runtime'ın yüklü olduğunu kontrol edin
2. Uygulamayı yeniden başlatın
3. Geçici internet dosyalarını temizleyin:
   - `%LOCALAPPDATA%\Microsoft\Edge\User Data` klasörünü yedekleyin ve silin
4. İnternet bağlantınızı kontrol edin

### 11.3 ADB bağlantı sorunlarını nasıl çözebilirim?

1. USB hata ayıklama modunun etkin olduğunu kontrol edin
2. USB kablosunu değiştirin
3. Farklı bir USB portu deneyin
4. ADB'yi yeniden başlatın: `adb kill-server` ve `adb start-server`
5. Cihaz sürücülerinin yüklü olduğunu kontrol edin

### 11.4 iOS cihaz bağlantı sorunlarını nasıl çözebilirim?

1. iTunes'un yüklü olduğunu kontrol edin
2. USB kablosunu değiştirin
3. Cihazın bilgisayara güvendiğini kontrol edin
4. iOS cihaz sürücülerini yeniden yükleyin

### 11.5 Veritabanı bağlantı hatalarını nasıl çözebilirim?

1. MySQL sunucusunun çalıştığını kontrol edin
2. `App.config` dosyasındaki veritabanı bağlantı dizesini kontrol edin
3. Veritabanı kullanıcı adı ve şifresinin doğru olduğunu doğrulayın
4. Güvenlik duvarı ayarlarını kontrol edin

## 12. Güvenlik

### 12.1 Joygame QA Utility kullanıcı verilerini nasıl korur?

Joygame QA Utility, kullanıcı verilerini yerel olarak saklar ve hassas bilgileri (API anahtarları, şifreler) şifreleyerek korur. Veriler, kullanıcının izni olmadan üçüncü taraflarla paylaşılmaz.

### 12.2 API anahtarlarımı nasıl güvenli tutabilirim?

1. API anahtarlarınızı düzenli olarak değiştirin
2. Anahtarları yalnızca güvenilir bilgisayarlarda kullanın
3. Anahtarları paylaşmayın veya kaynak koduna dahil etmeyin
4. Minimum gerekli izinlerle API anahtarları oluşturun

### 12.3 LDAP kimlik doğrulaması nasıl çalışır?

LDAP kimlik doğrulaması, kullanıcıların şirket kimlik bilgileriyle oturum açmasını sağlar. Kimlik bilgileri güvenli bir şekilde iletilir ve yerel olarak saklanmaz. Oturum açma işlemi, LDAP sunucusu tarafından doğrulanır.

### 12.4 Uygulama güncellemeleri güvenli midir?

Evet, uygulama güncellemeleri güvenli bir FTP bağlantısı üzerinden indirilir ve dosya bütünlüğü doğrulanır. Güncellemeler, yalnızca güvenilir kaynaklardan gelir ve kötü amaçlı yazılım taramasından geçirilir.

### 12.5 Mobil cihazlarımın güvenliğini nasıl sağlayabilirim?

1. Yalnızca güvenilir kaynaklardan APK dosyaları yükleyin
2. Test tamamlandıktan sonra USB hata ayıklama modunu devre dışı bırakın
3. Cihazları şifre veya biyometrik kimlik doğrulama ile koruyun
4. Test cihazlarında hassas kişisel bilgileri saklamayın

---

**Not**: Bu SSS belgesi, Joygame QA Utility uygulamasıyla ilgili yaygın soruları kapsar. Daha fazla yardım için geliştirme ekibiyle iletişime geçin.