# Joygame QA Utility - <PERSON><PERSON>ler Sözlüğü

## İçindekiler

- [A](#a)
- [B](#b)
- [C](#c)
- [D](#d)
- [E](#e)
- [F](#f)
- [G](#g)
- [H](#h)
- [I](#i)
- [J](#j)
- [K](#k)
- [L](#l)
- [M](#m)
- [N](#n)
- [O](#o)
- [P](#p)
- [Q](#q)
- [R](#r)
- [S](#s)
- [T](#t)
- [U](#u)
- [V](#v)
- [W](#w)
- [X](#x)
- [Y](#y)
- [Z](#z)

## A

### ADB (Android Debug Bridge)
Android cihazlarla iletişim kurmak için kullanılan bir komut satırı aracı. Joygame QA Utility, Android cihazları yönetmek, uygulamaları yüklemek, kaldırmak ve log'ları almak için ADB'yi kullanır.

### APK (Android Package Kit)
Android uygulamalarının dağıtım ve kurulum dosya formatı. Joygame QA Utility, APK dosyalarını Android cihazlara yüklemek ve yönetmek için kullanılır.

### API (Application Programming Interface)
Bir yazılımın diğer yazılımlarla iletişim kurmasını sağlayan bir dizi tanım, protokol ve araç. Joygame QA Utility, TestRail, Redmine ve Google gibi çeşitli servislerle iletişim kurmak için API'ler kullanır.

### AutoUpdater
Joygame QA Utility'nin otomatik güncelleme bileşeni. Yeni sürümleri kontrol eder, indirir ve yükler.

## B

### Bundle ID
Bir mobil uygulamayı benzersiz şekilde tanımlayan kimlik. iOS uygulamaları için genellikle ters domain adı formatında olur (örn. com.joygame.app).

### Build
Bir yazılımın derlenmiş ve çalıştırılabilir sürümü. Joygame QA Utility, farklı oyun build'lerini test etmek için kullanılır.

## C

### CI/CD (Continuous Integration/Continuous Deployment)
Yazılım geliştirme sürecinde kod değişikliklerinin otomatik olarak test edilmesi ve dağıtılması için kullanılan bir yöntem.

### CSV (Comma-Separated Values)
Verilerin virgülle ayrılmış metin formatı. Joygame QA Utility, bazı verileri CSV formatında dışa aktarabilir.

## D

### DeviceInfo
Bir mobil cihazın özelliklerini (model, seri numarası, işletim sistemi sürümü vb.) içeren veri yapısı.

### Debug
Yazılımdaki hataları bulmak ve düzeltmek için kullanılan süreç. Joygame QA Utility, mobil uygulamaların debug edilmesine yardımcı olur.

## E

### Emulator
Bir cihazın yazılım tabanlı simülasyonu. Android ve iOS emülatörleri, gerçek cihazlar olmadan mobil uygulamaları test etmek için kullanılabilir.

## F

### FTP (File Transfer Protocol)
Dosya aktarımı için kullanılan bir ağ protokolü. Joygame QA Utility'nin AutoUpdater bileşeni, güncellemeleri FTP sunucusundan indirir.

### Form
Windows Forms uygulamalarında kullanıcı arayüzünün temel bileşeni. Joygame QA Utility, Form1 adlı ana form üzerine inşa edilmiştir.

## G

### Google API
Google hizmetlerine programatik erişim sağlayan bir dizi API. Joygame QA Utility, Google Drive, Google Sheets ve Google Play Store entegrasyonları için bu API'leri kullanır.

### GoogleApiService
Joygame QA Utility'de Google API'lerine erişim sağlayan sınıf.

## H

### HTML (HyperText Markup Language)
Web sayfalarını oluşturmak için kullanılan standart işaretleme dili. Joygame QA Utility'nin WebView2 tabanlı arayüzü HTML kullanır.

## I

### iOS
Apple'ın mobil cihazları için işletim sistemi. Joygame QA Utility, iOS cihazları ve uygulamaları test etmek için kullanılabilir.

### IPA (iOS App Store Package)
iOS uygulamalarının dağıtım ve kurulum dosya formatı.

### idevice
iOS cihazlarla iletişim kurmak için kullanılan araçlar seti (idevice_id, ideviceinstaller, vb.).

## J

### JavaScript
Web sayfalarında etkileşimli öğeler oluşturmak için kullanılan bir programlama dili. Joygame QA Utility'nin WebView2 tabanlı arayüzü JavaScript kullanır.

### Joygame
Joygame QA Utility'yi geliştiren şirket.

## L

### LDAP (Lightweight Directory Access Protocol)
Dizin hizmetlerine erişim için kullanılan bir protokol. Joygame QA Utility, kullanıcı kimlik doğrulaması için LDAP kullanabilir.

### LdapAuthenticator
Joygame QA Utility'de LDAP kimlik doğrulaması sağlayan sınıf.

### Logcat
Android cihazlardan sistem ve uygulama log'larını görüntülemek için kullanılan ADB aracı.

### log4net
.NET uygulamaları için loglama kütüphanesi. Joygame QA Utility, uygulama log'ları için log4net kullanır.

## M

### MySQL
Açık kaynaklı bir ilişkisel veritabanı yönetim sistemi. Joygame QA Utility, verileri depolamak için MySQL kullanır.

### Mutex
Bir kaynağa aynı anda yalnızca bir sürecin erişmesini sağlayan senkronizasyon mekanizması. Joygame QA Utility, uygulamanın yalnızca bir örneğinin çalışmasını sağlamak için Mutex kullanır.

## N

### NuGet
.NET platformu için paket yöneticisi. Joygame QA Utility, harici kütüphaneleri yönetmek için NuGet kullanır.

### NVIDIA ShadowPlay
NVIDIA grafik kartları için ekran kaydı ve ekran görüntüsü alma teknolojisi. Joygame QA Utility, yüksek kaliteli ekran kayıtları için NVIDIA ShadowPlay'i kullanabilir.

### NvidiaShadowPlayHelper
Joygame QA Utility'de NVIDIA ShadowPlay ile etkileşim sağlayan sınıf.

## O

### OAuth
Kullanıcı kimlik bilgilerini paylaşmadan üçüncü taraf uygulamalara yetki vermek için kullanılan açık standart. Joygame QA Utility, Google API'lerine erişim için OAuth kullanır.

## P

### Package Name
Bir Android uygulamasını benzersiz şekilde tanımlayan kimlik (örn. com.joygame.app).

### Platform Tools
Android SDK'nın bir parçası olan ve ADB gibi araçları içeren bileşen. Joygame QA Utility, `platform-tools` klasöründe bu araçları içerir.

## Q

### QA (Quality Assurance)
Yazılım kalitesini sağlamak için kullanılan süreçler ve yöntemler. Joygame QA Utility, QA ekiplerinin mobil uygulamaları test etmesine yardımcı olmak için tasarlanmıştır.

## R

### Redmine
Proje yönetimi ve hata takibi için açık kaynaklı bir web uygulaması. Joygame QA Utility, hata raporlarını Redmine'a göndermek için entegrasyon sağlar.

### REST API
REpresentational State Transfer mimarisini kullanan bir API türü. Joygame QA Utility, TestRail ve Redmine gibi servislerle iletişim kurmak için REST API'ler kullanır.

## S

### Screenshot
Ekran görüntüsü. Joygame QA Utility, mobil cihazlardan ekran görüntüleri almak için kullanılabilir.

### Screenrecord
Ekran kaydı. Joygame QA Utility, mobil cihazlardan ekran kayıtları almak için kullanılabilir.

### SDK (Software Development Kit)
Belirli bir platform veya teknoloji için yazılım geliştirme araçları seti.

## T

### TestRail
Test durumlarını, test planlarını ve test sonuçlarını yönetmek için kullanılan bir test yönetim aracı. Joygame QA Utility, TestRail entegrasyonu sağlar.

### TestRailClient
Joygame QA Utility'de TestRail API'sine erişim sağlayan sınıf.

### Tailwind CSS
CSS framework'ü. Joygame QA Utility'nin web arayüzü Tailwind CSS kullanır.

## U

### UDID (Unique Device Identifier)
iOS cihazlar için benzersiz tanımlayıcı.

### USB Debugging
Android cihazlarda geliştirme ve hata ayıklama için kullanılan bir mod. ADB ile iletişim kurmak için etkinleştirilmesi gerekir.

## W

### WebView2
Microsoft Edge (Chromium) tabanlı web içeriğini Windows uygulamalarına entegre etmek için kullanılan bir kontrol. Joygame QA Utility, modern web arayüzünü göstermek için WebView2 kullanır.

### WebMessage
WebView2 ve ana uygulama arasında mesaj alışverişi için kullanılan mekanizma.

## X

### XML (eXtensible Markup Language)
Verileri yapılandırılmış bir formatta depolamak ve taşımak için kullanılan bir işaretleme dili. Joygame QA Utility, yapılandırma dosyaları için XML kullanır.

---

**Not**: Bu terimler sözlüğü, Joygame QA Utility uygulamasında ve dokümantasyonunda kullanılan teknik terimleri açıklar. Eksik veya yanlış bilgiler için geliştirme ekibiyle iletişime geçin.