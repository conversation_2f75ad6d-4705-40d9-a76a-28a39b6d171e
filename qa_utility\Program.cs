﻿namespace QA_Utility
{
    internal static class Program
    {
        /// <summary>
        ///  The main entry point for the application.
        /// </summary>
        /// 
        static Mutex mutex = new Mutex(true, "{MyApp-GUID}2");
        [STAThread]
        static void Main()
        {
            // To customize application configuration such as set high DPI settings or default font,
            // see https://aka.ms/applicationconfiguration.


            if (mutex.WaitOne(TimeSpan.Zero, true))
            {
                Application.EnableVisualStyles();
                Application.SetCompatibleTextRenderingDefault(false);
                //Application.Run(new Form2());
                Application.Run(new Form1());
                // Mutex serbest býrakýlýr.
                mutex.ReleaseMutex();
            }
            else
            {
                MessageBox.Show("Uygulama zaten çalışıyor!", "Uyarı", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
            }
        }
    }
}