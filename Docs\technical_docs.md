# Joygame QA Utility - Teknik Dokümantasyon

## İçindekiler

1. [<PERSON><PERSON><PERSON>](#1-mimari-genel-bak<PERSON><PERSON>)
2. [<PERSON><PERSON>](#2-proje-yap<PERSON><PERSON><PERSON>)
3. [<PERSON><PERSON><PERSON><PERSON><PERSON>](#3-tek<PERSON>loji-yığını)
4. [<PERSON><PERSON><PERSON>z <PERSON>](#4-a<PERSON><PERSON><PERSON>-mimar<PERSON>)
5. [Veri Modelleri](#5-veri-modelleri)
6. [API Entegrasyonları](#6-api-entegrasyonları)
7. [<PERSON>bil Cihaz İletişimi](#7-mobil-cihaz-iletişimi)
8. [<PERSON><PERSON><PERSON><PERSON><PERSON>](#8-gü<PERSON><PERSON><PERSON>-siste<PERSON>)
9. [<PERSON><PERSON><PERSON>lik](#9-güvenlik)
10. [Performans Optimizasyonu](#10-performans-optimizasyonu)
11. [Hata Ayıklama ve Loglama](#11-hata-ay<PERSON>klama-ve-loglama)
12. [<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ve <PERSON>](#12-dağıtım-ve-kurulum)

## 1. <PERSON><PERSON><PERSON>l <PERSON>kış

Joygame QA Utility, .NET Framework tabanlı bir Windows Forms uygulaması olup, modern web teknolojileri ile entegre edilmiş hibrit bir mimariye sahiptir. Uygulama, iki ana bileşenden oluşur:

1. **QA Utility**: Ana uygulama, kullanıcı arayüzü ve temel işlevleri içerir.
2. **AutoUpdater**: Bağımsız bir uygulama olarak çalışan güncelleme bileşeni.

### 1.1 Mimari Diyagram

```
+---------------------+      +---------------------+
|                     |      |                     |
|    QA Utility       |<---->|    AutoUpdater      |
|    (Ana Uygulama)   |      |    (Güncelleme)     |
|                     |      |                     |
+----------+----------+      +---------------------+
           |
           v
+----------+----------+
|                     |
|    WebView2         |
|    (Web Arayüzü)    |
|                     |
+----------+----------+
           |
           v
+----------+----------+      +---------------------+
|                     |      |                     |
|    Harici Servisler |<---->|    Mobil Cihazlar   |
|    (API Bağlantıları)|      |    (ADB/iOS Araçları)|
|                     |      |                     |
+---------------------+      +---------------------+
```

## 2. Proje Yapısı

### 2.1 Klasör Yapısı

```
joygame_utility/
├── .gitattributes
├── .gitignore
├── AutoUpdater/
│   ├── AutoUpdater.csproj
│   ├── Form1.cs
│   ├── Program.cs
│   ├── Properties/
│   ├── app.config
│   └── log4net.config
├── Docs/
│   ├── prd.md
│   ├── user_guide.md
│   └── technical_docs.md
├── LICENSE
├── QA Utility.sln
├── README.md
└── qa_utility/
    ├── ApkUploaderForm.cs
    ├── CustomInputBox.cs
    ├── Form1.cs
    ├── Program.cs
    ├── Properties/
    ├── QA Utility.csproj
    ├── Resources/
    │   ├── arriving.mp3
    │   ├── contextMenuHelper.js
    │   ├── index.html
    │   ├── scripts.js
    │   ├── styles.css
    │   └── testrail.js
    ├── classes/
    │   ├── GoogleApiService.cs
    │   ├── LdapAuthenticator.cs
    │   ├── NvidiaShadowPlayHelper.cs
    │   ├── TestRail/
    │   ├── downloader.cs
    │   ├── icard/
    │   ├── medianovaapi.cs
    │   └── redmine.cs
    ├── ioslog/
    └── platform-tools/
```

### 2.2 Bileşen Açıklamaları

- **AutoUpdater/**: Otomatik güncelleme işlevlerini içeren bağımsız uygulama.
- **qa_utility/**: Ana uygulama kodları ve kaynakları.
  - **Resources/**: Web arayüzü dosyaları (HTML, CSS, JavaScript).
  - **classes/**: Yardımcı sınıflar ve API entegrasyonları.
  - **ioslog/**: iOS cihazlarla iletişim için araçlar.
  - **platform-tools/**: Android cihazlarla iletişim için ADB araçları.

## 3. Teknoloji Yığını

### 3.1 Backend

- **.NET Framework**: Windows Forms uygulaması için temel çerçeve.
- **C#**: Ana programlama dili.
- **log4net**: Loglama kütüphanesi.
- **MySQL.Data**: MySQL veritabanı bağlantısı için.

### 3.2 Frontend

- **WebView2**: Modern web içeriğini görüntülemek için kullanılan bileşen.
- **HTML5/CSS3/JavaScript**: Web arayüzü için kullanılan teknolojiler.
- **Tailwind CSS**: CSS framework'ü.
- **Font Awesome**: İkon kütüphanesi.

### 3.3 Mobil Araçlar

- **ADB (Android Debug Bridge)**: Android cihazlarla iletişim için.
- **libimobiledevice**: iOS cihazlarla iletişim için.

### 3.4 Harici Kütüphaneler

- **Redmine.Net.Api**: Redmine entegrasyonu için.
- **Google.Protobuf**: Google API entegrasyonu için.
- **Microsoft.Web.WebView2**: WebView2 kontrolü için.

## 4. Arayüz Mimarisi

### 4.1 WebView2 Entegrasyonu

Uygulama, modern bir kullanıcı deneyimi sunmak için WebView2 kontrolünü kullanır. Bu, HTML, CSS ve JavaScript ile oluşturulmuş web tabanlı bir arayüzün Windows Forms uygulaması içinde çalışmasını sağlar.

```csharp
private async void InitializeWebView2()
{
    webView = new WebView2();
    webView.Dock = DockStyle.Fill;
    this.Controls.Add(webView);
    
    await webView.EnsureCoreWebView2Async(null);
    
    // JavaScript'ten C# koduna mesaj gönderme olayını dinle
    webView.WebMessageReceived += WebView_WebMessageReceived;
    
    // HTML dosyasını yükle
    webView.CoreWebView2.Navigate(Path.Combine(Application.StartupPath, "Resources", "index.html"));
}
```

### 4.2 JavaScript-C# İletişimi

Web arayüzü ve C# kodu arasındaki iletişim, WebView2'nin sağladığı mesajlaşma mekanizması ile gerçekleştirilir:

- **C# → JavaScript**: `webView.CoreWebView2.PostWebMessageAsJson()` metodu kullanılır.
- **JavaScript → C#**: `window.chrome.webview.postMessage()` metodu kullanılır ve `WebMessageReceived` olayı ile dinlenir.

```csharp
private async void WebView_WebMessageReceived(object sender, CoreWebView2WebMessageReceivedEventArgs e)
{
    var jsonMessage = JsonSerializer.Deserialize<JsonElement>(e.WebMessageAsJson);
    string messageType = jsonMessage.GetProperty("type").GetString();
    
    switch (messageType)
    {
        case "launchApp":
            await LaunchApplication();
            break;
        // Diğer mesaj tipleri...
    }
}
```

## 5. Veri Modelleri

### 5.1 Temel Veri Yapıları

```csharp
// Cihaz bilgisi için yardımcı sınıf
private class DeviceInfo
{
    public string Brand { get; set; }
    public string Model { get; set; }
    public string Version { get; set; }
    public string Chipset { get; set; }
    public string Cpu { get; set; }
    public string Resolution { get; set; }
    public string Language { get; set; }
}
```

### 5.2 Veritabanı Şeması

Uygulama, MySQL veritabanını kullanarak çeşitli verileri saklar:

- **Oyun Bilgileri**: Oyun adı, paket adı, versiyon bilgileri.
- **Test Logları**: Test sonuçları ve loglar.
- **Kullanıcı Bilgileri**: Kullanıcı kimlik bilgileri ve tercihleri.

## 6. API Entegrasyonları

### 6.1 TestRail API

TestRail ile entegrasyon, test senaryolarını ve sonuçlarını senkronize etmek için kullanılır:

```csharp
public class TestRailClient
{
    private readonly string _apiUrl;
    private readonly string _username;
    private readonly string _password;
    
    public async Task<List<TestCase>> GetTestCasesAsync(int projectId)
    {
        // TestRail API'sine istek gönderme kodu
    }
    
    public async Task AddTestResultAsync(int testId, int statusId, string comment)
    {
        // Test sonuçlarını gönderme kodu
    }
}
```

### 6.2 Redmine API

Redmine entegrasyonu, hata takibi ve proje yönetimi için kullanılır:

```csharp
public class redmine
{
    private readonly RedmineManager _manager;
    
    public redmine(Form1 form)
    {
        // Redmine bağlantısı kurma kodu
    }
    
    public async Task<Issue> CreateIssueAsync(string subject, string description)
    {
        // Yeni sorun oluşturma kodu
    }
}
```

### 6.3 Google API

Google servisleriyle entegrasyon, ödeme işlemleri ve diğer Google hizmetlerine erişim için kullanılır:

```csharp
public class GoogleApiService
{
    private readonly string _credentialPath;
    
    public GoogleApiService(string credentialPath)
    {
        _credentialPath = credentialPath;
    }
    
    public async Task<List<PaymentInfo>> GetPaymentsAsync(string packageName)
    {
        // Google Play ödeme bilgilerini alma kodu
    }
}
```

## 7. Mobil Cihaz İletişimi

### 7.1 Android Cihaz İletişimi

Android cihazlarla iletişim, ADB (Android Debug Bridge) aracılığıyla gerçekleştirilir:

```csharp
private string ExecuteAdbCommand(string command)
{
    Process process = new Process();
    process.StartInfo.FileName = Path.Combine(Application.StartupPath, "platform-tools", "adb.exe");
    process.StartInfo.Arguments = command;
    process.StartInfo.UseShellExecute = false;
    process.StartInfo.RedirectStandardOutput = true;
    process.StartInfo.CreateNoWindow = true;
    
    process.Start();
    string output = process.StandardOutput.ReadToEnd();
    process.WaitForExit();
    
    return output;
}
```

### 7.2 iOS Cihaz İletişimi

iOS cihazlarla iletişim, libimobiledevice araçları kullanılarak gerçekleştirilir:

```csharp
private string ExecuteIosCommand(string command, string arguments)
{
    Process process = new Process();
    process.StartInfo.FileName = Path.Combine(Application.StartupPath, "ioslog", command);
    process.StartInfo.Arguments = arguments;
    process.StartInfo.UseShellExecute = false;
    process.StartInfo.RedirectStandardOutput = true;
    process.StartInfo.CreateNoWindow = true;
    
    process.Start();
    string output = process.StandardOutput.ReadToEnd();
    process.WaitForExit();
    
    return output;
}
```

## 8. Güncelleme Sistemi

### 8.1 AutoUpdater Bileşeni

AutoUpdater, ana uygulamanın güncel kalmasını sağlayan bağımsız bir uygulamadır:

```csharp
public partial class Form1 : Form
{
    private readonly DataTable haslist = new();
    private readonly MySqlConnection coon;
    
    public Form1()
    {
        InitializeComponent();
        XmlConfigurator.Configure(new FileInfo("log4net.config"));
        logger.Info("Uygulama başlatıldı.");
    }
    
    private void CheckForUpdates()
    {
        // FTP sunucusundan dosya hash değerlerini kontrol etme
        // Değişen dosyaları indirme
        // Ana uygulamayı güncelleme
    }
}
```

### 8.2 Dosya Hash Kontrolü

Güncelleme sistemi, dosyaların hash değerlerini karşılaştırarak hangi dosyaların değiştiğini tespit eder:

```csharp
private string CalculateFileHash(string filePath)
{
    using (var md5 = MD5.Create())
    using (var stream = File.OpenRead(filePath))
    {
        byte[] hash = md5.ComputeHash(stream);
        return BitConverter.ToString(hash).Replace("-", "").ToLowerInvariant();
    }
}
```

## 9. Güvenlik

### 9.1 LDAP Kimlik Doğrulama

Kullanıcı kimlik doğrulaması, LDAP protokolü kullanılarak gerçekleştirilir:

```csharp
public class LdapAuthenticator
{
    private readonly string _ldapServer;
    private readonly int _ldapPort;
    
    public bool Authenticate(string username, string password)
    {
        // LDAP kimlik doğrulama kodu
    }
}
```

### 9.2 Yapılandırma Güvenliği

Hassas yapılandırma bilgileri (API anahtarları, şifreler) güvenli bir şekilde saklanır:

```csharp
// app.config dosyasında şifrelenmiş bölümler
<configuration>
  <connectionStrings configProtectionProvider="RsaProtectedConfigurationProvider">
    <EncryptedData>...</EncryptedData>
  </connectionStrings>
</configuration>
```

## 10. Performans Optimizasyonu

### 10.1 Asenkron İşlemler

Uygulama, kullanıcı arayüzünün donmasını önlemek için asenkron programlama tekniklerini kullanır:

```csharp
private async Task<string> GetDeviceInfoAsync()
{
    return await Task.Run(() => {
        return ExecuteAdbCommand("shell getprop");
    });
}
```

### 10.2 Kaynak Yönetimi

Uygulama, sistem kaynaklarını verimli kullanmak için çeşitli optimizasyonlar içerir:

- **Lazy Loading**: Gerektiğinde yükleme yapılır.
- **Dispose Pattern**: Kaynakların düzgün şekilde serbest bırakılması sağlanır.
- **Caching**: Sık kullanılan veriler önbelleğe alınır.

## 11. Hata Ayıklama ve Loglama

### 11.1 log4net Yapılandırması

Uygulama, log4net kütüphanesini kullanarak detaylı loglama yapar:

```xml
<!-- log4net.config -->
<log4net>
  <appender name="FileAppender" type="log4net.Appender.RollingFileAppender">
    <file value="logs/app.log" />
    <appendToFile value="true" />
    <rollingStyle value="Size" />
    <maxSizeRollBackups value="5" />
    <maximumFileSize value="10MB" />
    <staticLogFileName value="true" />
    <layout type="log4net.Layout.PatternLayout">
      <conversionPattern value="%date [%thread] %-5level %logger - %message%newline" />
    </layout>
  </appender>
  <root>
    <level value="INFO" />
    <appender-ref ref="FileAppender" />
  </root>
</log4net>
```

### 11.2 Hata Yakalama

Uygulama, hataları yakalamak ve loglamak için try-catch blokları kullanır:

```csharp
try
{
    // İşlem kodu
}
catch (Exception ex)
{
    logger.Error($"Hata oluştu: {ex.Message}", ex);
    MessageBox.Show($"İşlem sırasında bir hata oluştu: {ex.Message}", "Hata", MessageBoxButtons.OK, MessageBoxIcon.Error);
}
```

## 12. Dağıtım ve Kurulum

### 12.1 Derleme Süreci

Uygulama, Visual Studio kullanılarak derlenir:

1. Release yapılandırması seçilir.
2. Proje derlenir.
3. Çıktı dosyaları paketlenir.

### 12.2 Kurulum Paketi

Kurulum paketi, aşağıdaki bileşenleri içerir:

- Ana uygulama dosyaları
- AutoUpdater bileşeni
- Gerekli DLL'ler ve bağımlılıklar
- Yapılandırma dosyaları
- Mobil cihaz araçları (ADB, iOS araçları)

### 12.3 Sistem Gereksinimleri

- Windows 10 veya üzeri
- .NET Framework (uygun sürüm)
- WebView2 Runtime
- 4 GB RAM (minimum)
- 500 MB disk alanı

---

**Not**: Bu teknik dokümantasyon, Joygame QA Utility'nin temel teknik özelliklerini kapsar. Daha detaylı bilgi için kaynak kodunu inceleyebilirsiniz.