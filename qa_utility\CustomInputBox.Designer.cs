﻿namespace QA_Utility
{
    partial class CustomInputBox
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            lblPrompt = new Label();
            btnOK = new Button();
            btnCancel = new Button();
            txtOyunAdi = new TextBox();
            label1 = new Label();
            txtVersion = new TextBox();
            label2 = new Label();
            txtbuildNumber = new TextBox();
            label3 = new Label();
            osCombo = new ComboBox();
            SuspendLayout();
            // 
            // lblPrompt
            // 
            lblPrompt.AutoSize = true;
            lblPrompt.Location = new Point(12, 9);
            lblPrompt.Name = "lblPrompt";
            lblPrompt.Size = new Size(57, 15);
            lblPrompt.TabIndex = 0;
            lblPrompt.Text = "Oyun Adı";
            // 
            // btnOK
            // 
            btnOK.Location = new Point(263, 26);
            btnOK.Name = "btnOK";
            btnOK.Size = new Size(75, 42);
            btnOK.TabIndex = 1;
            btnOK.Text = "Tamam";
            btnOK.UseVisualStyleBackColor = true;
            btnOK.Click += btnOK_Click;
            // 
            // btnCancel
            // 
            btnCancel.Location = new Point(263, 97);
            btnCancel.Name = "btnCancel";
            btnCancel.Size = new Size(75, 41);
            btnCancel.TabIndex = 2;
            btnCancel.Text = "İptal";
            btnCancel.UseVisualStyleBackColor = true;
            btnCancel.Click += btnCancel_Click;
            // 
            // txtOyunAdi
            // 
            txtOyunAdi.Location = new Point(12, 27);
            txtOyunAdi.Name = "txtOyunAdi";
            txtOyunAdi.Size = new Size(245, 23);
            txtOyunAdi.TabIndex = 3;
            // 
            // label1
            // 
            label1.AutoSize = true;
            label1.Location = new Point(12, 53);
            label1.Name = "label1";
            label1.Size = new Size(22, 15);
            label1.TabIndex = 4;
            label1.Text = "OS";
            // 
            // txtVersion
            // 
            txtVersion.Location = new Point(12, 115);
            txtVersion.Name = "txtVersion";
            txtVersion.Size = new Size(108, 23);
            txtVersion.TabIndex = 7;
            // 
            // label2
            // 
            label2.AutoSize = true;
            label2.Location = new Point(12, 97);
            label2.Name = "label2";
            label2.Size = new Size(51, 15);
            label2.TabIndex = 6;
            label2.Text = "Versiyon";
            // 
            // txtbuildNumber
            // 
            txtbuildNumber.Location = new Point(149, 115);
            txtbuildNumber.Name = "txtbuildNumber";
            txtbuildNumber.Size = new Size(108, 23);
            txtbuildNumber.TabIndex = 8;
            // 
            // label3
            // 
            label3.AutoSize = true;
            label3.Location = new Point(149, 97);
            label3.Name = "label3";
            label3.Size = new Size(88, 15);
            label3.TabIndex = 9;
            label3.Text = "Build Numarası";
            // 
            // osCombo
            // 
            osCombo.DropDownStyle = ComboBoxStyle.DropDownList;
            osCombo.FlatStyle = FlatStyle.System;
            osCombo.FormattingEnabled = true;
            osCombo.Items.AddRange(new object[] { "AOS", "IOS" });
            osCombo.Location = new Point(12, 71);
            osCombo.Name = "osCombo";
            osCombo.Size = new Size(245, 23);
            osCombo.TabIndex = 10;
            // 
            // CustomInputBox
            // 
            AutoScaleDimensions = new SizeF(7F, 15F);
            AutoScaleMode = AutoScaleMode.Font;
            ClientSize = new Size(346, 145);
            Controls.Add(osCombo);
            Controls.Add(label3);
            Controls.Add(txtbuildNumber);
            Controls.Add(txtVersion);
            Controls.Add(label2);
            Controls.Add(label1);
            Controls.Add(txtOyunAdi);
            Controls.Add(btnCancel);
            Controls.Add(btnOK);
            Controls.Add(lblPrompt);
            DoubleBuffered = true;
            FormBorderStyle = FormBorderStyle.FixedSingle;
            Name = "CustomInputBox";
            ShowIcon = false;
            StartPosition = FormStartPosition.CenterScreen;
            TopMost = true;
            Load += CustomInputBox_Load;
            ResumeLayout(false);
            PerformLayout();
        }

        #endregion

        private Label lblPrompt;
        private Button btnOK;
        private Button btnCancel;
        private TextBox txtInput;
        private Label label1;
        private TextBox txtVersion;
        private Label label2;
        private TextBox txtbuildNumber;
        private Label label3;
        private TextBox txtOyunAdi;
        private ComboBox osCombo;
    }
}