using System;
using System.Windows.Forms;


namespace qa_utility.classes.icard
{
    public class IcardJsBridge
    {
        // C# Settings ile kaydedilen iCard kullanıcı adı ve şifresini JS'e döndürür
        public object GetIcardLogin()
        {
            // Settings.Default.IcardUsername ve IcardPassword property'le<PERSON> kullandı<PERSON>ı<PERSON>ızdan emin olun
            var username = QA_Utility.Properties.Settings.Default.IcardUsername ?? string.Empty;
            var password = QA_Utility.Properties.Settings.Default.IcardPassword ?? string.Empty;
            return new { username, password };
        }

        // JS'den çağrılır, C# Settings'e kaydeder
        public void SaveIcardLogin(string username, string password)
        {
            QA_Utility.Properties.Settings.Default.IcardUsername = username;
            QA_Utility.Properties.Settings.Default.IcardPassword = password;
            QA_Utility.Properties.Settings.Default.Save();
        }
    }
}
