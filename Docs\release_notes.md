# Joygame QA Utility - Sürüm Notları

## Sürüm 2.5.0 (15 Haziran 2023)

### Ye<PERSON>

- **TestRail Entegrasyonu İyileştirmeleri**
  - Test koşuları artık otomatik olarak TestRail'e raporlanabilir
  - Test sonuçları için ekran görüntüleri ve videolar otomatik olarak eklenebilir
  - TestRail projelerini ve test planlarını filtreleme seçenekleri eklendi

- **Gelişmiş Log Analizi**
  - Log dosyalarında arama ve filtreleme özellikleri eklendi
  - Özel log şablonları oluşturma ve kaydetme desteği
  - Log verilerini CSV veya Excel formatında dışa aktarma

- **Performans İyileştirmeleri**
  - Uygulama başlatma süresi %30 azaltıldı
  - Büyük log dosyalarının işlenmesi optimize edildi
  - WebView2 bellek kullanımı iyileştirildi

### İyileştirmeler

- WebView2 bileşeni en son sürüme güncellendi
- Kullanıcı arayüzü daha duyarlı hale getirildi
- Mobil cihaz algılama mekanizması iyileştirildi
- APK yükleme işlemi hızlandırıldı
- Veritabanı bağlantı havuzu optimizasyonları

### Hata Düzeltmeleri

- iOS cihazlarda log kaydı bazen duruyordu, düzeltildi
- Bazı Android cihazlarda ekran kaydı başlatılamıyordu, düzeltildi
- TestRail'e büyük ekli dosyalar yüklenirken oluşan hata düzeltildi
- Redmine entegrasyonunda karakter kodlama sorunları giderildi
- Uzun süre çalıştıktan sonra oluşan bellek sızıntısı düzeltildi
- Çoklu ekranlı sistemlerde pencere konumlandırma sorunu düzeltildi

## Sürüm 2.4.2 (3 Mayıs 2023)

### Hata Düzeltmeleri

- AutoUpdater'da FTP bağlantı sorunu düzeltildi
- Android 13 cihazlarda ADB bağlantı sorunu giderildi
- WebView2 içeriği bazen yüklenmiyordu, düzeltildi
- Büyük log dosyaları açılırken uygulama donuyordu, performans iyileştirildi
- Redmine API çağrılarında zaman aşımı sorunu düzeltildi

## Sürüm 2.4.1 (17 Nisan 2023)

### Hata Düzeltmeleri

- Kritik güvenlik açığı giderildi (CVE-2023-XXXXX)
- MySQL bağlantısında oluşan zaman aşımı sorunu düzeltildi
- iOS 16.4 cihazlarda log kaydı sorunu giderildi
- Bazı durumlarda APK yüklemesi tamamlanmıyordu, düzeltildi
- Google API yetkilendirme hatası düzeltildi

## Sürüm 2.4.0 (28 Mart 2023)

### Yeni Özellikler

- **Google Payments Entegrasyonu**
  - Google Play Store ödeme verilerini görüntüleme
  - Satın alma işlemlerini test etme ve doğrulama
  - Abonelik durumlarını izleme

- **Gelişmiş Ekran Kaydı**
  - NVIDIA ShadowPlay entegrasyonu ile yüksek kaliteli ekran kaydı
  - Özel çözünürlük ve kare hızı ayarları
  - Ekran kaydı için zamanlayıcı ve otomatik durdurma

- **Çoklu Cihaz Desteği**
  - Birden fazla Android ve iOS cihazı aynı anda yönetme
  - Cihazlar arasında hızlı geçiş yapma
  - Toplu komut çalıştırma

### İyileştirmeler

- Kullanıcı arayüzü yenilendi ve daha modern bir görünüm kazandı
- Sekme yapısı yeniden düzenlendi ve daha kullanıcı dostu hale getirildi
- ADB komutları optimize edildi ve daha hızlı çalışıyor
- Log dosyaları için otomatik temizleme ve arşivleme
- Veritabanı şeması güncellendi ve performans iyileştirildi

### Hata Düzeltmeleri

- Bazı Android cihazlarda uygulama verilerini temizleme işlemi çalışmıyordu, düzeltildi
- iOS cihazlarda ekran görüntüsü alma bazen başarısız oluyordu, düzeltildi
- TestRail API çağrılarında kimlik doğrulama sorunu giderildi
- WebView2 içeriğinde JavaScript hataları düzeltildi
- Büyük APK dosyalarını yüklerken oluşan bellek sorunu giderildi

## Sürüm 2.3.1 (14 Şubat 2023)

### Hata Düzeltmeleri

- Windows 11 uyumluluk sorunları giderildi
- LDAP kimlik doğrulama hatası düzeltildi
- Redmine API'sinde zaman aşımı sorunu giderildi
- Bazı durumlarda uygulama kapanırken çöküyordu, düzeltildi
- iOS 16.3 cihazlarda bağlantı sorunu giderildi

## Sürüm 2.3.0 (20 Ocak 2023)

### Yeni Özellikler

- **LDAP Kimlik Doğrulama**
  - Şirket kimlik bilgileriyle oturum açma
  - Kullanıcı rollerine göre özellik erişimi
  - Oturum süresi ve güvenlik ayarları

- **Redmine Entegrasyonu**
  - Hata raporlarını doğrudan Redmine'a gönderme
  - Mevcut sorunları görüntüleme ve güncelleme
  - Ekran görüntüleri ve logları otomatik olarak ekleme

- **Otomatik Test Çalıştırma**
  - Temel UI testlerini otomatik çalıştırma
  - Test sonuçlarını raporlama
  - Test senaryolarını kaydetme ve yeniden çalıştırma

### İyileştirmeler

- WebView2 bileşeni güncellendi
- Veritabanı bağlantı güvenliği artırıldı
- APK analiz özellikleri geliştirildi
- Kullanıcı arayüzü performansı iyileştirildi
- Log dosyaları için gelişmiş filtreleme seçenekleri

### Hata Düzeltmeleri

- Android 12 cihazlarda ADB bağlantı sorunu giderildi
- Büyük log dosyalarını açarken oluşan bellek sorunu düzeltildi
- Bazı durumlarda ekran kaydı dosyaları kaydedilmiyordu, düzeltildi
- AutoUpdater'da dosya kilitleme sorunu giderildi
- WebView2 içeriğinde CSS görüntüleme sorunları düzeltildi

## Sürüm 2.2.0 (5 Aralık 2022)

### Yeni Özellikler

- **iOS Desteği İyileştirmeleri**
  - iOS 16 tam destek
  - iOS uygulama yükleme ve kaldırma
  - iOS cihaz loglarını görüntüleme

- **Google Drive Entegrasyonu**
  - Ekran görüntülerini ve videoları otomatik olarak Google Drive'a yükleme
  - Klasör yapısını özelleştirme
  - Paylaşım bağlantıları oluşturma

- **Gelişmiş APK Yönetimi**
  - APK içeriğini analiz etme
  - Manifest dosyasını görüntüleme
  - APK imza bilgilerini kontrol etme

### İyileştirmeler

- Kullanıcı arayüzü tepki süresi iyileştirildi
- ADB komut çalıştırma performansı artırıldı
- Log dosyaları için daha iyi organizasyon
- Bellek kullanımı optimize edildi
- Başlatma süresi kısaltıldı

### Hata Düzeltmeleri

- Bazı Android cihazlarda ekran görüntüsü alma başarısız oluyordu, düzeltildi
- WebView2 içeriğinde JavaScript hataları düzeltildi
- AutoUpdater'da güncelleme kontrolü bazen başarısız oluyordu, düzeltildi
- Veritabanı bağlantısında karakter kodlama sorunu giderildi
- Uzun süre çalıştıktan sonra oluşan bellek sızıntısı düzeltildi

## Sürüm 2.1.0 (15 Ekim 2022)

### Yeni Özellikler

- **TestRail Entegrasyonu**
  - Test durumlarını görüntüleme
  - Test sonuçlarını gönderme
  - Test planlarını yönetme

- **Gelişmiş Log Analizi**
  - Log dosyalarında arama
  - Özel filtreleme seçenekleri
  - Log verilerini dışa aktarma

### İyileştirmeler

- WebView2 bileşeni güncellendi
- Kullanıcı arayüzü iyileştirildi
- ADB komutları optimize edildi
- Veritabanı sorguları iyileştirildi

### Hata Düzeltmeleri

- Android 12 cihazlarda bağlantı sorunu giderildi
- APK yükleme hatası düzeltildi
- WebView2 içeriğinde görüntüleme sorunları giderildi
- AutoUpdater'da dosya indirme hatası düzeltildi

## Sürüm 2.0.0 (1 Eylül 2022)

### Yeni Özellikler

- **Tamamen Yenilenen Arayüz**
  - WebView2 tabanlı modern kullanıcı arayüzü
  - Sekme yapısı ile daha iyi organizasyon
  - Duyarlı tasarım

- **Gelişmiş Mobil Cihaz Yönetimi**
  - Android ve iOS cihaz desteği
  - APK yükleme ve kaldırma
  - Uygulama verilerini temizleme

- **Ekran Kaydı ve Görüntüsü**
  - Yüksek kaliteli ekran kaydı
  - Ekran görüntüsü alma
  - Otomatik dosya adlandırma ve organizasyon

- **AutoUpdater Bileşeni**
  - Otomatik güncelleme kontrolü
  - Arka planda güncelleme indirme
  - Kolay kurulum

### İyileştirmeler

- .NET Framework 4.7.2'ye güncellendi
- Veritabanı yapısı yenilendi
- Log sistemi geliştirildi (log4net)
- Performans optimizasyonları

### Hata Düzeltmeleri

- Çeşitli kararlılık sorunları giderildi
- Bellek sızıntıları düzeltildi
- Kullanıcı arayüzü hataları giderildi

## Sürüm 1.5.0 (10 Haziran 2022)

### Yeni Özellikler

- Android cihaz yönetimi
- APK yükleme desteği
- Basit log görüntüleme

### İyileştirmeler

- Kullanıcı arayüzü güncellemeleri
- Performans iyileştirmeleri

### Hata Düzeltmeleri

- Çeşitli kararlılık sorunları giderildi

## Sürüm 1.0.0 (15 Mart 2022)

### İlk Sürüm

- Temel QA araçları
- Android cihaz algılama
- Basit log görüntüleme
- Ekran görüntüsü alma

---

**Not**: Bu sürüm notları, Joygame QA Utility'nin geliştirme geçmişini göstermektedir. Her sürüm, önceki sürümlerdeki tüm özellikleri ve düzeltmeleri içerir.