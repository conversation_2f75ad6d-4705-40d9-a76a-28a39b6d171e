using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Threading.Tasks;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Configuration;

namespace QA_Utility.classes.TestRail
{
    public class TestRailClient
    {
        private readonly string _baseUrl;
        private readonly string _username;
        private readonly string _password;
        private readonly HttpClient _client;

        public TestRailClient()
        {
            _baseUrl = ConfigurationManager.AppSettings["TestRailBaseUrl"]?.TrimEnd('/') ?? "";
            _username = ConfigurationManager.AppSettings["TestRailUsername"] ?? "";
            _password = ConfigurationManager.AppSettings["TestRailPassword"] ?? "";

            var handler = new HttpClientHandler
            {
                AutomaticDecompression = DecompressionMethods.GZip | DecompressionMethods.Deflate
            };

            _client = new HttpClient(handler);
            
            // Set basic auth header
            var authToken = Encoding.ASCII.GetBytes($"{_username}:{_password}");
            _client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue(
                "Basic", Convert.ToBase64String(authToken));
            
            // Set content type header
            _client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
        }

        /// <summary>
        /// Check if the TestRail connection is valid
        /// </summary>
        public async Task<bool> CheckConnectionAsync()
        {
            try
            {
                // Try to get projects as a simple connection test
                var response = await _client.GetAsync($"{_baseUrl}/index.php?/api/v2/get_projects");
                return response.IsSuccessStatusCode;
            }
            catch
            {
                return false;
            }
        }        /// <summary>
        /// Get all projects from TestRail
        /// </summary>
        public async Task<List<TestRailProject>> GetProjectsAsync()
        {
            try
            {
                var response = await _client.GetAsync($"{_baseUrl}/index.php?/api/v2/get_projects");
                response.EnsureSuccessStatusCode();

                var content = await response.Content.ReadAsStringAsync();
                Console.WriteLine($"Raw API response: {content}");

                return ParseTestRailResponse<List<TestRailProject>>(content);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error getting projects: {ex.Message}");
                return new List<TestRailProject>();
            }
        }        /// <summary>
        /// Get all test suites for a specific project
        /// </summary>
        public async Task<List<TestRailSuite>> GetSuitesAsync(int projectId)
        {
            try
            {
                var response = await _client.GetAsync($"{_baseUrl}/index.php?/api/v2/get_suites/{projectId}");
                response.EnsureSuccessStatusCode();

                var content = await response.Content.ReadAsStringAsync();
                Console.WriteLine($"Raw suites response: {content}");

                return ParseTestRailResponse<List<TestRailSuite>>(content);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error getting suites: {ex.Message}");
                return new List<TestRailSuite>();
            }
        }        /// <summary>
        /// Get all test cases for a specific project and suite
        /// </summary>
        public async Task<List<TestRailCase>> GetCasesAsync(int projectId, int? suiteId = null)
        {
            try
            {
                string url = $"{_baseUrl}/index.php?/api/v2/get_cases/{projectId}";
                if (suiteId.HasValue)
                {
                    url += $"&suite_id={suiteId.Value}";
                }

                var response = await _client.GetAsync(url);
                response.EnsureSuccessStatusCode();

                var content = await response.Content.ReadAsStringAsync();
                Console.WriteLine($"Raw cases response: {content}");

                return ParseTestRailResponse<List<TestRailCase>>(content);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error getting cases: {ex.Message}");
                return new List<TestRailCase>();
            }
        }        /// <summary>
        /// Get a specific test case by ID
        /// </summary>
        public async Task<TestRailCase> GetCaseAsync(int caseId)
        {
            try
            {
                var response = await _client.GetAsync($"{_baseUrl}/index.php?/api/v2/get_case/{caseId}");
                response.EnsureSuccessStatusCode();

                var content = await response.Content.ReadAsStringAsync();
                Console.WriteLine($"Raw case detail response: {content}");

                return ParseTestRailResponse<TestRailCase>(content);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error getting case: {ex.Message}");
                return new TestRailCase();
            }
        }

        /// <summary>
        /// Add a test result for a test case
        /// </summary>
        public async Task<bool> AddResultForCaseAsync(int runId, int caseId, TestRailResult result)
        {
            try
            {
                var json = JsonSerializer.Serialize(result, new JsonSerializerOptions
                {
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                });
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _client.PostAsync(
                    $"{_baseUrl}/index.php?/api/v2/add_result_for_case/{runId}/{caseId}",
                    content);
                
                response.EnsureSuccessStatusCode();
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error adding result: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Get all test runs for a specific project
        /// </summary>
        public async Task<List<TestRailRun>> GetRunsAsync(int projectId)
        {
            try
            {
                var response = await _client.GetAsync($"{_baseUrl}/index.php?/api/v2/get_runs/{projectId}");
                response.EnsureSuccessStatusCode();

                var content = await response.Content.ReadAsStringAsync();
                Console.WriteLine($"Raw runs response: {content}");

                return ParseTestRailResponse<List<TestRailRun>>(content);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error getting runs: {ex.Message}");
                return new List<TestRailRun>();
            }
        }

        /// <summary>
        /// Get a specific test run by ID
        /// </summary>
        public async Task<TestRailRun> GetRunAsync(int runId)
        {
            try
            {
                var response = await _client.GetAsync($"{_baseUrl}/index.php?/api/v2/get_run/{runId}");
                response.EnsureSuccessStatusCode();

                var content = await response.Content.ReadAsStringAsync();
                Console.WriteLine($"Raw run detail response: {content}");

                return ParseTestRailResponse<TestRailRun>(content);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error getting run: {ex.Message}");
                return new TestRailRun();
            }
        }        /// <summary>
        /// Helper method to safely handle parsing of TestRail API responses that could be in different formats
        /// </summary>
        private T ParseTestRailResponse<T>(string json) where T : new()
        {
            if (string.IsNullOrEmpty(json))
            {
                return new T();
            }

            var options = new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            };

            try
            {
                // First try direct deserialization
                return JsonSerializer.Deserialize<T>(json, options);
            }
            catch (JsonException firstEx)
            {
                Console.WriteLine($"Direct deserialization failed: {firstEx.Message}");
                
                try
                {
                    // If it's a list type
                    if (typeof(T).IsGenericType && typeof(T).GetGenericTypeDefinition() == typeof(List<>))
                    {
                        Type itemType = typeof(T).GetGenericArguments()[0];
                        
                        // Try using our wrapper class
                        var wrapperType = typeof(TestRailResponseWrapper<>).MakeGenericType(itemType);
                        var wrapper = JsonSerializer.Deserialize(json, wrapperType, options);
                        
                        // Get the appropriate property based on item type
                        var propertyName = GetResponsePropertyNameForType(itemType);
                        if (!string.IsNullOrEmpty(propertyName))
                        {
                            var property = wrapperType.GetProperty(propertyName);
                            if (property != null)
                            {
                                var value = property.GetValue(wrapper);
                                if (value != null)
                                {
                                    return (T)value;
                                }
                            }
                        }
                        
                        // If no specific property matches, try finding any array property
                        using JsonDocument doc = JsonDocument.Parse(json);
                        if (doc.RootElement.ValueKind == JsonValueKind.Object)
                        {
                            foreach (var property in doc.RootElement.EnumerateObject())
                            {
                                if (property.Value.ValueKind == JsonValueKind.Array)
                                {
                                    var arrayJson = property.Value.GetRawText();
                                    return JsonSerializer.Deserialize<T>(arrayJson, options);
                                }
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Advanced parsing failed: {ex.Message}");
                }

                // Return default if all parsing attempts fail
                return new T();
            }
        }
        
        /// <summary>
        /// Returns property name in the wrapper based on the type
        /// </summary>
        private string GetResponsePropertyNameForType(Type type)
        {
            if (type == typeof(TestRailProject))
                return "Projects";
            if (type == typeof(TestRailSuite))
                return "Suites";
            if (type == typeof(TestRailCase))
                return "Cases";
            if (type == typeof(TestRailRun))
                return "Runs";
                
            return string.Empty;
        }
    }

    public class TestRailProject
    {
        [JsonPropertyName("id")]
        public int Id { get; set; }

        [JsonPropertyName("name")]
        public string Name { get; set; } = string.Empty;

        [JsonPropertyName("announcement")]
        public string Announcement { get; set; } = string.Empty;

        [JsonPropertyName("show_announcement")]
        public bool ShowAnnouncement { get; set; }

        [JsonPropertyName("is_completed")]
        public bool IsCompleted { get; set; }

        [JsonPropertyName("completed_on")]
        public DateTime? CompletedOn { get; set; }

        [JsonPropertyName("url")]
        public string Url { get; set; } = string.Empty;
    }

    public class TestRailSuite
    {
        [JsonPropertyName("id")]
        public int Id { get; set; }

        [JsonPropertyName("name")]
        public string Name { get; set; } = string.Empty;

        [JsonPropertyName("description")]
        public string Description { get; set; } = string.Empty;

        [JsonPropertyName("project_id")]
        public int ProjectId { get; set; }

        [JsonPropertyName("url")]
        public string Url { get; set; } = string.Empty;
    }

    public class TestRailCase
    {
        [JsonPropertyName("id")]
        public int Id { get; set; }

        [JsonPropertyName("title")]
        public string Title { get; set; } = string.Empty;

        [JsonPropertyName("section_id")]
        public int? SectionId { get; set; }

        [JsonPropertyName("template_id")]
        public int TemplateId { get; set; }

        [JsonPropertyName("type_id")]
        public int? TypeId { get; set; }

        [JsonPropertyName("priority_id")]
        public int? PriorityId { get; set; }

        [JsonPropertyName("milestone_id")]
        public int? MilestoneId { get; set; }

        [JsonPropertyName("refs")]
        public string Refs { get; set; } = string.Empty;

        [JsonPropertyName("suite_id")]
        public int SuiteId { get; set; }

        [JsonPropertyName("custom_preconds")]
        public string Preconditions { get; set; } = string.Empty;

        [JsonPropertyName("custom_steps")]
        public string Steps { get; set; } = string.Empty;

        [JsonPropertyName("custom_expected")]
        public string ExpectedResult { get; set; } = string.Empty;

        [JsonPropertyName("created_by")]
        public int? CreatedBy { get; set; }

        [JsonPropertyName("created_on")]
        public int? CreatedOn { get; set; }

        [JsonPropertyName("updated_by")]
        public int? UpdatedBy { get; set; }

        [JsonPropertyName("updated_on")]
        public int? UpdatedOn { get; set; }

        [JsonPropertyName("estimate")]
        public string Estimate { get; set; } = string.Empty;

        [JsonPropertyName("estimate_forecast")]
        public string EstimateForecast { get; set; } = string.Empty;
    }

    public class TestRailResult
    {
        [JsonPropertyName("status_id")]
        public int StatusId { get; set; }

        [JsonPropertyName("comment")]
        public string Comment { get; set; } = string.Empty;

        [JsonPropertyName("version")]
        public string Version { get; set; } = string.Empty;

        [JsonPropertyName("elapsed")]
        public string Elapsed { get; set; } = string.Empty;

        [JsonPropertyName("defects")]
        public string Defects { get; set; } = string.Empty;

        [JsonPropertyName("assignedto_id")]
        public int? AssignedToId { get; set; }
    }

    public class TestRailRun
    {
        [JsonPropertyName("id")]
        public int Id { get; set; }

        [JsonPropertyName("name")]
        public string Name { get; set; } = string.Empty;

        [JsonPropertyName("description")]
        public string Description { get; set; } = string.Empty;

        [JsonPropertyName("suite_id")]
        public int SuiteId { get; set; }

        [JsonPropertyName("milestone_id")]
        public int? MilestoneId { get; set; }

        [JsonPropertyName("assignedto_id")]
        public int? AssignedToId { get; set; }

        [JsonPropertyName("include_all")]
        public bool IncludeAll { get; set; }

        [JsonPropertyName("is_completed")]
        public bool IsCompleted { get; set; }

        [JsonPropertyName("completed_on")]
        public int? CompletedOn { get; set; }

        [JsonPropertyName("passed_count")]
        public int? PassedCount { get; set; }

        [JsonPropertyName("blocked_count")]
        public int? BlockedCount { get; set; }

        [JsonPropertyName("untested_count")]
        public int? UntestedCount { get; set; }

        [JsonPropertyName("retest_count")]
        public int? RetestCount { get; set; }

        [JsonPropertyName("failed_count")]
        public int? FailedCount { get; set; }

        [JsonPropertyName("project_id")]
        public int ProjectId { get; set; }

        [JsonPropertyName("plan_id")]
        public int? PlanId { get; set; }

        [JsonPropertyName("created_on")]
        public int? CreatedOn { get; set; }

        [JsonPropertyName("created_by")]
        public int? CreatedBy { get; set; }

        [JsonPropertyName("url")]
        public string Url { get; set; } = string.Empty;
    }

    public static class TestRailStatuses
    {
        public const int Passed = 1;
        public const int Blocked = 2;
        public const int Untested = 3;
        public const int Retest = 4;
        public const int Failed = 5;
    }
}
