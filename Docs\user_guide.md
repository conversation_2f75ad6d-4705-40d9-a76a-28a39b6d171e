# Joygame QA Utility - Kullanıcı Kılavuzu

## İçindekiler

1. [<PERSON><PERSON><PERSON>](#1-g<PERSON><PERSON>)
2. [<PERSON><PERSON><PERSON>](#2-kuru<PERSON>)
3. [<PERSON><PERSON><PERSON><PERSON><PERSON>](#3-b<PERSON><PERSON><PERSON><PERSON><PERSON>)
4. [<PERSON>](#4-an<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>)
5. [<PERSON>bil Cihaz İşlemleri](#5-mobil-cihaz-işlemleri)
6. [Uygulama Testleri](#6-uygulama-testleri)
7. [<PERSON><PERSON><PERSON> Görüntüsü ve Video](#7-ekran-görüntüsü-ve-video)
8. [Log Yönetimi](#8-log-yönetimi)
9. [TestRail Entegrasyonu](#9-testrail-entegrasyonu)
10. [Redmine Entegrasyonu](#10-redmine-entegrasyonu)
11. [Google Ödemeleri](#11-google-ödemeleri)
12. [<PERSON><PERSON>](#12-sorun-giderme)

## 1. Giriş

<PERSON>game QA Utility, oyun test süreçlerini kolaylaştırmak ve hızlandırmak için tasarlanmış kapsamlı bir araç setidir. Bu kılavuz, uygulamanın tüm özelliklerini ve nasıl kullanılacağını detaylı bir şekilde açıklar.

## 2. Kurulum

### 2.1 Sistem Gereksinimleri

- Windows 10 veya üzeri işletim sistemi
- .NET Framework (uygun sürüm için .csproj dosyalarını kontrol edin)
- WebView2 Runtime
- Android ve iOS cihazlar için sürücüler

### 2.2 Kurulum Adımları

1. Projeyi GitHub'dan klonlayın:
   ```
   git clone https://github.com/joygame/joygame_utility.git
   ```

2. Visual Studio ile `QA Utility.sln` dosyasını açın.

3. Gerekli NuGet paketlerini yükleyin.

4. Uygulamayı derleyin ve çalıştırın.

### 2.3 İlk Çalıştırma

Uygulama ilk kez çalıştırıldığında, gerekli yapılandırma ayarlarını yapmanız istenebilir. Aşağıdaki bilgileri hazır bulundurun:

- LDAP kimlik bilgileri
- TestRail API erişim bilgileri
- Redmine API erişim bilgileri
- FTP sunucu bilgileri

## 3. Başlangıç

### 3.1 Giriş Yapma

1. Uygulamayı başlatın.
2. Kullanıcı adı ve şifrenizi girin.
3. "Beni Hatırla" seçeneğini işaretleyerek bir sonraki girişte otomatik olarak giriş yapabilirsiniz.
4. "Giriş Yap" düğmesine tıklayın.

### 3.2 Ana Ekran

Giriş yaptıktan sonra, ana ekran görüntülenir. Bu ekranda:

- Üst kısımda oyun ve işletim sistemi seçimi
- Ortada işlem sekmeleri
- Alt kısımda durum bilgileri

## 4. Ana Arayüz

### 4.1 Üst Panel

- **OS Seçimi**: Android veya iOS platformunu seçin.
- **Oyun Seçimi**: Test edilecek oyunu seçin.
- **Her Zaman Üstte**: Uygulamanın diğer pencerelerin üzerinde kalmasını sağlar.

### 4.2 Sekmeler

- **İşlemler**: Temel cihaz ve uygulama işlemleri
- **QA Task**: Test görevleri yönetimi
- **Video ve SS Verileri**: Ekran görüntüleri ve video kayıtları
- **Google Ödemeleri**: Ödeme işlemleri takibi
- **Oyun Verileri**: Oyun bilgileri ve istatistikleri
- **Yerel Loglar**: Kaydedilen logların görüntülenmesi
- **Task Oluşturucu**: Yeni test görevleri oluşturma
- **TestRail**: TestRail entegrasyonu
- **iCard**: iCard entegrasyonu

## 5. Mobil Cihaz İşlemleri

### 5.1 Cihaz Bağlantısı

#### Android Cihazlar

1. USB kablosu ile cihazı bilgisayara bağlayın.
2. USB hata ayıklama modunu etkinleştirin.
3. Uygulama otomatik olarak cihazı algılayacaktır.

#### iOS Cihazlar

1. USB kablosu ile cihazı bilgisayara bağlayın.
2. Gerekli sürücülerin yüklü olduğundan emin olun.
3. Uygulama otomatik olarak cihazı algılayacaktır.

### 5.2 Cihaz Bilgileri

Bağlı cihazın aşağıdaki bilgileri görüntülenir:

- Marka ve Model
- İşletim Sistemi Sürümü
- Ekran Çözünürlüğü
- Dil Ayarları
- Donanım Özellikleri

### 5.3 Cihaz Kontrolü

- **Yeniden Başlat**: Cihazı yeniden başlatır.
- **Metin Gönder**: Cihaza metin gönderir.
- **Dosya Transferi**: Cihaz ve bilgisayar arasında dosya transferi yapar.

## 6. Uygulama Testleri

### 6.1 APK Yükleme

1. "APK Yükle" düğmesine tıklayın.
2. APK dosyasını sürükleyip bırakın veya dosya seçiciyi kullanarak seçin.
3. "Yükle" düğmesine tıklayarak APK'yı cihaza yükleyin.

### 6.2 Uygulama İşlemleri

- **Uygulamayı Başlat**: Seçili uygulamayı başlatır.
- **Uygulamayı Kapat**: Çalışan uygulamayı kapatır.
- **Mağazada Görüntüle**: Uygulamayı uygulama mağazasında açar.
- **Uygulamayı Kaldır**: Uygulamayı cihazdan kaldırır.
- **Uygulama Verilerini Temizle**: Uygulama verilerini siler.

### 6.3 Uygulama Bilgileri

- **Versiyon**: Uygulama sürümü
- **Build Numarası**: Yapı numarası
- **Paket Adı**: Uygulama paket adı
- **Hedef SDK**: Hedef Android SDK sürümü

## 7. Ekran Görüntüsü ve Video

### 7.1 Ekran Görüntüsü Alma

1. "Ekran Görüntüsü Al" düğmesine tıklayın.
2. Ekran görüntüsü otomatik olarak kaydedilir ve "Video ve SS Verileri" sekmesinde görüntülenir.

### 7.2 Video Kaydı

1. "Video Kaydı Başlat" düğmesine tıklayın.
2. Kayıt başladığında düğme rengi değişir.
3. Kaydı durdurmak için tekrar düğmeye tıklayın.
4. Video otomatik olarak kaydedilir ve "Video ve SS Verileri" sekmesinde görüntülenir.

### 7.3 Son 30 Saniye Kaydı

NVIDIA ShadowPlay entegrasyonu ile:

1. "Son 30 Saniyeyi Kaydet" düğmesine tıklayın.
2. Son 30 saniyenin kaydı otomatik olarak oluşturulur.

## 8. Log Yönetimi

### 8.1 Log Kaydı Başlatma

1. "Log Kaydı Başlat" düğmesine tıklayın.
2. Log notu girin (isteğe bağlı).
3. Kayıt başladığında düğme rengi değişir.
4. Kaydı durdurmak için tekrar düğmeye tıklayın.

### 8.2 Logları Görüntüleme

1. "Yerel Loglar" sekmesine geçin.
2. Kaydedilen loglar listede görüntülenir.
3. Bir log seçerek içeriğini görüntüleyebilirsiniz.

### 8.3 Log Analizi

- **Filtreleme**: Belirli anahtar kelimelere göre logları filtreleyin.
- **Dışa Aktarma**: Logları metin dosyası olarak dışa aktarın.
- **Paylaşma**: Logları e-posta veya diğer uygulamalar aracılığıyla paylaşın.

## 9. TestRail Entegrasyonu

### 9.1 TestRail Bağlantısı

1. "TestRail" sekmesine geçin.
2. API anahtarınızı ve TestRail URL'nizi girin.
3. "Bağlan" düğmesine tıklayın.

### 9.2 Test Senaryoları

- **Test Senaryolarını Görüntüleme**: Mevcut test senaryolarını listeleyin.
- **Test Çalıştırma**: Yeni bir test çalıştırması başlatın.
- **Sonuçları Güncelleme**: Test sonuçlarını TestRail'e gönderin.

## 10. Redmine Entegrasyonu

### 10.1 Redmine Bağlantısı

1. Redmine API anahtarınızı ve URL'nizi yapılandırın.
2. Uygulama otomatik olarak Redmine ile bağlantı kurar.

### 10.2 Hata Raporlama

1. "Hata Raporu Oluştur" düğmesine tıklayın.
2. Hata detaylarını girin.
3. Ekran görüntüsü veya video ekleyin (isteğe bağlı).
4. "Gönder" düğmesine tıklayarak Redmine'a gönderin.

## 11. Google Ödemeleri

### 11.1 Ödeme İşlemleri

1. "Google Ödemeleri" sekmesine geçin.
2. Ödeme işlemlerini görüntüleyin ve analiz edin.

### 11.2 Otomatik İade

1. "Otomatik İade" özelliğini etkinleştirin.
2. İade süresini ayarlayın.
3. Sistem otomatik olarak test ödemelerini iade edecektir.

## 12. Sorun Giderme

### 12.1 Genel Sorunlar

#### Uygulama Başlatma Sorunları

- **Sorun**: Uygulama başlatılamıyor.
- **Çözüm**: .NET Framework'ün doğru sürümünün yüklü olduğundan emin olun.

#### Cihaz Bağlantı Sorunları

- **Sorun**: Cihaz algılanmıyor.
- **Çözüm**: USB kablosunu kontrol edin ve USB hata ayıklama modunun etkin olduğundan emin olun.

#### APK Yükleme Sorunları

- **Sorun**: APK yüklenemiyor.
- **Çözüm**: APK dosyasının geçerli olduğundan ve cihazın yeterli depolama alanına sahip olduğundan emin olun.

### 12.2 Log Dosyaları

Sorun giderme için log dosyalarını kontrol edin:

1. Uygulama klasöründeki "logs" dizinine gidin.
2. En son log dosyasını açın ve hata mesajlarını inceleyin.

### 12.3 Yardım Alma

Daha fazla yardım için:

- Dahili dokümantasyonu kontrol edin.
- GitHub deposundaki sorun takipçisini kullanın.
- Geliştirme ekibiyle iletişime geçin.

---

**Not**: Bu kullanıcı kılavuzu, Joygame QA Utility'nin temel özelliklerini kapsar. Daha spesifik işlevler için ilgili dokümantasyona başvurun.