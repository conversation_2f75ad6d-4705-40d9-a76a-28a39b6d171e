name: CI

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  build:
    runs-on: windows-latest

    steps:
    - uses: actions/checkout@v3

    - name: Setup MSBuild
      uses: microsoft/setup-msbuild@v1.1

    - name: Setup NuGet
      uses: NuGet/setup-nuget@v1.0.5

    - name: Setup .NET Framework
      uses: actions/setup-dotnet@v3
      with:
        dotnet-version: '6.0.x'

    - name: Restore NuGet packages
      run: nuget restore "QA Utility.sln"

    - name: Build solution
      run: msbuild "QA Utility.sln" /p:Configuration=Release /p:Platform="Any CPU"

    - name: Run tests
      run: dotnet test --no-build --verbosity normal

  code-quality:
    runs-on: windows-latest

    steps:
    - uses: actions/checkout@v3

    - name: Setup .NET Framework
      uses: actions/setup-dotnet@v3
      with:
        dotnet-version: '6.0.x'

    - name: Install SonarCloud scanner
      run: dotnet tool install --global dotnet-sonarscanner

    - name: Begin SonarCloud analysis
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
      run: dotnet sonarscanner begin /k:"joygame_qa-utility" /o:"joygame" /d:sonar.login="${{ secrets.SONAR_TOKEN }}" /d:sonar.host.url="https://sonarcloud.io"

    - name: Setup MSBuild
      uses: microsoft/setup-msbuild@v1.1

    - name: Setup NuGet
      uses: NuGet/setup-nuget@v1.0.5

    - name: Restore NuGet packages
      run: nuget restore "QA Utility.sln"

    - name: Build solution
      run: msbuild "QA Utility.sln" /p:Configuration=Release /p:Platform="Any CPU"

    - name: End SonarCloud analysis
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
      run: dotnet sonarscanner end /d:sonar.login="${{ secrets.SONAR_TOKEN }}"

  codeql-analysis:
    runs-on: windows-latest
    permissions:
      security-events: write

    steps:
    - uses: actions/checkout@v3

    - name: Initialize CodeQL
      uses: github/codeql-action/init@v2
      with:
        languages: csharp, javascript

    - name: Setup MSBuild
      uses: microsoft/setup-msbuild@v1.1

    - name: Setup NuGet
      uses: NuGet/setup-nuget@v1.0.5

    - name: Restore NuGet packages
      run: nuget restore "QA Utility.sln"

    - name: Build solution
      run: msbuild "QA Utility.sln" /p:Configuration=Release /p:Platform="Any CPU"

    - name: Perform CodeQL Analysis
      uses: github/codeql-action/analyze@v2