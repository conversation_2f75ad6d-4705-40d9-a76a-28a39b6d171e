# Güvenlik Politikası

## Desteklenen Sürümler

Aşağıdaki Joygame QA Utility sürümleri şu anda güvenlik güncellemeleri ile desteklenmektedir:

| Sürüm | Destekleniyor |
| ----- | ------------- |
| 2.5.x | :white_check_mark: |
| 2.4.x | :white_check_mark: |
| 2.3.x | :white_check_mark: |
| 2.2.x | :x: |
| 2.1.x | :x: |
| 2.0.x | :x: |
| < 2.0 | :x: |

## Güvenlik Açığı Bildirme

Joygame QA Utility'de bir güvenlik açığı bulduysanız, lütfen aşağıdaki adımları izleyin:

1. **Açığı hemen kamuya açıklamayın**. Güvenlik açıklarının sorumlu bir şekilde açı<PERSON>nması, tüm kullanıcıların korunmasına yardımcı olur.

2. **Güvenlik açığını e-posta ile bildirin**. Güvenlik açığını [<EMAIL>](mailto:<EMAIL>) adresine e-posta göndererek bildirin. E-postanızda şunları belirtin:
   - Güvenlik açığının türü ve açıklaması
   - Açığın nasıl yeniden oluşturulabileceği (adım adım talimatlar)
   - Açığın potansiyel etkisi
   - Varsa, açığı gidermek için önerilen çözümler

3. **Yanıt bekleyin**. Güvenlik ekibimiz bildiriminizi inceleyecek ve size 48 saat içinde yanıt verecektir. Yanıtımızda, sonraki adımlar ve zaman çizelgesi hakkında bilgi verilecektir.

## Güvenlik Açığı Açıklama Politikası

Güvenlik açığı bildirildiğinde, aşağıdaki süreci izleriz:

1. Bildirimi aldığımızı doğrularız ve açığı değerlendiririz.
2. Açığı doğrular ve etkisini belirleriz.
3. Açığı gidermek için bir düzeltme geliştiririz.
4. Düzeltmeyi test eder ve desteklenen tüm sürümlere uygularız.
5. Düzeltmeyi içeren bir güvenlik güncellemesi yayınlarız.
6. Güvenlik açığını ve düzeltmeyi, kullanıcıların güncellemeleri uygulayabilmesi için makul bir süre geçtikten sonra kamuya açıklarız.

## Güvenlik En İyi Uygulamaları

Joygame QA Utility'yi güvenli bir şekilde kullanmak için aşağıdaki en iyi uygulamaları öneririz:

1. **Her zaman en son sürümü kullanın**. Güvenlik güncellemeleri düzenli olarak yayınlanır ve en son sürümü kullanmak, bilinen güvenlik açıklarına karşı korunmanıza yardımcı olur.

2. **Güçlü kimlik doğrulama kullanın**. LDAP kimlik doğrulaması kullanırken güçlü parolalar seçin ve mümkünse iki faktörlü kimlik doğrulamayı etkinleştirin.

3. **API anahtarlarını güvende tutun**. TestRail, Redmine ve Google API anahtarlarınızı güvende tutun ve düzenli olarak değiştirin. API anahtarlarını asla kaynak koduna dahil etmeyin veya halka açık depolarda paylaşmayın.

4. **Uygulama yapılandırmasını güvende tutun**. `App.config` dosyasındaki veritabanı bağlantı dizesi ve diğer hassas bilgileri koruyun. Üretim ortamlarında, bu bilgileri şifrelenmiş bir şekilde saklayın.

5. **Düzenli güncellemeler yapın**. Güvenlik güncellemeleri ve yamalar yayınlandığında, bunları hemen uygulayın.

6. **Mobil cihazları güvende tutun**. Test cihazlarında USB hata ayıklama modunu yalnızca gerektiğinde etkinleştirin ve test tamamlandığında devre dışı bırakın.

## Güvenlik Güncellemeleri

Güvenlik güncellemeleri, aşağıdaki kanallar aracılığıyla duyurulur:

- Uygulama içi bildirimler (AutoUpdater aracılığıyla)
- [Joygame QA Utility GitHub Deposu](https://github.com/joygame/qa-utility/security/advisories)
- Kayıtlı kullanıcılara e-posta bildirimleri

## İletişim

Güvenlikle ilgili sorularınız veya endişeleriniz için lütfen [<EMAIL>](mailto:<EMAIL>) adresine e-posta gönderin.