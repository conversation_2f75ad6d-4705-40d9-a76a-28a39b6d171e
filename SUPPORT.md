# Joygame QA Utility Destek

Joygame QA Utility ile ilgili sorunlarınız veya sorularınız için aşağıdaki destek kanallarını kullanabilirsiniz.

## Dokümantasyon

Sorunuzu yanıtlamak için öncelikle dokümantasyonumuza başvurmanızı öneririz:

- [Kullanım Kılavuzu](./Docs/user_guide.md)
- [Teknik Dokümantasyon](./Docs/technical_docs.md)
- [Kurulum Kılavuzu](./Docs/installation_guide.md)
- [Sorun Giderme Kılavuzu](./Docs/troubleshooting_guide.md)
- [Sıkça Sorulan Sorular (SSS)](./Docs/faq.md)

## Sorun Bildirme

Bir hata bulduysanız veya yeni bir özellik önermek istiyorsanız, lütfen GitHub üzerinden bir sorun oluşturun:

1. [<PERSON><PERSON><PERSON> sayfasına](https://github.com/joygame/qa-utility/issues) gidin
2. "New Issue" (Yeni Sorun) düğmesine tıklayın
3. Uygun şablonu seçin (Hata Raporu veya Özellik İsteği)
4. Şablonu doldurun ve gönderin

Sorun bildirirken, lütfen aşağıdaki bilgileri sağlamaya çalışın:

- Joygame QA Utility sürümü
- İşletim sistemi ve sürümü
- Sorunun yeniden oluşturulması için adımlar
- Beklenen davranış ve gerçek davranış
- İlgili log dosyaları veya ekran görüntüleri

## E-posta Desteği

Acil sorunlar veya özel sorular için, e-posta desteği de sağlıyoruz:

- Genel sorular: [<EMAIL>](mailto:<EMAIL>)
- Teknik destek: [<EMAIL>](mailto:<EMAIL>)
- Güvenlik sorunları: [<EMAIL>](mailto:<EMAIL>)

E-posta gönderirken, lütfen aşağıdaki bilgileri dahil edin:

- Konu satırında kısa bir sorun açıklaması
- Joygame QA Utility sürümü
- İşletim sistemi ve sürümü
- Sorunun ayrıntılı açıklaması
- Daha önce denediğiniz çözümler

## Topluluk Desteği

Joygame QA Utility topluluğundan destek almak için aşağıdaki kanalları kullanabilirsiniz:

- [GitHub Tartışmaları](https://github.com/joygame/qa-utility/discussions)
- [Joygame Discord Sunucusu](https://discord.gg/joygame)

## Ticari Destek

Kurumsal müşteriler için özel destek seçenekleri sunuyoruz. Daha fazla bilgi için lütfen [<EMAIL>](mailto:<EMAIL>) adresine e-posta gönderin.

## Yanıt Süreleri

- GitHub Sorunları: 2-3 iş günü
- E-posta Desteği: 1-2 iş günü
- Güvenlik Sorunları: 24 saat içinde
- Topluluk Kanalları: Değişken (topluluk üyelerinin müsaitliğine bağlı)

## Destek Politikası

Joygame QA Utility için destek, aşağıdaki politikalara tabidir:

- Yalnızca son iki ana sürüm (şu anda 2.4.x ve 2.5.x) için tam destek sağlanmaktadır
- Eski sürümler için sınırlı destek veya destek yok
- Değiştirilmiş veya özelleştirilmiş kurulumlar için sınırlı destek

Destek ekibimiz, sorununuzu çözmek için elinden geleni yapacaktır, ancak tüm sorunların çözülebileceğini garanti edemeyiz.