﻿namespace QA_Utility
{
    public partial class CustomInputBox : Form
    {
        public CustomInputBox()
        {
            InitializeComponent();
        }


        private void CustomInputBox_Load(object sender, EventArgs e)
        {

        }
        public string gameName { get; private set; }
        public string os { get; private set; }
        public string version { get; private set; }
        public string buildNumber { get; private set; }

        public CustomInputBox(string title, string gameName, string OS, string version = "0", string buildNUmber = "0")
        {
            InitializeComponent();

            this.Text = title;
            txtOyunAdi.Text = gameName;
            osCombo.Text = OS;
            this.AcceptButton = btnOK;
            this.CancelButton = btnCancel;
            txtVersion.Text = version;
            txtbuildNumber.Text = buildNUmber;
        }
        bool returnEdebilirMyim = false;

        private bool txtChecker(string txt, string type)
        {
            if (string.IsNullOrWhiteSpace(txt))
            {
                MessageBox.Show(type + " değeri geçersiz");
                return false;
            }
            return true;
        }
        private void btnOK_Click(object sender, EventArgs e)
        {
            gameName = txtOyunAdi.Text;
            if (gameName == "!Seçilmedi")
            {
                MessageBox.Show("Geçerli oyun adı girin");
                return;
            }
            os = osCombo.Text;
            version = txtVersion.Text;
            buildNumber = txtbuildNumber.Text;
            returnEdebilirMyim = txtChecker(gameName, "Oyun Adı");
            if (!returnEdebilirMyim) { return; }
            returnEdebilirMyim = txtChecker(os, "İşletim Sistemi");
            if (!returnEdebilirMyim) { return; }
            returnEdebilirMyim = txtChecker(version, "Versiyon");
            if (!returnEdebilirMyim) { return; }
            returnEdebilirMyim = txtChecker(buildNumber, "Build Number");
            if (!returnEdebilirMyim) { return; }
            this.DialogResult = DialogResult.OK;
            this.Close();
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }
    }

}
