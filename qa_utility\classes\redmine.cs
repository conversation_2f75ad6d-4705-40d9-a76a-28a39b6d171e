﻿using log4net.Repository.Hierarchy;
using Redmine.Net.Api;
using Redmine.Net.Api.Async;
using Redmine.Net.Api.Exceptions;
using Redmine.Net.Api.Types;
using System.Collections.Specialized;
using System.IO;
using System.Net;

namespace QA_Utility.classes
{

    internal class redmine
    {
        readonly string host = "https://project.joygame.com/";
        readonly string login = "qa-api";
        readonly string password = "uE7xI3qQ5gF4hA8a";
        readonly string apiKey = "9b97c4dd8eaa7e1f85e950b4c8dd8276185be659";
        static RedmineManager? manager;
        bool connectionStatus = false;
        int currentUserId;
        private Form1? _form;  // Form1 instance to use for notifications
        public class CustomIdentifiableName : IdentifiableName
        {
            public void SetId(int id)
            {
                typeof(IdentifiableName).GetProperty("Id").SetValue(this, id);
            }
        }
        public redmine(Form1? form = null)
        {
            _form = form;
        }

        public void createTask(string gamename, string taskName, int projectID)
        {
            try
            {
                // Check if game name is valid
                if (string.IsNullOrEmpty(gamename) || gamename.Contains("Seçilmedi"))
                {
                    MessageBox.Show("Lütfen geçerli bir oyun seçin!", "Uyarı", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                var localmanager = new RedmineManager(host, Properties.Settings.Default.redmineid, Properties.Settings.Default.redminepw);  //RedmineManager(host, apiKey);

                var projects = localmanager.GetObjects<Project>(new NameValueCollection());
                var project = projects.OfType<IdentifiableName>()
                                       .Where(p => p.Id.Equals(projectID))
                                       .FirstOrDefault();
                if (project != null)
                {
                    //var parameters = new NameValueCollection { { "name", "abdulkadir.aktas" } };
                    //var users = localmanager.GetObjects<User>();

                    //if (users != null && users.Any())
                    if (true)
                    {
                        //var foundUser = users.FirstOrDefault(u => u.Login.Equals("abdulkadir.aktas"));
                        //var foundUser2 = users.FirstOrDefault(u => u.Login.Equals(Properties.Settings.Default.testername.ToString()));
                        //localmanager.GetCurrentUser().Id
                        var foundUser2 = localmanager.GetCurrentUser().Id; //localmanager.GetObject<User>(Properties.Settings.Default.testername.ToString(), new NameValueCollection { { "include", "memberships" } });
                        if (foundUser2 != null)
                        {
                            // Oyun ismi custom field'ını ekle (ID: 5)
                            var customFields = new List<IssueCustomField>();

                            try
                            {
                                // Redmine API'den custom field'ları al // şimdilik redmine connection kaldırıldı doğrudan id verildi.

                                //var redmineTempManager = tempConnectionAsync();

                                //var allCustomFields = redmineTempManager.Result.GetObjects<CustomField>();
                                //var gameNameCustomField = allCustomFields.FirstOrDefault(cf => cf.Name == "Game Name");

                                //if (gameNameCustomField != null)
                                //{
                                // Custom field ID'sini al
                                    int customFieldId = 30; //gameNameCustomField.Id;

                                    // IssueCustomField oluştur
                                    var issueCustomField = new IssueCustomField
                                    {
                                        Name = "Oyun ismi",
                                        Values = new List<CustomFieldValue>
                                        {
                                            new CustomFieldValue { Info = gamename }
                                        }
                                    };

                                    // Reflection ile ID'yi ayarla
                                    typeof(IdentifiableName).GetProperty("Id").SetValue(issueCustomField, customFieldId);

                                    // Listeye ekle
                                    customFields.Add(issueCustomField);

                                    Form1.logger.Debug($"Oyun ismi custom field oluşturuldu: {gamename} (ID: {customFieldId})");
                                //}
                                //else
                                //{
                                //    // Custom field bulunamadıysa sabit ID kullan
                                //    var customField = new CustomIdentifiableName();
                                //    customField.SetId(5);

                                //    var issueCustomField = new IssueCustomField
                                //    {
                                //        Name = "Oyun ismi",
                                //        Values = new List<CustomFieldValue>
                                //        {
                                //            new CustomFieldValue { Info = gamename }
                                //        }
                                //    };

                                //    // Reflection ile ID'yi ayarla
                                //    typeof(IdentifiableName).GetProperty("Id").SetValue(issueCustomField, 5);

                                //    // Listeye ekle
                                //    customFields.Add(issueCustomField);

                                //    Form1.logger.Debug($"Oyun ismi custom field oluşturuldu: {gamename} (ID: 5)");
                                //}
                            }
                            catch (Exception ex)
                            {
                                Form1.logger.Error($"Custom field oluşturma hatası: {ex.Message}");

                                // Hata durumunda basit bir custom field oluştur
                                customFields.Add(new IssueCustomField
                                {
                                    Name = "Oyun ismi",
                                    Values = new List<CustomFieldValue>
                                    {
                                        new CustomFieldValue { Info = gamename }
                                    }
                                });
                            }

                            Issue issue = new Issue
                            {
                                Project = project,
                                Subject = taskName,
                                Description = taskName + " için QA api tarafından oluşturulan taskıdır.",
                                AssignedTo = IdentifiableName.Create<IdentifiableName>(10),
                                StartDate = DateTime.Now,
                                Author = IdentifiableName.Create<IdentifiableName>(localmanager.GetCurrentUser().Id),
                                Watchers = new List<Watcher>()
                                {
                                    IdentifiableName.Create<Watcher>(localmanager.GetCurrentUser().Id)
                                },
                                CustomFields = customFields
                            };

                            try
                            {
                                Issue savedIssue = localmanager.CreateObject(issue);
                                // html'e bildirim gönder
                                _form?.notificationSendToHtml("Task başarıyla oluşturuldu.");
                            }
                            catch (RedmineException ex)
                            {
                                Form1.logger.Error($"RedmineException: {ex.Message}");
                                MessageBox.Show($"Task oluşturulurken hata: {ex.Message}", "Hata", MessageBoxButtons.OK, MessageBoxIcon.Error);
                            }
                            catch (WebException webEx)
                            {
                                string responseMessage = "Bilinmeyen hata";
                                if (webEx.Response != null)
                                {
                                    using (var stream = webEx.Response.GetResponseStream())
                                    using (var reader = new StreamReader(stream))
                                    {
                                        responseMessage = reader.ReadToEnd();
                                    }
                                }
                                Form1.logger.Error($"WebException: {responseMessage}");
                                MessageBox.Show($"Task oluşturulurken hata: {responseMessage}", "Hata", MessageBoxButtons.OK, MessageBoxIcon.Error);
                            }
                        }
                        else
                        {
                            MessageBox.Show("Kullanıcı bilgisi alınamadı.", "Hata", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        }
                    }
                    else
                    {
                        MessageBox.Show("Kullanıcı bulunamadı.", "Hata", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
                else
                {
                    MessageBox.Show($"Proje ID {projectID} bulunamadı.", "Hata", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                Form1.logger.Error($"Task oluşturma hatası: {ex.Message}");
                MessageBox.Show($"Task oluşturulurken beklenmeyen hata: {ex.Message}", "Hata", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        public async Task<bool> connectionAsync(string login, string password, bool apiskey = false)
        {
            if (apiskey)
            {
                manager = new RedmineManager(host, apiKey);
            }else{
                manager = new RedmineManager(host, login, password);
            }
            // redmine'a bağlan.
            // redmine'a bağlanıp bağlanmadığını kontrol et.

            var timeoutTask = Task.Delay(5000); // 5 saniye timeout
            var currentUser = manager.GetCurrentUserAsync();
            var completedTask = await Task.WhenAny(currentUser, timeoutTask);
            if (completedTask.Status == TaskStatus.Faulted)
            {
                connectionStatus = false;
            }
            else
            {
                Console.WriteLine("Redmine'a bağlanıldı.");
                connectionStatus = true;
                currentUserId = currentUser.Result.Id;
            }
            return connectionStatus;
        }

        // tempConneciton function
        public Task<RedmineManager> tempConnectionAsync()
        {
            var manager = new RedmineManager(host, apiKey);
            return Task.FromResult(manager);
        }

        public int getCurrentUserId()
        {
            return currentUserId;
        }

        public bool getConnectionStatus()
        {
            return connectionStatus;
        }
        // tüm projeleri getir.
        public async Task<List<Project>?> getAllProjectsAsync()
        {
            // redmine'a bağlı değilsek iptal et
            if (!connectionStatus)
            {
                Console.WriteLine("Redmine'a bağlı değilsiniz.");
                return null;
            }
            var projects = await manager.GetObjectsAsync<Project>(null);
            return projects;
        }

        public async Task<List<User>?> getUserFromAllUsersAsync(string userName, RedmineManager? tempManager = null)
        {
            // Hangi manager'ı kullanacağımızı belirle
            RedmineManager currentManager = tempManager ?? manager;

            // redmine'a bağlı değilsek ve tempManager yoksa iptal et
            if (!connectionStatus && tempManager == null)
            {
                Console.WriteLine("Redmine'a bağlı değilsiniz.");
                return null;
            }

            try
            {
                // username'e göre kullanıcıyı getir.
                var parameters = new NameValueCollection { { "name", userName } };
                var users = await currentManager.GetObjectsAsync<User>(parameters);
                return users;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"getUserFromAllUsersAsync hata: {ex.Message}");
                Form1.logger.Error($"getUserFromAllUsersAsync hata: {ex.Message}");
                return null;
            }
        }

        // kullanıcıya atanmı veya izleyici olarak eklenmiş tüm issue'ları getir.
        public async Task<List<Issue>?> getAllIssuesAsync(string userName, RedmineManager? tempManager = null)
        {
            // Hangi manager'ı kullanacağımızı belirle
            RedmineManager currentManager = tempManager ?? manager;

            // redmine'a bağlı değilsek ve tempManager yoksa iptal et
            if (!connectionStatus && tempManager == null)
            {
                Console.WriteLine("Redmine'a bağlı değilsiniz.");
                return null;
            }

            try
            {
                // redmine'da kullanıcıyı userName'e göre bul.
                // userid getir
                var parameters = new NameValueCollection { { "name", userName } };
                var users = await currentManager.GetObjectsAsync<User>(parameters);

                if (users == null || users.Count == 0)
                {
                    Console.WriteLine("Kullanıcı bulunamadı.");
                    return null;
                }

                // userid'ye göre izleyici eklenmiş veya atanmış issue'ları getir.
                var watcherIssues = await currentManager.GetObjectsAsync<Issue>(new NameValueCollection { { "watcher_id", users[0].Id.ToString() } });
                var assignedIssues = await currentManager.GetObjectsAsync<Issue>(new NameValueCollection { { "assigned_to_id", users[0].Id.ToString() } });

                // issuelar null ve idler unique olacak şekilde birleştir.
                var issues = watcherIssues ?? new List<Issue>();

                if (assignedIssues != null)
                {
                    foreach (var item in assignedIssues)
                    {
                        if (!issues.Contains(item))
                        {
                            issues.Add(item);
                        }
                    }
                }
                return issues;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"getAllIssuesAsync hata: {ex.Message}");
                Form1.logger.Error($"getAllIssuesAsync hata: {ex.Message}");
                return null;
            }
        }

        // kullanıcı grubuna atanmış tüm işleri getir.

        public async Task<List<Issue>?> getAllIssuesForGroupsNameAsync(string groupName, RedmineManager? tempManager = null)
        {
            // Hangi manager'ı kullanacağımızı belirle
            RedmineManager currentManager = tempManager ?? manager;

            // redmine'a bağlı değilsek ve tempManager yoksa iptal et
            if (!connectionStatus && tempManager == null)
            {
                Console.WriteLine("Redmine'a bağlı değilsiniz.");
                return null;
            }

            try
            {
                // redmine'da kullanıcı grubunu groupName'e göre bul.
                // groupid getir
                var group = await getGroupFromAllGroupsAsync(groupName, tempManager);
                if (group == null)
                {
                    Console.WriteLine("Kullanıcı grubu bulunamadı.");
                    return null;
                }
                // groupid'ye göre izleyici eklenmiş veya atanmış issue'ları getir.
                var assignedIssues = await currentManager.GetObjectsAsync<Issue>(new NameValueCollection { { "assigned_to_id", group[0].Id.ToString() } });
                return assignedIssues;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"getAllIssuesForGroupsNameAsync hata: {ex.Message}");
                Form1.logger.Error($"getAllIssuesForGroupsNameAsync hata: {ex.Message}");
                return null;
            }
        }

        private async Task<List<Group>?> getGroupFromAllGroupsAsync(string groupName, RedmineManager? tempManager = null)
        {
            // Hangi manager'ı kullanacağımızı belirle
            RedmineManager currentManager = tempManager ?? manager;

            // redmine'a bağlı değilsek ve tempManager yoksa iptal et
            if (!connectionStatus && tempManager == null)
            {
                Console.WriteLine("Redmine'a bağlı değilsiniz.");
                return null;
            }

            try
            {
                // tüm grupları bul ve içerisinde groupName olanı getir
                var groups = await currentManager.GetObjectsAsync<Group>(null);
                var group = groups.Where(g => g.Name == groupName).ToList();
                return group;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"getGroupFromAllGroupsAsync hata: {ex.Message}");
                Form1.logger.Error($"getGroupFromAllGroupsAsync hata: {ex.Message}");
                return null;
            }
        }

        // gerCustomFieldFromProject
        public async Task<List<CustomField>?> getCustomFieldFromProjectAsync(int projectId)
        {
            // redmine'a bağlı değilsek iptal et
            if (!connectionStatus)
            {
                Console.WriteLine("Redmine'a bağlı değilsiniz.");
                return null;
            }
            // proje id'sine göre custom field'ları getir.
            var customFields = await manager.GetObjectsAsync<CustomField>(new NameValueCollection { { "project_id", projectId.ToString() } });
            return customFields;
        }

        internal void updateIssue(string? id, string columnName, string? value)
        {
            // sütun ismi Subject ,Status , StartDate, DueDate, DoneRatio değilse iptal et.
            //if (columnName != "Subject" && columnName != "Status" && columnName != "StartDate" && columnName != "DueDate" && columnName != "DoneRatio" && columnName != "Author" && columnName != "AssignedTo" && columnName != "Platform")
            //{
            //    return;
            //}

            // id'si verilen issue'ın columnName'ine value'sunu yaz.
            if (id == null || columnName == null || value == null)
            {
                return;
            }
            var issue = manager?.GetObject<Issue>(id, new NameValueCollection { { "include", "relations,children,attachments,journals,watchers" } });
            // columun bir custom field mi?

            var issuCustomFields = manager?.GetObjects<CustomField>();
            var customField = issuCustomFields?.Where(p => p.Name == columnName).ToList();
            bool columuniscustomField = false;
            if (customField?.Count > 0)
            {
                columuniscustomField = true;
            }

            // value tarih bilgisi ise datetime'a çevir
            if (columnName == "DueDate" || columnName == "StartDate")
            {
                // eğer startdate duedate'den büyükse önce startdate'i sonra duedate'i güncelle.
                if (columnName == "StartDate")
                {
                    DateTime startDate = DateTime.Parse(value);
                    if (startDate > issue?.DueDate)
                    {
                        issue.DueDate = startDate;
                        manager?.UpdateObject(id, issue);
                    }
                }

                DateTime date = DateTime.Parse(value);
                issue?.GetType().GetProperty(columnName).SetValue(issue, date);
            }
            // value eğer DoneRatio ise system.single tipinde olduğu için convert et.
            else if (columnName == "DoneRatio")
            {
                issue?.GetType().GetProperty(columnName).SetValue(issue, Convert.ToSingle(value));
            }
            //System.ArgumentException: 'Object of type 'System.String' cannot be converted to type 'Redmine.Net.Api.Types.IdentifiableName'.'
            else if (columnName == "Author")
            {
                return;
            }
            else if (columnName == "AssignedTo")
            {

            }
            else if (columnName == "Status")
            {
                var statuses = manager?.GetObjects<IssueStatus>();
                var status = statuses?.Where(s => s.Name == value).ToList();
                issue?.GetType().GetProperty(columnName).SetValue(issue, status[0]);
            }
            else if (columuniscustomField)
            {
                // platform adına sahip custom field'ın id'sini al
                var customFieldId = customField[0].Id;
                // customField[0].PossibleValues bu varsa
                if (customField[0].PossibleValues != null)
                {
                    // value değeri custom field'ın name'idir. Bana custom field'ın value'sunu ver.
                    var cCustomField = customField[0].PossibleValues.Where(p => p.Label == value);
                    // issue'ın custom field'ına value'sunu yaz.
                    issue.CustomFields.Where(c => c.Id == customFieldId).ToList()[0].Values[0].Info = cCustomField.First().Value;
                }
                else
                {
                    issue.CustomFields.Where(c => c.Id == customFieldId).ToList()[0].Values[0].Info = value;
                }
            }
            else
            {
                issue?.GetType().GetProperty(columnName).SetValue(issue, value);
            }
            manager?.UpdateObject(id, issue);
        }

        // issue id'sine göre atanılabilir kullanıcıları getir.
        public List<User>? getAssignableUsers(int id)
        {
            // redmine'a bağlı değilsek iptal et
            if (!connectionStatus)
            {
                Console.WriteLine("Redmine'a bağlı değilsiniz.");
                return null;
            }
            // issue id'sine göre atanılabilir kullanıcıları getir.
            var users = manager?.GetObjects<User>(new NameValueCollection { { "assignable", "*" } });
            return users;
        }

        // Tüm durumları getir
        public async Task<List<IssueStatus>?> getStatusesAsync()
        {
            // redmine'a bağlı değilsek iptal et
            if (!connectionStatus)
            {
                Console.WriteLine("Redmine'a bağlı değilsiniz.");
                return null;
            }
            var statuses = await manager.GetObjectsAsync<IssueStatus>(null);
            return statuses;
        }

        // Tüm öncelikleri getir
        public async Task<List<IssuePriority>?> getPrioritiesAsync()
        {
            // redmine'a bağlı değilsek iptal et
            if (!connectionStatus)
            {
                Console.WriteLine("Redmine'a bağlı değilsiniz.");
                return null;
            }
            var priorities = await manager.GetObjectsAsync<IssuePriority>(null);
            return priorities;
        }

        // Tüm kullanıcıları getir
        public async Task<List<User>?> getUsersAsync()
        {
            // redmine'a bağlı değilsek iptal et
            if (!connectionStatus)
            {
                Console.WriteLine("Redmine'a bağlı değilsiniz.");
                return null;
            }
            var users = await manager.GetObjectsAsync<User>(null);
            return users;
        }

        // Task'i güncelle
        public async Task updateIssueAsync(string taskId, Dictionary<string, string> updates)
        {
            // redmine'a bağlı değilsek iptal et
            if (!connectionStatus)
            {
                Console.WriteLine("Redmine'a bağlı değilsiniz.");
                return;
            }

            var issue = manager?.GetObject<Issue>(taskId, new NameValueCollection { { "include", "relations,children,attachments,journals,watchers" } });
            if (issue == null) return;

            foreach (var update in updates)
            {
                string columnName = update.Key;
                string value = update.Value;

                // Özel durumları kontrol et
                if (columnName == "Subject")
                {
                    issue.Subject = value;
                }
                else if (columnName == "Description")
                {
                    issue.Description = value;
                }
                else if (columnName == "Status")
                {
                    var statuses = await getStatusesAsync();
                    var status = statuses?.FirstOrDefault(s => s.Id.ToString() == value);
                    if (status != null)
                    {
                        issue.Status = status;
                    }
                }
                else if (columnName == "Priority")
                {
                    var priorities = await getPrioritiesAsync();
                    var priority = priorities?.FirstOrDefault(p => p.Id.ToString() == value);
                    if (priority != null)
                    {
                        issue.Priority = priority;
                    }
                }
                else if (columnName == "AssignedTo")
                {
                    var users = await getUsersAsync();
                    var user = users?.FirstOrDefault(u => u.Id.ToString() == value);
                    if (user != null)
                    {
                        issue.AssignedTo = IdentifiableName.Create<IdentifiableName>(user.Id);
                    }
                }
                else if (columnName == "DoneRatio")
                {
                    if (int.TryParse(value, out int doneRatio))
                    {
                        issue.DoneRatio = doneRatio;
                    }
                }
                else if (columnName == "StartDate")
                {
                    if (DateTime.TryParse(value, out DateTime startDate))
                    {
                        issue.StartDate = startDate;
                    }
                }
                else if (columnName == "DueDate")
                {
                    if (DateTime.TryParse(value, out DateTime dueDate))
                    {
                        issue.DueDate = dueDate;
                    }
                }
            }

            await manager.UpdateObjectAsync(taskId, issue);
        }

        // Task'e yorum ekle
        public async Task addIssueCommentAsync(string taskId, string comment)
        {
            // redmine'a bağlı değilsek iptal et
            if (!connectionStatus)
            {
                Console.WriteLine("Redmine'a bağlı değilsiniz.");
                return;
            }

            var issue = manager?.GetObject<Issue>(taskId, new NameValueCollection { { "include", "relations,children,attachments,journals,watchers" } });
            if (issue == null) return;

            // Yorum eklemek için issue'yi güncelle
            issue.Notes = comment;

            await manager.UpdateObjectAsync(taskId, issue);
        }
    }
}
