using System;
using System.Collections.Generic;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace QA_Utility.classes.TestRail
{
    /// <summary>
    /// Helper class to parse different TestRail API response formats
    /// </summary>
    public class TestRailResponseWrapper<T>
    {
        [JsonPropertyName("offset")]
        public int? Offset { get; set; }
        
        [JsonPropertyName("limit")]
        public int? Limit { get; set; }
        
        [JsonPropertyName("size")]
        public int? Size { get; set; }
        
        [JsonPropertyName("_links")]
        public Dictionary<string, object> Links { get; set; }
        
        [JsonPropertyName("projects")]
        public List<T> Projects { get; set; }
        
        [JsonPropertyName("suites")]
        public List<T> Suites { get; set; }
        
        [JsonPropertyName("cases")]
        public List<T> Cases { get; set; }
        
        [JsonPropertyName("runs")]
        public List<T> Runs { get; set; }
    }
}
