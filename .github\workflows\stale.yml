name: Mark stale issues and pull requests

on:
  schedule:
  - cron: '0 0 * * *'  # Her gün gece yarısı çalıştır

jobs:
  stale:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/stale@v5
      with:
        repo-token: ${{ secrets.GITHUB_TOKEN }}
        stale-issue-message: 'Bu sorun 60 gündür güncellenmedi ve "stale" olarak işaretlendi. 7 gün içinde herhangi bir aktivite olmazsa, bu sorun otomatik olarak kapatılacaktır.'
        stale-pr-message: 'Bu pull request 30 gündür güncellenmedi ve "stale" olarak işaretlendi. 7 gün içinde herhangi bir aktivite olmazsa, bu pull request otomatik olarak kapatılacaktır.'
        close-issue-message: 'Bu sorun, 7 gün boyunca herhangi bir aktivite olmadığı için otomatik olarak kapatıldı. Sorun hala devam ediyorsa, lü<PERSON><PERSON> yeni bir sorun açın.'
        close-pr-message: 'Bu pull request, 7 gün boyunca herhangi bir aktivite olmadığı için otomatik olarak kapatıldı. Değişikliklerinizi hala birleştirmek istiyorsanız, lütfen yeni bir pull request açın.'
        stale-issue-label: 'stale'
        stale-pr-label: 'stale'
        exempt-issue-labels: 'bug,enhancement,documentation,security,pinned,in-progress'
        exempt-pr-labels: 'awaiting-approval,work-in-progress,pinned,in-progress'
        days-before-stale: 60  # Sorunlar için 60 gün
        days-before-pr-stale: 30  # PR'ler için 30 gün
        days-before-close: 7  # Stale olduktan 7 gün sonra kapat
        exempt-all-milestones: true  # Bir kilometre taşına atanmış tüm sorunları ve PR'leri muaf tut