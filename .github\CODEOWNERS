# Bu dosya, belirli dosya veya dizinler için otomatik olarak inceleme isteklerinin atanmasını sağlar
# Her satır bir sahiplik kuralını tanımlar ve şu formattadır:
# dosya_deseni sahip1 sahip2 ...

# V<PERSON><PERSON><PERSON><PERSON> sahipler (tüm dosyalar için)
* @joygame/qa-utility-maintainers

# QA Utility ana uygulama
/qa_utility/ @joygame/qa-utility-core-team

# AutoUpdater bileşeni
/AutoUpdater/ @joygame/qa-utility-devops

# Web arayüzü ve JavaScript dosyaları
/qa_utility/Resources/*.html @joygame/qa-utility-frontend
/qa_utility/Resources/*.js @joygame/qa-utility-frontend
/qa_utility/Resources/*.css @joygame/qa-utility-frontend

# Mobil cihaz entegrasyonu
/qa_utility/platform-tools/ @joygame/qa-utility-mobile-team
/qa_utility/ioslog/ @joygame/qa-utility-mobile-team

# API entegrasyonları
/qa_utility/classes/TestRail/ @joygame/qa-utility-integration-team
/qa_utility/classes/Redmine/ @joygame/qa-utility-integration-team
/qa_utility/classes/GoogleAPI/ @joygame/qa-utility-integration-team

# Dokümantasyon
/Docs/ @joygame/qa-utility-docs
*.md @joygame/qa-utility-docs

# CI/CD ve GitHub yapılandırması
/.github/ @joygame/qa-utility-devops