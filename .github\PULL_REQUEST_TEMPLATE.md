# Pull Request Açıklaması

Lütfen bu PR'nin ne değişiklikler yaptığını açıklayın ve ilgili sorunları bağlayın.

## Değişiklik Türü

Lütfen bu PR'nin türünü belirtmek için ilgili kutuları işaretleyin:

- [ ] <PERSON><PERSON> düzeltmesi (mevcut işlevselliği bozmayan bir değişiklik)
- [ ] <PERSON><PERSON> (mevcut işlevselliği bozmayan bir değişiklik)
- [ ] Yıkıcı değişiklik (mevcut işlevselliği bozacak bir düzeltme veya özellik)
- [ ] Bu değişiklik bir dokümantasyon güncellemesi gerektirir
- [ ] Dokümantasyon güncellemesi

## Bu PR Nasıl Test Edildi?

Lütfen değişikliklerinizi test etmek için yaptığınız testleri açıklayın. Aşağıdakileri sağlayın:

- [ ] Birim testleri
- [ ] Entegrasyon testleri
- [ ] <PERSON> testler (test adımlarını açıklayın)

Ayrıca, test ortamınızı da belirtin:

- İşletim Sistemi:
- .NET Framework Sürümü:
- WebView2 Runtime Sürümü:
- Test edilen cihazlar (varsa):

## Kontrol Listesi:

- [ ] Kodum tüm kendi testlerinden geçiyor
- [ ] Yeni ve mevcut birim testlerini yerel olarak çalıştırdım
- [ ] Kod stilini ve formatlama kurallarını takip ettim
- [ ] Gereksiz yorum satırlarını, yazdırmaları ve hata ayıklama kodunu kaldırdım
- [ ] Değişikliklerim yeni uyarılara neden olmuyor
- [ ] Dokümantasyonu güncelledim
- [ ] Değişikliklerim geriye dönük uyumludur

## İlgili Sorunlar

Bu PR'nin çözdüğü sorunları listeleyin. Örneğin:

Fixes #123, Addresses #456, Related to #789

## Ekran Görüntüleri (varsa)

Değişikliklerinizi gösteren ekran görüntüleri ekleyin (özellikle UI değişiklikleri için).

## Ek Bağlam

Bu PR hakkında başka bir bağlam veya bilgi ekleyin.