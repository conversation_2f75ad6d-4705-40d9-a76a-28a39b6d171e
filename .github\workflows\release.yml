name: Release

on:
  push:
    tags:
      - 'v*'

jobs:
  build:
    runs-on: windows-latest

    steps:
    - uses: actions/checkout@v3
      with:
        fetch-depth: 0

    - name: Setup MSBuild
      uses: microsoft/setup-msbuild@v1.1

    - name: Setup NuGet
      uses: NuGet/setup-nuget@v1.0.5

    - name: Setup .NET Framework
      uses: actions/setup-dotnet@v3
      with:
        dotnet-version: '6.0.x'

    - name: Get version from tag
      id: get_version
      run: echo "VERSION=${GITHUB_REF#refs/tags/v}" >> $GITHUB_OUTPUT
      shell: bash

    - name: Update version in AssemblyInfo.cs
      run: |
        $version = "${{ steps.get_version.outputs.VERSION }}"
        $files = Get-ChildItem -Path . -Filter AssemblyInfo.cs -Recurse
        foreach ($file in $files) {
          (Get-Content $file.FullName) -replace '\[assembly: AssemblyVersion\("[0-9]+(\.[0-9]+)*"\)\]', "[assembly: AssemblyVersion(\"$version\")]" | Set-Content $file.FullName
          (Get-Content $file.FullName) -replace '\[assembly: AssemblyFileVersion\("[0-9]+(\.[0-9]+)*"\)\]', "[assembly: AssemblyFileVersion(\"$version\")]" | Set-Content $file.FullName
        }
      shell: pwsh

    - name: Restore NuGet packages
      run: nuget restore "QA Utility.sln"

    - name: Build solution
      run: msbuild "QA Utility.sln" /p:Configuration=Release /p:Platform="Any CPU"

    - name: Run tests
      run: dotnet test --no-build --verbosity normal

    - name: Create installer
      run: |
        # Burada Inno Setup veya benzeri bir araç kullanarak kurulum dosyası oluşturulabilir
        # Örnek: iscc "installer.iss"
      shell: pwsh

    - name: Create ZIP archive
      run: |
        $version = "${{ steps.get_version.outputs.VERSION }}"
        $zipFileName = "joygame-qa-utility-$version.zip"
        Compress-Archive -Path "qa_utility\bin\Release\*" -DestinationPath $zipFileName
      shell: pwsh

    - name: Create Release
      id: create_release
      uses: actions/create-release@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        tag_name: ${{ github.ref }}
        release_name: Joygame QA Utility ${{ steps.get_version.outputs.VERSION }}
        body_path: CHANGELOG.md
        draft: false
        prerelease: false

    - name: Upload ZIP Release Asset
      uses: actions/upload-release-asset@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        upload_url: ${{ steps.create_release.outputs.upload_url }}
        asset_path: ./joygame-qa-utility-${{ steps.get_version.outputs.VERSION }}.zip
        asset_name: joygame-qa-utility-${{ steps.get_version.outputs.VERSION }}.zip
        asset_content_type: application/zip

    - name: Upload Installer Release Asset
      uses: actions/upload-release-asset@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        upload_url: ${{ steps.create_release.outputs.upload_url }}
        asset_path: ./joygame-qa-utility-setup-${{ steps.get_version.outputs.VERSION }}.exe
        asset_name: joygame-qa-utility-setup-${{ steps.get_version.outputs.VERSION }}.exe
        asset_content_type: application/octet-stream

  update-ftp:
    needs: build
    runs-on: windows-latest

    steps:
    - uses: actions/checkout@v3

    - name: Get version from tag
      id: get_version
      run: echo "VERSION=${GITHUB_REF#refs/tags/v}" >> $GITHUB_OUTPUT
      shell: bash

    - name: Download release assets
      run: |
        $version = "${{ steps.get_version.outputs.VERSION }}"
        $zipUrl = "https://github.com/joygame/qa-utility/releases/download/v$version/joygame-qa-utility-$version.zip"
        $installerUrl = "https://github.com/joygame/qa-utility/releases/download/v$version/joygame-qa-utility-setup-$version.exe"
        Invoke-WebRequest -Uri $zipUrl -OutFile "joygame-qa-utility-$version.zip"
        Invoke-WebRequest -Uri $installerUrl -OutFile "joygame-qa-utility-setup-$version.exe"
      shell: pwsh

    - name: Upload to FTP
      uses: SamKirkland/FTP-Deploy-Action@4.3.0
      with:
        server: ${{ secrets.FTP_SERVER }}
        username: ${{ secrets.FTP_USERNAME }}
        password: ${{ secrets.FTP_PASSWORD }}
        local-dir: ./
        server-dir: /updates/
        include: |
          joygame-qa-utility-${{ steps.get_version.outputs.VERSION }}.zip
          joygame-qa-utility-setup-${{ steps.get_version.outputs.VERSION }}.exe

    - name: Update version.json
      run: |
        $version = "${{ steps.get_version.outputs.VERSION }}"
        $versionJson = @{
          "version" = $version
          "releaseDate" = (Get-Date -Format "yyyy-MM-dd")
          "zipUrl" = "ftp://updates.joygame.com/updates/joygame-qa-utility-$version.zip"
          "installerUrl" = "ftp://updates.joygame.com/updates/joygame-qa-utility-setup-$version.exe"
          "changelogUrl" = "https://github.com/joygame/qa-utility/blob/main/CHANGELOG.md"
        } | ConvertTo-Json
        $versionJson | Set-Content -Path "version.json"
      shell: pwsh

    - name: Upload version.json to FTP
      uses: SamKirkland/FTP-Deploy-Action@4.3.0
      with:
        server: ${{ secrets.FTP_SERVER }}
        username: ${{ secrets.FTP_USERNAME }}
        password: ${{ secrets.FTP_PASSWORD }}
        local-dir: ./
        server-dir: /updates/
        include: |
          version.json