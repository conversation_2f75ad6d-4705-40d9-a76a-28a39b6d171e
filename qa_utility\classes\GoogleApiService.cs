﻿﻿using Google.Apis.Auth.OAuth2;
using Google.Apis.Drive.v3;
using Google.Apis.Drive.v3.Data;
using Google.Apis.Oauth2.v2;
using Google.Apis.Services;
using Google.Apis.Upload;
using Google.Apis.Util;
using Google.Apis.Util.Store;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace QA_Utility.classes
{
    /// <summary>
    /// Ana Google API servisi sınıfı. Tüm Google API entegrasyonlarını yönetir.
    /// </summary>
    public class GoogleApiService
    {
        #region Common Properties and Fields

        private static UserCredential credential;
        private static readonly string ApplicationName = "QA Utility";
        private static readonly string ClientSecretFilePath = "client_secret.json";
        private static readonly string TokenFilePath = "token.json";
        private static readonly string ApiKey = ConfigurationManager.AppSettings["GoogleApiKey"];// "AIzaSyD5aEzzAu4j_Wr7hpinxrd8JFkQyP56gJs";

        // Tüm Google servisleri için gerekli scope'ları birleştir
        private static readonly string[] AllScopes = {
            DriveService.Scope.DriveFile,
            DriveService.Scope.Drive,
            Oauth2Service.Scope.UserinfoProfile,
            Oauth2Service.Scope.UserinfoEmail,
            "https://www.googleapis.com/auth/androidpublisher"
        };

        #endregion

        #region Authentication

        /// <summary>
        /// Google API için kimlik doğrulama yapar ve token alır
        /// </summary>
        /// <returns>Access token</returns>
        public static async Task<string> GetToken()
        {
            try
            {
                var credential = await GetCredentials();
                return credential.Token.AccessToken;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Token alınırken hata oluştu: {ex.Message}", "Hata", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return null;
            }
        }

        /// <summary>
        /// Eski metotlarla uyumluluk için
        /// </summary>
        /// <param name="scopes">Kullanılmayacak</param>
        /// <returns>Access token</returns>
        public static async Task<string> GetToken(string[] scopes)
        {
            return await GetToken();
        }

        /// <summary>
        /// Google API için kimlik bilgilerini alır
        /// </summary>
        /// <returns>Kimlik bilgileri</returns>
        private static async Task<UserCredential> GetCredentials()
        {
            try
            {
                using (var stream = new FileStream(ClientSecretFilePath, FileMode.Open, FileAccess.Read))
                {
                    credential = await GoogleWebAuthorizationBroker.AuthorizeAsync(
                        GoogleClientSecrets.FromStream(stream).Secrets,
                        AllScopes,  // Tüm scope'ları kullan
                        "user",
                        CancellationToken.None,
                        new FileDataStore(TokenFilePath, true));

                    // Token süresi dolmuşsa yenile
                    if (credential.Token.IsExpired(SystemClock.Default))
                    {
                        await credential.RefreshTokenAsync(CancellationToken.None);
                    }

                    return credential;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Kimlik doğrulama hatası: {ex.Message}", "Hata", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return null;
            }
        }

        /// <summary>
        /// Eski metotlarla uyumluluk için
        /// </summary>
        /// <param name="scopes">Kullanılmayacak</param>
        /// <returns>Kimlik bilgileri</returns>
        private static async Task<UserCredential> GetCredentials(string[] scopes)
        {
            return await GetCredentials();
        }

        /// <summary>
        /// Kimlik bilgilerini yeniler
        /// </summary>
        /// <returns>Yenilenen kimlik bilgileri</returns>
        public static async Task<UserCredential> ReauthorizeAsync()
        {
            try
            {
                if (credential != null)
                {
                    await GoogleWebAuthorizationBroker.ReauthorizeAsync(credential, CancellationToken.None);
                    return credential;
                }
                return null;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Kimlik yenileme hatası: {ex.Message}", "Hata", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return null;
            }
        }

        #endregion

        #region Google Drive Service

        private static DriveService driveService;

        /// <summary>
        /// Klasör seçim dialog'u
        /// </summary>
        public class FolderSelectionDialog : Form
        {
            private TreeView folderTreeView;
            private Button selectButton;
            private Button cancelButton;
            private Button createFolderButton;
            private TextBox newFolderTextBox;

            public string SelectedFolderId { get; private set; }

            public FolderSelectionDialog(List<DriveFolder> rootFolders)
            {
                InitializeComponent();
                PopulateTreeView(rootFolders);
            }

            private void InitializeComponent()
            {
                this.Text = "Google Drive Klasör Seçimi";
                this.Size = new System.Drawing.Size(600, 500);
                this.StartPosition = FormStartPosition.CenterScreen;
                this.MinimumSize = new System.Drawing.Size(500, 400);

                // TableLayoutPanel kullanalım - daha güvenilir layout için
                TableLayoutPanel mainLayout = new TableLayoutPanel
                {
                    Dock = DockStyle.Fill,
                    RowCount = 2,
                    ColumnCount = 1
                };

                // Row yüksekliklerini ayarla
                mainLayout.RowStyles.Add(new RowStyle(SizeType.Percent, 70F)); // TreeView için %70
                mainLayout.RowStyles.Add(new RowStyle(SizeType.Percent, 30F)); // Butonlar için %30

                // TreeView oluştur
                folderTreeView = new TreeView
                {
                    Dock = DockStyle.Fill,
                    Margin = new Padding(10)
                };
                folderTreeView.NodeMouseDoubleClick += FolderTreeView_NodeMouseDoubleClick;

                // Alt panel için TableLayoutPanel
                TableLayoutPanel buttonPanel = new TableLayoutPanel
                {
                    Dock = DockStyle.Fill,
                    RowCount = 2,
                    ColumnCount = 2,
                    Margin = new Padding(10)
                };

                // Üst satır: TextBox ve Yeni Klasör butonu
                buttonPanel.RowStyles.Add(new RowStyle(SizeType.Percent, 50F));
                buttonPanel.RowStyles.Add(new RowStyle(SizeType.Percent, 50F));
                buttonPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 70F));
                buttonPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 30F));

                // TextBox
                newFolderTextBox = new TextBox
                {
                    Dock = DockStyle.Fill,
                    Margin = new Padding(5)
                };

                // Yeni Klasör butonu
                createFolderButton = new Button
                {
                    Text = "Yeni Klasör Oluştur",
                    Dock = DockStyle.Fill,
                    Margin = new Padding(5)
                };
                createFolderButton.Click += CreateFolderButton_Click;

                // Alt satır için FlowLayoutPanel - butonları sağa hizalamak için
                FlowLayoutPanel actionButtonsPanel = new FlowLayoutPanel
                {
                    Dock = DockStyle.Fill,
                    FlowDirection = FlowDirection.RightToLeft,
                    Margin = new Padding(5),
                    Padding = new Padding(0, 10, 0, 0)
                };

                // Seç ve İptal butonları
                cancelButton = new Button
                {
                    Text = "İptal",
                    Size = new System.Drawing.Size(80, 30),
                    DialogResult = DialogResult.Cancel,
                    Margin = new Padding(5)
                };

                selectButton = new Button
                {
                    Text = "Seç",
                    Size = new System.Drawing.Size(80, 30),
                    DialogResult = DialogResult.OK,
                    Margin = new Padding(5)
                };
                selectButton.Click += SelectButton_Click;

                // Butonları FlowLayoutPanel'e ekle
                actionButtonsPanel.Controls.Add(cancelButton);
                actionButtonsPanel.Controls.Add(selectButton);

                // Kontrolleri buttonPanel'e ekle
                buttonPanel.Controls.Add(newFolderTextBox, 0, 0);
                buttonPanel.Controls.Add(createFolderButton, 1, 0);
                buttonPanel.Controls.Add(actionButtonsPanel, 0, 1);
                buttonPanel.SetColumnSpan(actionButtonsPanel, 2);

                // Ana layout'a ekle
                mainLayout.Controls.Add(folderTreeView, 0, 0);
                mainLayout.Controls.Add(buttonPanel, 0, 1);

                // Form'a ekle
                this.Controls.Add(mainLayout);

                // Varsayılan buton
                this.AcceptButton = selectButton;
                this.CancelButton = cancelButton;
            }

            private async void FolderTreeView_NodeMouseDoubleClick(object sender, TreeNodeMouseClickEventArgs e)
            {
                if (e.Node.Tag is string folderId)
                {
                    var subFolders = await GoogleApiService.GetFolders(folderId);
                    e.Node.Nodes.Clear();

                    foreach (var folder in subFolders)
                    {
                        var node = new TreeNode(folder.Name) { Tag = folder.Id };
                        e.Node.Nodes.Add(node);
                    }

                    e.Node.Expand();
                }
            }

            private void PopulateTreeView(List<DriveFolder> folders)
            {
                folderTreeView.Nodes.Clear();

                // Root klasörü ekle
                var rootNode = new TreeNode("Drive") { Tag = "root" };
                folderTreeView.Nodes.Add(rootNode);

                // Alt klasörleri ekle
                foreach (var folder in folders)
                {
                    var node = new TreeNode(folder.Name) { Tag = folder.Id };
                    rootNode.Nodes.Add(node);
                }

                rootNode.Expand();
            }

            private void SelectButton_Click(object sender, EventArgs e)
            {
                if (folderTreeView.SelectedNode != null && folderTreeView.SelectedNode.Tag is string folderId)
                {
                    SelectedFolderId = folderId;
                    DialogResult = DialogResult.OK;
                    Close();
                }
                else
                {
                    MessageBox.Show("Lütfen bir klasör seçin.", "Uyarı", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }
            }

            private async void CreateFolderButton_Click(object sender, EventArgs e)
            {
                string folderName = newFolderTextBox.Text.Trim();
                if (string.IsNullOrEmpty(folderName))
                {
                    MessageBox.Show("Lütfen klasör adı girin.", "Uyarı", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                if (folderTreeView.SelectedNode == null)
                {
                    MessageBox.Show("Lütfen bir üst klasör seçin.", "Uyarı", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                string parentId = folderTreeView.SelectedNode.Tag as string;
                if (string.IsNullOrEmpty(parentId))
                {
                    MessageBox.Show("Geçersiz klasör seçimi.", "Hata", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                try
                {
                    // Yeni klasör oluştur
                    var newFolder = await GoogleApiService.CreateFolder(parentId, folderName);

                    // Ağaca ekle
                    var newNode = new TreeNode(newFolder.Name) { Tag = newFolder.Id };
                    folderTreeView.SelectedNode.Nodes.Add(newNode);
                    folderTreeView.SelectedNode.Expand();

                    newFolderTextBox.Clear();
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Klasör oluşturulurken hata oluştu: {ex.Message}", "Hata", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        /// <summary>
        /// Google Drive servisini başlatır ve yetkilendirir
        /// </summary>
        /// <returns>Başarılı olup olmadığı</returns>
        public static async Task<bool> InitializeDriveService()
        {
            try
            {
                var credential = await GetCredentials();
                if (credential == null) return false;

                // Drive servisini oluştur
                driveService = new DriveService(new BaseClientService.Initializer()
                {
                    HttpClientInitializer = credential,
                    ApplicationName = ApplicationName,
                });

                return true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Drive servisi başlatılamadı: {ex.Message}", "Hata", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return false;
            }
        }

        /// <summary>
        /// Belirtilen parent klasörü altındaki tüm klasörleri getirir
        /// </summary>
        /// <param name="parentId">Parent klasör ID'si</param>
        /// <returns>Klasör listesi</returns>
        public static async Task<List<DriveFolder>> GetFolders(string parentId)
        {
            try
            {
                if (driveService == null)
                {
                    bool initialized = await InitializeDriveService();
                    if (!initialized) return new List<DriveFolder>();
                }

                var request = driveService.Files.List();
                request.Q = $"mimeType='application/vnd.google-apps.folder' and '{parentId}' in parents and trashed=false";
                request.Fields = "files(id, name)";

                var result = await request.ExecuteAsync();

                return result.Files.Select(f => new DriveFolder
                {
                    Id = f.Id,
                    Name = f.Name
                }).ToList();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Klasörler getirilirken hata oluştu: {ex.Message}", "Hata", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return new List<DriveFolder>();
            }
        }

        /// <summary>
        /// Kullanıcının Drive'ında klasör seçmesini sağlar
        /// </summary>
        /// <returns>Seçilen klasörün ID'si veya iptal edilirse null</returns>
        public static async Task<string> SelectFolder()
        {
            try
            {
                if (driveService == null)
                {
                    bool initialized = await InitializeDriveService();
                    if (!initialized) return null;
                }

                // Root klasörü al
                var rootFolders = await GetFolders("root");

                // Kullanıcıya klasör seçtir
                using (var folderDialog = new FolderSelectionDialog(rootFolders))
                {
                    if (folderDialog.ShowDialog() == DialogResult.OK)
                    {
                        return folderDialog.SelectedFolderId;
                    }
                }

                return null;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Klasör seçiminde hata oluştu: {ex.Message}", "Hata", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return null;
            }
        }

        /// <summary>
        /// Yüklenen dosya bilgilerini içeren sınıf
        /// </summary>
        public class DriveFileInfo
        {
            public bool Success { get; set; }
            public string FileId { get; set; }
            public string WebViewLink { get; set; }
        }

        /// <summary>
        /// Dosyayı Google Drive'a yükler
        /// </summary>
        /// <param name="filePath">Yüklenecek dosyanın yolu</param>
        /// <param name="folderId">Yüklenecek klasörün ID'si</param>
        /// <returns>Yükleme bilgilerini içeren nesne</returns>
        public static async Task<DriveFileInfo> UploadFileToDrive(string filePath, string folderId)
        {
            try
            {
                if (driveService == null)
                {
                    bool initialized = await InitializeDriveService();
                    if (!initialized) return new DriveFileInfo { Success = false };
                }

                if (!System.IO.File.Exists(filePath))
                {
                    MessageBox.Show($"Dosya bulunamadı: {filePath}", "Hata", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return new DriveFileInfo { Success = false };
                }

                var fileMetadata = new Google.Apis.Drive.v3.Data.File()
                {
                    Name = Path.GetFileName(filePath),
                    Parents = new List<string> { folderId }
                };

                // MIME tipini belirle
                string mimeType = "application/octet-stream";
                string extension = Path.GetExtension(filePath).ToLower();
                switch (extension)
                {
                    case ".jpg":
                    case ".jpeg":
                        mimeType = "image/jpeg";
                        break;
                    case ".png":
                        mimeType = "image/png";
                        break;
                    case ".gif":
                        mimeType = "image/gif";
                        break;
                    case ".pdf":
                        mimeType = "application/pdf";
                        break;
                    case ".txt":
                        mimeType = "text/plain";
                        break;
                    case ".mp4":
                        mimeType = "video/mp4";
                        break;
                    case ".zip":
                        mimeType = "application/zip";
                        break;
                }

                using (var stream = new FileStream(filePath, FileMode.Open))
                {
                    var request = driveService.Files.Create(fileMetadata, stream, mimeType);
                    // ID ve webViewLink alanlarını iste
                    request.Fields = "id, webViewLink";

                    // Progress için
                    request.ProgressChanged += (IUploadProgress progress) =>
                    {
                        switch (progress.Status)
                        {
                            case UploadStatus.Uploading:
                                Console.WriteLine($"{progress.BytesSent} bytes gönderildi.");
                                break;
                            case UploadStatus.Failed:
                                Console.WriteLine("Yükleme başarısız oldu.");
                                break;
                        }
                    };

                    var response = await request.UploadAsync();
                    if (response.Status == UploadStatus.Failed)
                    {
                        MessageBox.Show($"Dosya yüklenirken hata oluştu: {response.Exception.Message}", "Hata", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        return new DriveFileInfo { Success = false };
                    }

                    // Yüklenen dosyanın ID'sini al
                    var file = request.ResponseBody;

                    return new DriveFileInfo
                    {
                        Success = true,
                        FileId = file.Id,
                        WebViewLink = file.WebViewLink
                    };
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Dosya yüklenirken hata oluştu: {ex.Message}", "Hata", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return new DriveFileInfo { Success = false };
            }
        }

        /// <summary>
        /// Belirtilen parent klasör altında aynı isimde klasör olup olmadığını kontrol eder
        /// </summary>
        /// <param name="parentId">Parent klasör ID'si</param>
        /// <param name="folderName">Kontrol edilecek klasör adı</param>
        /// <returns>Klasör varsa bilgileri, yoksa null</returns>
        public static async Task<DriveFolder> GetFolderByName(string parentId, string folderName)
        {
            try
            {
                if (driveService == null)
                {
                    bool initialized = await InitializeDriveService();
                    if (!initialized) return null;
                }

                var request = driveService.Files.List();
                request.Q = $"mimeType='application/vnd.google-apps.folder' and '{parentId}' in parents and name='{folderName}' and trashed=false";
                request.Fields = "files(id, name)";

                var result = await request.ExecuteAsync();

                if (result.Files != null && result.Files.Count > 0)
                {
                    return new DriveFolder
                    {
                        Id = result.Files[0].Id,
                        Name = result.Files[0].Name
                    };
                }

                return null;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Klasör kontrolünde hata: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Google Drive'da yeni klasör oluşturur veya varsa mevcut klasörü döndürür
        /// </summary>
        /// <param name="parentId">Parent klasör ID'si</param>
        /// <param name="folderName">Oluşturulacak klasör adı</param>
        /// <returns>Oluşturulan veya mevcut klasör bilgileri</returns>
        public static async Task<DriveFolder> CreateFolder(string parentId, string folderName)
        {
            try
            {
                if (driveService == null)
                {
                    bool initialized = await InitializeDriveService();
                    if (!initialized) return null;
                }

                // Önce aynı isimde klasör var mı kontrol et
                var existingFolder = await GetFolderByName(parentId, folderName);
                if (existingFolder != null)
                {
                    // Aynı isimde klasör varsa onu döndür
                    return existingFolder;
                }

                // Klasör yoksa yeni oluştur
                var fileMetadata = new Google.Apis.Drive.v3.Data.File()
                {
                    Name = folderName,
                    MimeType = "application/vnd.google-apps.folder",
                    Parents = new List<string> { parentId }
                };

                var request = driveService.Files.Create(fileMetadata);
                request.Fields = "id, name";
                var folder = await request.ExecuteAsync();

                return new DriveFolder
                {
                    Id = folder.Id,
                    Name = folder.Name
                };
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Klasör oluştururken hata oluştu: {ex.Message}", "Hata", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return null;
            }
        }

        #endregion

        #region Google Play Publisher API

        /// <summary>
        /// Google Play'den uygulama yorumlarını getirir
        /// </summary>
        /// <param name="packageName">Uygulama paket adı</param>
        /// <returns>Yorumlar tablosu</returns>
        public static async Task<DataTable> GetAllComments(string packageName)
        {
            DataTable allComments = new DataTable();
            SetupCommentsDataTable(allComments);

            try
            {
                string accessToken = await GetToken();
                if (string.IsNullOrEmpty(accessToken)) return allComments;

                using (HttpClient client = new HttpClient())
                {
                    client.BaseAddress = new Uri("https://androidpublisher.googleapis.com/");
                    client.DefaultRequestHeaders.Accept.Clear();
                    client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);
                    client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));

                    var requestUri = $"androidpublisher/v3/applications/{packageName}/reviews?key={ApiKey}";

                    HttpResponseMessage response = await client.GetAsync(requestUri);
                    if (response.IsSuccessStatusCode)
                    {
                        string data = await response.Content.ReadAsStringAsync();
                        var reviewRoot = JsonConvert.DeserializeObject<ReviewModels.ReviewRoot>(data);

                        if (reviewRoot?.Reviews != null)
                        {
                            int id = 1;
                            foreach (var review in reviewRoot.Reviews)
                            {
                                if (review.Comments != null && review.Comments.Count > 0)
                                {
                                    foreach (var comment in review.Comments)
                                    {
                                        if (comment.UserComment != null)
                                        {
                                            DataRow row = allComments.NewRow();
                                            row["Id"] = id++;
                                            row["ReviewId"] = review.ReviewId;
                                            row["PackageName"] = packageName;
                                            row["AuthorName"] = review.AuthorName;
                                            row["CommentText"] = comment.UserComment.Text;

                                            if (comment.UserComment.LastModified != null)
                                            {
                                                row["LastModified"] = comment.UserComment.LastModified.ToDateTime();
                                            }

                                            row["StarRating"] = comment.UserComment.StarRating;
                                            row["ReviewerLanguage"] = comment.UserComment.ReviewerLanguage;
                                            row["Device"] = comment.UserComment.Device;
                                            row["AndroidOsVersion"] = comment.UserComment.AndroidOsVersion;
                                            row["AppVersionCode"] = comment.UserComment.AppVersionCode;
                                            row["AppVersionName"] = comment.UserComment.AppVersionName;

                                            if (comment.UserComment.DeviceMetadata != null)
                                            {
                                                row["ProductName"] = comment.UserComment.DeviceMetadata.ProductName;
                                                row["Manufacturer"] = comment.UserComment.DeviceMetadata.Manufacturer;
                                                row["DeviceClass"] = comment.UserComment.DeviceMetadata.DeviceClass;
                                                row["ScreenWidthPx"] = comment.UserComment.DeviceMetadata.ScreenWidthPx;
                                                row["ScreenHeightPx"] = comment.UserComment.DeviceMetadata.ScreenHeightPx;
                                                row["NativePlatform"] = comment.UserComment.DeviceMetadata.NativePlatform;
                                                row["ScreenDensityDpi"] = comment.UserComment.DeviceMetadata.ScreenDensityDpi;
                                                row["GlEsVersion"] = comment.UserComment.DeviceMetadata.GlEsVersion;
                                                row["RamMb"] = comment.UserComment.DeviceMetadata.RamMb;
                                            }

                                            if (comment.DeveloperComment != null)
                                            {
                                                row["DeveloperReply"] = comment.DeveloperComment.Text;
                                                if (comment.DeveloperComment.LastModified != null)
                                                {
                                                    row["DeveloperReplyDate"] = comment.DeveloperComment.LastModified.ToDateTime();
                                                }
                                            }

                                            allComments.Rows.Add(row);
                                        }
                                    }
                                }
                            }
                        }
                    }
                    else
                    {
                        MessageBox.Show($"Yorumlar alınırken hata oluştu: {response.StatusCode}", "Hata", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Yorumlar alınırken hata oluştu: {ex.Message}", "Hata", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }

            return allComments;
        }

        /// <summary>
        /// Yorum tablosunu oluşturur
        /// </summary>
        /// <param name="commentsTable">Oluşturulacak tablo</param>
        public static void SetupCommentsDataTable(DataTable commentsTable)
        {
            commentsTable.Columns.Add("Id", typeof(int)).AutoIncrement = true;
            commentsTable.Columns.Add("ReviewId", typeof(string));
            commentsTable.Columns.Add("PackageName", typeof(string));
            commentsTable.Columns.Add("AuthorName", typeof(string));
            commentsTable.Columns.Add("CommentText", typeof(string));
            commentsTable.Columns.Add("LastModified", typeof(DateTime));
            commentsTable.Columns.Add("StarRating", typeof(int));
            commentsTable.Columns.Add("ReviewerLanguage", typeof(string));
            commentsTable.Columns.Add("Device", typeof(string));
            commentsTable.Columns.Add("AndroidOsVersion", typeof(int));
            commentsTable.Columns.Add("AppVersionCode", typeof(int));
            commentsTable.Columns.Add("AppVersionName", typeof(string));
            commentsTable.Columns.Add("ProductName", typeof(string));
            commentsTable.Columns.Add("Manufacturer", typeof(string));
            commentsTable.Columns.Add("DeviceClass", typeof(string));
            commentsTable.Columns.Add("ScreenWidthPx", typeof(int));
            commentsTable.Columns.Add("ScreenHeightPx", typeof(int));
            commentsTable.Columns.Add("NativePlatform", typeof(string));
            commentsTable.Columns.Add("ScreenDensityDpi", typeof(int));
            commentsTable.Columns.Add("GlEsVersion", typeof(int));
            commentsTable.Columns.Add("RamMb", typeof(int));
            commentsTable.Columns.Add("DeveloperReply", typeof(string));
            commentsTable.Columns.Add("DeveloperReplyDate", typeof(DateTime));
        }

        /// <summary>
        /// Yorumlar için DataTable döndürür
        /// </summary>
        /// <returns>Yorumlar için DataTable</returns>
        public static DataTable GetCommentsDataTable()
        {
            DataTable commentsTable = new DataTable();
            SetupCommentsDataTable(commentsTable);
            return commentsTable;
        }

        /// <summary>
        /// Yoruma cevap verir
        /// </summary>
        /// <param name="packageName">Uygulama paket adı</param>
        /// <param name="reviewId">Yorum ID'si</param>
        /// <param name="reply">Cevap metni</param>
        /// <returns>İşlem başarılı mı?</returns>
        public static async Task<bool> ReplyToReview(string packageName, string reviewId, string reply)
        {
            try
            {
                string accessToken = await GetToken();
                if (string.IsNullOrEmpty(accessToken)) return false;

                string url = $"https://androidpublisher.googleapis.com/androidpublisher/v3/applications/{packageName}/reviews/{reviewId}:reply?key={ApiKey}";

                var replyContent = new
                {
                    replyText = reply
                };

                using (HttpClient client = new HttpClient())
                {
                    client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);

                    var content = new StringContent(JsonConvert.SerializeObject(replyContent), Encoding.UTF8, "application/json");

                    HttpResponseMessage response = await client.PostAsync(url, content);

                    if (response.IsSuccessStatusCode)
                    {
                        return true;
                    }
                    else
                    {
                        string errorContent = await response.Content.ReadAsStringAsync();
                        MessageBox.Show($"Cevap gönderilirken hata oluştu: {response.StatusCode}\n{errorContent}", "Hata", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        return false;
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Cevap gönderilirken hata oluştu: {ex.Message}", "Hata", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return false;
            }
        }

        /// <summary>
        /// Ödemeyi iade eder
        /// </summary>
        /// <param name="orderId">Sipariş ID'si</param>
        /// <param name="packageName">Uygulama paket adı</param>
        /// <returns>İşlem sonucu</returns>
        public static async Task<(bool isSuccess, string message)> RefundPayment(string orderId, string packageName)
        {
            try
            {
                string accessToken = await GetToken();
                if (string.IsNullOrEmpty(accessToken))
                    return (false, "Token alınamadı");

                string url = $"https://androidpublisher.googleapis.com/androidpublisher/v3/applications/{packageName}/orders/{orderId}:refund?revoke=true&key={ApiKey}";

                using (HttpClient client = new HttpClient())
                {
                    client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);

                    HttpResponseMessage response = await client.PostAsync(url, null);

                    if (response.IsSuccessStatusCode)
                    {
                        return (true, "İade işlemi başarılı");
                    }
                    else
                    {
                        string errorContent = await response.Content.ReadAsStringAsync();
                        return (false, $"İade işlemi başarısız: {response.StatusCode}\n{errorContent}");
                    }
                }
            }
            catch (Exception ex)
            {
                return (false, $"İade işlemi sırasında hata: {ex.Message}");
            }
        }

        #endregion

        #region Model Classes

        /// <summary>
        /// Google Drive klasör bilgilerini tutan sınıf
        /// </summary>
        public class DriveFolder
        {
            public string Id { get; set; }
            public string Name { get; set; }
        }

        /// <summary>
        /// Google Play Review modelleri
        /// </summary>
        public class ReviewModels
        {
            public class ReviewRoot
            {
                public List<Review> Reviews { get; set; }
            }

            public class Review
            {
                public string ReviewId { get; set; }
                public string AuthorName { get; set; }
                public List<Comment> Comments { get; set; }
            }

            public class Comment
            {
                public UserComment UserComment { get; set; }
                public DeveloperComment DeveloperComment { get; set; }
            }

            public class DeveloperComment
            {
                public string Text { get; set; }
                public LastModified LastModified { get; set; }
            }

            public class LastModified
            {
                public long Seconds { get; set; }
                public int Nanos { get; set; }

                public DateTime ToDateTime()
                {
                    // Unix zaman damgasını DateTime nesnesine dönüştürme
                    DateTimeOffset dateTimeOffset = DateTimeOffset.FromUnixTimeSeconds(Seconds).AddTicks(Nanos / 100);
                    return dateTimeOffset.DateTime;
                }
            }

            public class UserComment
            {
                public string Text { get; set; }
                public LastModified LastModified { get; set; }
                public int StarRating { get; set; }
                public string ReviewerLanguage { get; set; }
                public string Device { get; set; }
                public int AndroidOsVersion { get; set; }
                public int AppVersionCode { get; set; }
                public string AppVersionName { get; set; }
                public DeviceMetadata DeviceMetadata { get; set; }
            }

            public class DeviceMetadata
            {
                public string ProductName { get; set; }
                public string Manufacturer { get; set; }
                public string DeviceClass { get; set; }
                public int ScreenWidthPx { get; set; }
                public int ScreenHeightPx { get; set; }
                public string NativePlatform { get; set; }
                public int ScreenDensityDpi { get; set; }
                public int GlEsVersion { get; set; }
                public int RamMb { get; set; }
            }
        }

        #endregion
    }
}
