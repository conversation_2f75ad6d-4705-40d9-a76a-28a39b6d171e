﻿using System;
using System.DirectoryServices;

public class LdapAuthenticator
{
    public bool Authenticate(string username, string password)
    {
        try
        {
            string domain = "intaktiv.intl"; // Domain adresin
            string ldapPath = $"LDAP://{domain}";

            using (DirectoryEntry entry = new DirectoryEntry(ldapPath, $"{domain}\\{username}", password))
            {
                // Force authentication by accessing NativeObject
                object native = entry.NativeObject;
                return true; // <PERSON><PERSON><PERSON> başarılı
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine("LDAP Hatası: " + ex.Message);
            return false; // <PERSON><PERSON><PERSON> başarısız
        }
    }
}
