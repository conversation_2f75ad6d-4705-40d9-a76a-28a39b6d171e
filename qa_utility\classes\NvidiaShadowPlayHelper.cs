﻿using System;
using System.Runtime.InteropServices;
using System.Windows.Forms;

namespace QA_Utility.Helpers
{
    public static class NvidiaShadowPlayHelper
    {
        [DllImport("user32.dll", SetLastError = true)]
        private static extern void keybd_event(byte bVk, byte bScan, uint dwFlags, IntPtr dwExtraInfo);

        private const byte VK_MENU = 0x12; // Alt tuşu
        private const byte VK_F10 = 0x79; // F10 tuşu
        private const uint KEYEVENTF_KEYUP = 0x0002;

        /// <summary>
        /// NVIDIA ShadowPlay ile son 30 saniyeyi kaydeder.
        /// </summary>
        public static void CaptureLast30Seconds()
        {
            try
            {
                // Alt tuşuna bas
                keybd_event(VK_MENU, 0, 0, IntPtr.Zero);
                // F10 tuşuna bas
                keybd_event(VK_F10, 0, 0, IntPtr.Zero);

                // F10 tuşunu bırak
                keybd_event(VK_F10, 0, KEYEVENTF_KEYUP, IntPtr.Zero);
                // Alt tuşunu bırak
                keybd_event(VK_MENU, 0, KEYEVENTF_KEYUP, IntPtr.Zero);

            }
            catch (Exception ex)
            {
                MessageBox.Show($"NVIDIA ShadowPlay tetiklenirken bir hata oluştu: {ex.Message}",
                    "Hata", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
