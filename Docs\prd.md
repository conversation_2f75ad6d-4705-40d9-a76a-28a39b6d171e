# Joygame QA Utility - <PERSON><PERSON><PERSON>n Gereksinim Dokümanı (PRD)

## 1. Giriş

### 1.1 Amaç

Joygame QA Utility, oyun test süreçlerini otomatize etmek, hızlandırmak ve standardize etmek amacıyla geliştirilmiş bir araç setidir. <PERSON><PERSON>, uygulamanın temel özelliklerini, kullanım senaryolarını ve teknik gereksinimlerini tanımlar.

### 1.2 <PERSON><PERSON><PERSON>, QA Utility uygulamasının tüm bileşenlerini (ana uygulama ve AutoUpdater) kapsar ve aşağıdaki konuları içerir:

- Kullanıcı gereksinimleri
- Fonksiyonel özellikler
- Teknik gereksinimler
- Entegrasyon noktaları
- Kullanıcı arayüzü gereksinimleri

### 1.3 Hedef Kitle

- QA Ekibi: Oyun testlerini gerçekleştiren test uzmanları
- Geliştirme E<PERSON>bi: Hata raporlarını değerlendiren ve düzeltmeleri yapan geliştiriciler
- <PERSON><PERSON>: Test süreçlerini takip eden ve raporları değerlendiren yöneticiler

## 2. Ürün Özellikleri

### 2.1 Temel Özellikler

#### 2.1.1 Mobil Uygulama Testi

- **Android ve iOS Desteği**: Her iki platformda da test yapabilme
- **Uygulama Kurulum ve Kaldırma**: APK/IPA dosyalarını cihazlara yükleme ve kaldırma
- **Uygulama Bilgilerini Görüntüleme**: Versiyon, paket adı, hedef SDK gibi bilgileri görme
- **Uygulama Başlatma ve Durdurma**: Test edilen uygulamaları kontrol etme

#### 2.1.2 Ekran Kaydı ve Görüntü Alma

- **Ekran Görüntüsü**: Anlık ekran görüntüsü alma
- **Video Kaydı**: Test süreçlerini kaydetme
- **NVIDIA ShadowPlay Entegrasyonu**: Son 30 saniyeyi kaydetme özelliği

#### 2.1.3 Log Yönetimi

- **Logcat Kaydı**: Android cihazlardan log toplama
- **iOS Log Kaydı**: iOS cihazlardan sistem loglarını alma
- **Log Analizi**: Toplanan logları filtreleme ve analiz etme

#### 2.1.4 Entegrasyon Özellikleri

- **TestRail Entegrasyonu**: Test senaryolarını ve sonuçlarını senkronize etme
- **Redmine Entegrasyonu**: Hata takibi ve proje yönetimi
- **Google API Entegrasyonu**: Google servisleriyle bağlantı
- **LDAP Kimlik Doğrulama**: Kurumsal kimlik doğrulama sistemi

### 2.2 Otomatik Güncelleme

- **Versiyon Kontrolü**: Mevcut sürümü kontrol etme
- **Dosya Hash Kontrolü**: Değişen dosyaları tespit etme
- **Otomatik İndirme**: Güncellemeleri FTP sunucusundan indirme
- **Uygulama Güncelleme**: Ana uygulamayı kapatıp güncellemeleri uygulama

## 3. Kullanıcı Arayüzü Gereksinimleri

### 3.1 Web Tabanlı Arayüz

- **Modern Tasarım**: Kullanıcı dostu, modern bir arayüz
- **Responsive Tasarım**: Farklı ekran boyutlarına uyum
- **Sekme Yapısı**: Farklı işlevler için organize edilmiş sekmeler
- **Bildirim Sistemi**: Kullanıcıya bilgi veren bildirimler

### 3.2 Sekmeler ve İşlevleri

- **İşlemler**: Temel cihaz ve uygulama işlemleri
- **QA Task**: Test görevleri yönetimi
- **Video ve SS Verileri**: Ekran görüntüleri ve video kayıtları
- **Google Ödemeleri**: Ödeme işlemleri takibi
- **Oyun Verileri**: Oyun bilgileri ve istatistikleri
- **Yerel Loglar**: Kaydedilen logların görüntülenmesi
- **Task Oluşturucu**: Yeni test görevleri oluşturma
- **TestRail**: TestRail entegrasyonu
- **iCard**: iCard entegrasyonu
- **Yönetici Paneli**: Yönetici işlevleri

## 4. Teknik Gereksinimler

### 4.1 Sistem Gereksinimleri

- **İşletim Sistemi**: Windows 10 veya üzeri
- **.NET Framework**: Uygun sürüm (proje dosyalarında belirtilen)
- **WebView2 Runtime**: Modern web arayüzü için
- **Mobil Cihaz Sürücüleri**: Android ve iOS cihazlar için

### 4.2 Bağımlılıklar

- **MySQL**: Veritabanı bağlantısı için
- **log4net**: Loglama için
- **Google API**: Google servisleri için
- **TestRail API**: TestRail entegrasyonu için
- **Redmine API**: Redmine entegrasyonu için

### 4.3 Güvenlik Gereksinimleri

- **LDAP Kimlik Doğrulama**: Güvenli kullanıcı girişi
- **Şifrelenmiş Yapılandırma**: Hassas bilgilerin korunması
- **Tek Örnek Çalışma**: Mutex ile uygulama çakışmalarını önleme

## 5. Entegrasyon Noktaları

### 5.1 Harici Servisler

- **TestRail**: Test senaryoları ve sonuçları için
- **Redmine**: Hata takibi ve proje yönetimi için
- **Google API**: Google servisleri için
- **FTP Sunucusu**: Dosya transferi ve güncellemeler için
- **MySQL Veritabanı**: Veri depolama için

### 5.2 Mobil Cihaz Entegrasyonu

- **ADB (Android Debug Bridge)**: Android cihazlarla iletişim
- **iOS Araçları**: iOS cihazlarla iletişim (idevice araçları)
- **NVIDIA ShadowPlay**: Ekran kaydı için

## 6. Performans Gereksinimleri

- **Hızlı Başlatma**: Uygulama 5 saniye içinde başlamalı
- **Düşük Kaynak Kullanımı**: Sistem kaynaklarını verimli kullanma
- **Eşzamanlı İşlemler**: Birden fazla işlemi aynı anda yürütebilme

## 7. Bakım ve Destek

- **Otomatik Güncelleme**: Yeni sürümlerin otomatik dağıtımı
- **Log Dosyaları**: Sorun giderme için detaylı loglar
- **Hata Raporlama**: Kullanıcı geri bildirimleri için mekanizmalar

## 8. Gelecek Geliştirmeler

- **Otomasyon Testleri**: Test senaryolarının otomatize edilmesi
- **Bulut Entegrasyonu**: Uzak cihazlarda test yapabilme
- **Genişletilmiş Analitik**: Daha detaylı test sonuçları ve analizler
- **Çoklu Dil Desteği**: Farklı dil seçenekleri

## 9. Ekler

### 9.1 Terimler Sözlüğü

- **APK**: Android Uygulama Paketi
- **IPA**: iOS Uygulama Paketi
- **ADB**: Android Debug Bridge
- **Logcat**: Android log toplama aracı
- **TestRail**: Test yönetim platformu
- **Redmine**: Proje yönetim ve hata takip sistemi

### 9.2 İlgili Dokümanlar

- Kullanıcı Kılavuzu
- Kurulum Kılavuzu
- API Dokümantasyonu
- Hata Ayıklama Kılavuzu