﻿<?xml version="1.0" encoding="utf-8" ?>
<log4net>
  <!-- Konsola loglama için appender -->
  <appender name="ConsoleAppender" type="log4net.Appender.ConsoleAppender">
    <layout type="log4net.Layout.PatternLayout">
      <conversionPattern value="%date [%thread] %-5level %logger - %message%newline" />
    </layout>
  </appender>

  <!-- Dosyaya loglama için appender -->
  <appender name="FileAppender" type="log4net.Appender.RollingFileAppender">
    <datePattern value="yyyy-MM-dd&quot;.log&quot;" />
    <file value="applog\utilityLog-" />
    <appendToFile value="true" />
    <rollingStyle value="Date" />
    <staticLogFileName value="false" />
    <layout type="log4net.Layout.PatternLayout">
      <conversionPattern value="%date{yyyy-MM-dd HH:mm:ss} %-5level %logger - %message%newline" />
    </layout>
  </appender>

  <!-- Root logger tanımı -->
  <root>
    <level value="DEBUG" />
    <appender-ref ref="ConsoleAppender" />
    <appender-ref ref="FileAppender" />
  </root>
</log4net>
