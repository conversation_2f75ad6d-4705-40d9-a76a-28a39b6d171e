/**
 * <PERSON><PERSON><PERSON> menüsünün konumunu ekranın sınırları içinde kalacak şekilde ayarlar
 * @param {HTMLElement} contextMenu - Bağlam menüsü elementi
 * @param {number} x - Fare tıklama X koordinatı
 * @param {number} y - Fare tıklama Y koordinatı
 */
function positionContextMenu(contextMenu, x, y) {
    if (!contextMenu) return;

    // Önce menüyü görünür hale getir ama pozisyonunu ayarlama
    // Bu, boyutlarını doğru şekilde alabilmemiz için gerekli
    contextMenu.style.visibility = 'hidden';
    contextMenu.classList.remove('hidden');

    // Menünün boyutlarını al
    const menuWidth = contextMenu.offsetWidth;
    const menuHeight = contextMenu.offsetHeight;

    // Menüyü tekrar gizle
    contextMenu.classList.add('hidden');
    contextMenu.style.visibility = 'visible';

    // Ekranın boyutlarını al
    const windowWidth = window.innerWidth;
    const windowHeight = window.innerHeight;

    // Scroll pozisyonlarını al
    const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft;
    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

    // Menünün sağa taşıp taşmadığını kontrol et
    if (x + menuWidth > windowWidth + scrollLeft) {
        x = x - menuWidth; // Menüyü tıklama noktasının soluna yerleştir

        // Eğer hala taşıyorsa, en sağa sabitle
        if (x < scrollLeft) {
            x = windowWidth + scrollLeft - menuWidth - 10; // 10px güvenlik marjı
        }
    }

    // Menünün aşağıya taşıp taşmadığını kontrol et
    if (y + menuHeight > windowHeight + scrollTop) {
        y = y - menuHeight; // Menüyü tıklama noktasının üstüne yerleştir

        // Eğer hala taşıyorsa, en alta sabitle
        if (y < scrollTop) {
            y = windowHeight + scrollTop - menuHeight - 10; // 10px güvenlik marjı
        }
    }

    // Menünün soldan taşıp taşmadığını kontrol et
    if (x < scrollLeft) {
        x = scrollLeft + 10; // 10px güvenlik marjı
    }

    // Menünün yukarıdan taşıp taşmadığını kontrol et
    if (y < scrollTop) {
        y = scrollTop + 10; // 10px güvenlik marjı
    }

    // Menünün konumunu ayarla
    contextMenu.style.top = y + 'px';
    contextMenu.style.left = x + 'px';
}
