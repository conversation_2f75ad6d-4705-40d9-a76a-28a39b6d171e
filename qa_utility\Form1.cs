using MySql.Data.MySqlClient;
using Redmine.Net.Api.Types;
using System.Collections.Specialized;
using System.ComponentModel;
using System.Data;
using System.Diagnostics;
using System.Net;
using System.Security.Cryptography;
using System.Text;
using System.Text.RegularExpressions;
using File = System.IO.File;
using log4net;
using log4net.Config;
using System.IO;
using System.Linq;
using System.Net.Http;
using QA_Utility.classes;
using QA_Utility.classes.TestRail;
using System.Configuration;
using System.Text.Json;
using Google.Protobuf.WellKnownTypes;
using QA_Utility.Helpers;
using Microsoft.Web.WebView2.WinForms;
using Microsoft.Web.WebView2.Core;
using System.Printing;
using Mysqlx.Cursor;
using MedianovaApi;
using System.Collections;
using Redmine.Net.Api.Async;
using qa_utility; // ApkUploaderForm sınıfına erişim için eklendi

// some adb commands
// adb shell pm list packages
// adb shell pm list packages -f
// adb shell dumpsys gfxinfo package-name framestats
// adb shell dumpsys meminfo package_name|pid [-d]
// adb shell am start -a android.intent.action.VIEW -d 'market://details?id=com.flatgames.cardetail'
// adb shell pm list packages -f -3
// admin size : 679; 535
// user size : 679; 379

namespace QA_Utility
{    public partial class Form1 : Form
    {
        private WebView2 webView;
        readonly redmine redmine;
        private readonly TestRailClient testRailClient;
        // Consolidated Google API service
        private readonly bool debugMode = false;
        private readonly Process adbLogger = new();
        private Process logcatProcess = new();
        private bool isDeviceConnected, tasksisloaded = false;
        private string logcatPath;
        private bool videoRecorLoop = false;
        // F5 tuşunu engellemek için kullanılan yardımcı sınıf
        [System.Runtime.InteropServices.ComVisible(true)]
        public class PreventRefreshHelper
        {
            // Boş sınıf, sadece JavaScript'e erişim için
        }
        private int count = 0;
        private bool fromloaded;
        private int clickcount;
        private Size adminSize = new(931, 606);
        private Size userSize = new(816, 606);
        DataTable googleodemeleridatatable = new();
        DataTable allGamesDatas = new();
        DataTable testerList = new();
        DataTable localLogDataTable = new();
        DataTable hasTable = new(); // Dosya hash verilerini saklamak için DataTable
        string ftpServer = ConfigurationManager.AppSettings["FtpServer"];
        string ftpUsername = ConfigurationManager.AppSettings["FtpUsername"];
        string ftpPassword = ConfigurationManager.AppSettings["FtpPassword"];
        string medianovaclient = ConfigurationManager.AppSettings["MediaNovaKey"];
        string medianovasecret = ConfigurationManager.AppSettings["MediaNovaSecret"];
        MySqlConnection coon = new MySqlConnection(ConfigurationManager.AppSettings["DbConnectionString"]);
        public static readonly ILog logger = LogManager.GetLogger(typeof(Form1));
        // UI state değişkenleri
        private string selectedGame = "!Seçilmedi";
        private string selectedOS = "AOS";
        private bool isAlwaysTop;
        private bool isAutoBundle;
        private string currentBundleId;
        private string currentVersion;
        private string currentBuildNumber;
        private string currentDeviceStatus;
        private DeviceInfo currentDeviceInfo;
        private bool logStatus, aosPanelVisible, iosPanelVisible = false;
        private bool isWirelessConnection = false;
        private string folderLocation = "/sdcard/qadr_utility";
        // Blacklist tanımlama - kontrol edilmeyecek ve DataGridView'e eklenmeyecek öğeler
        List<string> blacklist = new List<string>
                {
                    "token.json",
                    "WebView2Cache",
                    "logs",
                    "AutoUpdater.deps.json",
                    "AutoUpdater.dll",
                    "AutoUpdater.dll.config",
                    "AutoUpdater.exe",
                    "AutoUpdater.pdb",
                    "AutoUpdater.runtimeconfig.json",
                    "BouncyCastle.Cryptography.dll",
                    "Google.Protobuf.dll",
                    "K4os.Compression.LZ4.dll",
                    "K4os.Compression.LZ4.Streams.dll",
                    "K4os.Hash.xxHash.dll",
                    "log4net.config",
                    "log4net.dll",
                    "MySql.Data.dll",
                    "System.Configuration.ConfigurationManager.dll",
                    "System.Diagnostics.DiagnosticSource.dll",
                    "System.IO.Pipelines.dll",
                    "System.Security.Cryptography.ProtectedData.dll",
                    "System.Security.Permissions.dll",
                    "System.Text.Encoding.CodePages.dll",
                    "System.Text.Encodings.Web.dll",
                    "System.Text.Json.dll",
                    "System.Windows.Extensions.dll",
                    "ZstdSharp.dll",
                    "applog",
                    "images",
                    "comerr64.dll",
                    "gssapi64.dll",
                    "k5sprt64.dll",
                    "krb5_64.dll",
                    "krbcc64.dll",
                };
        // Spesifik klasör/dosya kombinasyonları için blacklist
        List<string> specificPathBlacklist = new List<string>
                {
                    "runtimes\\browser\\lib\\net6.0\\System.Text.Encodings.Web.dll",
                    "WebView2Cache",
                };
        // Device info için yardımcı sınıf
        private class DeviceInfo
        {
            public string Brand { get; set; }
            public string Model { get; set; }
            public string Version { get; set; }
            public string Chipset { get; set; }
            public string Cpu { get; set; }
            public string Resolution { get; set; }
            public string Language { get; set; }
        }
        public Form1()
        {
            InitializeComponent();
            InitializeWebView2();

            // Initialize redmine with this Form1 instance
            redmine = new redmine(this);
            testRailClient = new TestRailClient();

            // log4net yapılandırmasını yükle
            XmlConfigurator.Configure(new FileInfo("log4net.config"));
            logger.Info("Uygulama başlatıldı.");

            // Eski WinForms kontrollerini gizle
            //aosPanel.Hide();
            //iosPanelVisible.Hide();
            //devPanel.Hide();
            //tabControl1.Hide();
        }
        // WebView2'den gelen mesajları işleyen method
        private async void WebView_WebMessageReceived(object sender, CoreWebView2WebMessageReceivedEventArgs e)
        {
            try
            {
                // Debug log ekle
                logger.Debug($"Received message: {e.WebMessageAsJson}");

                var jsonMessage = JsonSerializer.Deserialize<JsonElement>(e.WebMessageAsJson);
                string messageType = jsonMessage.GetProperty("type").GetString();

                // Debug log ekle
                logger.Debug($"Message type: {messageType}");
                switch (messageType)
                {
                    case "getCurrentFolderLocation":
                        SendCurrentFolderLocation();
                        break;

                    case "getStorageLocations":
                        Thread storageThread = new Thread(() =>
                        {
                            GetStorageLocations();
                        });
                        storageThread.SetApartmentState(ApartmentState.STA);
                        storageThread.Start();
                        break;

                    case "saveStorageLocation":
                        if (jsonMessage.TryGetProperty("location", out JsonElement locationElement))
                        {
                            SaveStorageLocation(locationElement.GetString());
                        }
                        break;
                        
                    case "testrail":
                        if (jsonMessage.TryGetProperty("action", out JsonElement actionElement))
                        {
                            // Handle TestRail message
                            HandleTestRailMessage(e.WebMessageAsJson);
                        }
                        break;

                    case "uploadApk":
                        OpenApkUploaderForm();
                        break;
                        
                    case "launchApp":
                        logger.Debug("Handling launchApp message");
                        var launchData = jsonMessage.GetProperty("data");
                        await LaunchApplication();
                        break;

                    case "closeApp":
                        logger.Debug("Handling closeApp message");
                        var closeData = jsonMessage.GetProperty("data");
                        await CloseApplication();
                        break;

                    case "viewInStore":
                        logger.Debug("Handling viewInStore message");
                        var storeData = jsonMessage.GetProperty("data");
                        await ViewInStore();
                        break;

                    case "uninstallApp":
                        logger.Debug("Handling uninstallApp message");
                        var uninstallData = jsonMessage.GetProperty("data");
                        await UninstallApplication();
                        break;

                    case "login":
                        var data = jsonMessage.GetProperty("data");
                        string id = data.GetProperty("id").GetString();
                        string password = data.GetProperty("password").GetString();
                        bool rememberMe = data.GetProperty("rememberMe").GetBoolean();
                        await loginAsync(id, password, rememberMe);
                        break;

                    case "setAlwaysTop":
                        isAlwaysTop = jsonMessage.GetProperty("value").GetBoolean();
                        this.TopMost = isAlwaysTop;
                        Properties.Settings.Default.alwaysTop = isAlwaysTop;
                        Properties.Settings.Default.Save();
                        break;

                    case "setAutoBundle":
                        isAutoBundle = jsonMessage.GetProperty("value").GetBoolean();
                        Properties.Settings.Default.autoGetBundle = isAutoBundle;
                        Properties.Settings.Default.Save();
                        break;
                    case "refreshGameList":
                        getAllGameListFromFtp();
                        break;

                    case "gameSelected":
                        selectedGame = jsonMessage.GetProperty("value").GetString();

                        if (!string.IsNullOrEmpty(selectedGame))
                        {
                            currentBundleId = getGameDataFromDataTable(selectedGame, "bundleid");
                            if (!string.IsNullOrEmpty(currentBundleId))
                            {
                                if (aosPanelVisible == true && !currentBundleId.Contains("Seçilmedi"))
                                {
                                    await Task.Run(() =>
                                    {
                                        string tempbundle = currentBundleId;
                                        var bundleInfo = getsBundleInfo(tempbundle);
                                        this.BeginInvoke((Action)(() =>
                                        {
                                            webView.CoreWebView2.PostWebMessageAsJson(JsonSerializer.Serialize(new
                                            {
                                                type = "bundleInfo",
                                                bundleInfo = new
                                                {
                                                    version = bundleInfo.version,
                                                    buildNumber = bundleInfo.versionCode,
                                                    bundleId = currentBundleId,
                                                    targetApi = bundleInfo.targetSdk
                                                }
                                            }));
                                        }));
                                    });
                                }
                            }
                        }
                        break;

                    case "openInternalLink":
                        logger.Debug("Handling openInternalLink message");
                        var internalLinkData = jsonMessage.GetProperty("data");
                        await OpenInternalLink();
                        break;

                    case "clearAppData":
                        logger.Debug("Handling clearAppData message");
                        var clearDataData = jsonMessage.GetProperty("data");
                        await ClearAppData();
                        break;
                    case "startVideoRecording":
                        videoRecords();
                        if (jsonMessage.TryGetProperty("continuousRecording", out JsonElement continuousRecordingElement))
                        {
                            videoRecorLoop = continuousRecordingElement.ValueKind == JsonValueKind.True;
                        }
                        break;
                    case "osSelected":
                        selectedOS = jsonMessage.GetProperty("value").GetString();
                        if (selectedOS == null)
                        {
                            return;
                        }
                        if (selectedOS.ToString() == "AOS")
                        {
                            UpdateDeviceInfo();
                            //aosPanel.Enabled = true;
                            iosPanelVisible = false;
                            if (selectedGame == null)
                            {
                                return;
                            }
                            string AOSbundleID = getGameDataFromDataTable(selectedGame, "bundleid");
                            if (!AOSbundleID.Contains("Seçilmedi"))
                            {

                                //string[] versionbuildnumber = getAppVersion(AOSbundleID);
                                //string version = versionbuildnumber[0];
                                //string versionCode = versionbuildnumber[1];
                                //string targetSdk = versionbuildnumber[2];
                                //label1.Text = "App Version : " + version;
                                //label2.Text = "Build Number : " + versionCode;
                                //lblTagerApi.Text = "Target SDK : " + targetSdk;
                            }
                        }
                        else
                        {
                            aosPanelVisible = false;
                        }
                        break;
                    case "takeScreenshot":
                        getSS(true);
                        break;
                    case "restartDevice":
                        if (selectedOS == "AOS")
                        {
                            aosRestart();
                        }
                        else if (selectedOS == "IOS")
                        {
                            iosRestart();
                        }
                        break;
                    case "refreshAppInfo":
                        refreshAppInfo();
                        break;
                    case "installApk":
                        //string fileName = jsonMessage.GetProperty("fileLocation").GetString();
                        //InstallApk(fileName);
                        break;
                    case "startLogRecording":
                        logBaslatfromHtml(jsonMessage.GetProperty("lognote").GetString());
                        break;
                    case "recordLast30Seconds":
                        NvidiaShadowPlayHelper.CaptureLast30Seconds();
                        break;
                    case "closeUtility":
                        closeUtility();
                        break;
                    case "sendText":
                        sendText(jsonMessage.GetProperty("text").GetString());
                        break;
                    case "tabChanged":
                        {
                            string tab = jsonMessage.GetProperty("tab").GetString();
                            if (tab == "video-ss")
                            {
                                // Video ve SS verileri sekmesi açıldığında ilgili verileri gönder
                                SendVideoSsData();
                            }
                            break;
                        }
                    case "openFile":
                        dosyayiCihazdanTempeGetir(jsonMessage.GetProperty("filename").GetString(), true);
                        break;
                    case "copyFile":
                        string selectedRow = jsonMessage.GetProperty("filename").GetString();
                        string folderPath = Path.Combine(Path.GetTempPath(), "qa_utility");
                        StringCollection strings = new StringCollection();
                        Thread thread = new Thread(() =>
                        {
                            dosyayiCihazdanTempeGetir(selectedRow);
                            strings.Add(Path.Combine(folderPath, selectedRow));
                            Clipboard.SetFileDropList(strings);
                        });
                        thread.SetApartmentState(ApartmentState.STA);
                        thread.Start();
                        break;
                    case "deleteFile":
                        string fileToDelete = jsonMessage.GetProperty("filename").GetString();
                        deleteFunction(fileToDelete);
                        // Silme işleminden sonra video-ss listesini güncelle
                        SendVideoSsData();
                        break;
                    case "copyFiles":
                        string folderPath2 = Path.Combine(Path.GetTempPath(), "qa_utility");
                        StringCollection strings2 = new StringCollection();
                        Thread thread2 = new Thread(() =>
                        {
                            foreach (var filename in jsonMessage.GetProperty("filenames").EnumerateArray())
                            {
                                string file = filename.GetString();
                                dosyayiCihazdanTempeGetir(file);
                                strings2.Add(Path.Combine(folderPath2, file));
                            }
                            Clipboard.SetFileDropList(strings2);
                        });
                        thread2.SetApartmentState(ApartmentState.STA);
                        thread2.Start();
                        break;
                    case "deleteFiles":
                        foreach (var filename in jsonMessage.GetProperty("filenames").EnumerateArray())
                        {
                            string file = filename.GetString();
                            deleteFunction(file);
                        }
                        // Silme işleminden sonra video-ss listesini güncelle
                        SendVideoSsData();
                        break;
                    case "uploadToDrive":
                        await UploadFilesToDrive(jsonMessage.GetProperty("filenames"), jsonMessage.GetProperty("folderId").GetString());
                        break;
                    case "getDriveFolders":
                        await GetDriveFolders(jsonMessage.GetProperty("parentId").GetString());
                        break;
                    case "getQaTasks":
                        await GetQaTasks();
                        break;

                    case "getStudioQATasks":
                        await GetStudioQATasks();
                        break;

                    case "getAllTasks":
                        await GetAllTasks();
                        break;

                    case "getQaTaskStatuses":
                        await GetQaTaskStatuses();
                        break;

                    case "getQaTaskPriorities":
                        await GetQaTaskPriorities();
                        break;

                    case "getQaTaskUsers":
                        await GetQaTaskUsers();
                        break;

                    case "updateQaTaskStatus":
                        var statusData = jsonMessage.GetProperty("data");
                        string taskId = statusData.GetProperty("taskId").GetString();
                        string statusId = statusData.GetProperty("statusId").GetString();
                        bool refreshAfterStatusUpdate = false;
                        if (jsonMessage.TryGetProperty("refreshList", out JsonElement refreshElement))
                        {
                            refreshAfterStatusUpdate = refreshElement.GetBoolean();
                        }
                        await UpdateQaTaskStatus(taskId, statusId, refreshAfterStatusUpdate);
                        break;

                    case "updateQaTask":
                        var taskData = jsonMessage.GetProperty("data");
                        bool refreshAfterTaskUpdate = false;
                        if (jsonMessage.TryGetProperty("refreshList", out JsonElement refreshTaskElement))
                        {
                            refreshAfterTaskUpdate = refreshTaskElement.GetBoolean();
                        }
                        await UpdateQaTask(taskData, refreshAfterTaskUpdate);
                        break;

                    case "addQaTaskComment":
                        var commentData = jsonMessage.GetProperty("data");
                        string commentTaskId = commentData.GetProperty("taskId").GetString();
                        string comment = commentData.GetProperty("comment").GetString();
                        bool refreshAfterComment = false;
                        if (jsonMessage.TryGetProperty("refreshList", out JsonElement refreshCommentElement))
                        {
                            refreshAfterComment = refreshCommentElement.GetBoolean();
                        }
                        await AddQaTaskComment(commentTaskId, comment, refreshAfterComment);
                        break;

                    case "updateAllTaskStatus":
                        var allStatusData = jsonMessage.GetProperty("data");
                        string allTaskId = allStatusData.GetProperty("taskId").GetString();
                        string allStatusId = allStatusData.GetProperty("statusId").GetString();
                        bool refreshAfterAllStatusUpdate = false;
                        if (jsonMessage.TryGetProperty("refreshList", out JsonElement refreshAllElement))
                        {
                            refreshAfterAllStatusUpdate = refreshAllElement.GetBoolean();
                        }
                        await UpdateAllTaskStatus(allTaskId, allStatusId, refreshAfterAllStatusUpdate);
                        break;

                    case "updateStudioQATaskStatus":
                        var studioQAStatusData = jsonMessage.GetProperty("data");
                        string studioQATaskId = studioQAStatusData.GetProperty("taskId").GetString();
                        string studioQAStatusId = studioQAStatusData.GetProperty("statusId").GetString();
                        bool refreshAfterStudioQAStatusUpdate = false;
                        if (jsonMessage.TryGetProperty("refreshList", out JsonElement refreshStudioQAElement))
                        {
                            refreshAfterStudioQAStatusUpdate = refreshStudioQAElement.GetBoolean();
                        }
                        await UpdateStudioQATaskStatus(studioQATaskId, studioQAStatusId, refreshAfterStudioQAStatusUpdate);
                        break;

                    case "updateAllTask":
                        var allTaskData = jsonMessage.GetProperty("data");
                        bool refreshAfterAllTaskUpdate = false;
                        if (jsonMessage.TryGetProperty("refreshList", out JsonElement refreshAllTaskElement))
                        {
                            refreshAfterAllTaskUpdate = refreshAllTaskElement.GetBoolean();
                        }
                        await UpdateAllTask(allTaskData, refreshAfterAllTaskUpdate);
                        break;

                    case "addAllTaskComment":
                        var allCommentData = jsonMessage.GetProperty("data");
                        string allCommentTaskId = allCommentData.GetProperty("taskId").GetString();
                        string allComment = allCommentData.GetProperty("comment").GetString();
                        bool refreshAfterAllComment = false;
                        if (jsonMessage.TryGetProperty("refreshList", out JsonElement refreshAllCommentElement))
                        {
                            refreshAfterAllComment = refreshAllCommentElement.GetBoolean();
                        }
                        await AddAllTaskComment(allCommentTaskId, allComment, refreshAfterAllComment);
                        break;

                    case "createDriveFolder":
                        bool isNested = false;
                        int currentIndex = 0;
                        int totalParts = 0;
                        bool uploadAfterCreate = false;
                        List<string> filesToUpload = null;

                        if (jsonMessage.TryGetProperty("isNested", out JsonElement isNestedElement))
                        {
                            isNested = isNestedElement.GetBoolean();
                        }

                        if (jsonMessage.TryGetProperty("currentIndex", out JsonElement currentIndexElement))
                        {
                            currentIndex = currentIndexElement.GetInt32();
                        }

                        if (jsonMessage.TryGetProperty("totalParts", out JsonElement totalPartsElement))
                        {
                            totalParts = totalPartsElement.GetInt32();
                        }

                        if (jsonMessage.TryGetProperty("uploadAfterCreate", out JsonElement uploadAfterCreateElement))
                        {
                            uploadAfterCreate = uploadAfterCreateElement.GetBoolean();
                        }

                        if (jsonMessage.TryGetProperty("filesToUpload", out JsonElement filesToUploadElement) && filesToUploadElement.ValueKind == JsonValueKind.Array)
                        {
                            filesToUpload = new List<string>();
                            foreach (var file in filesToUploadElement.EnumerateArray())
                            {
                                filesToUpload.Add(file.GetString());
                            }
                        }

                        await CreateDriveFolder(
                            jsonMessage.GetProperty("parentId").GetString(),
                            jsonMessage.GetProperty("folderName").GetString(),
                            isNested,
                            currentIndex,
                            totalParts,
                            uploadAfterCreate,
                            filesToUpload);
                        break;

                    case "createNestedFolders":
                        string parentId = jsonMessage.GetProperty("parentId").GetString();
                        string folderPaths = jsonMessage.GetProperty("folderPath").GetString();
                        List<string> filesToUploadNested = null;

                        if (jsonMessage.TryGetProperty("filesToUpload", out JsonElement filesToUploadNestedElement) && filesToUploadNestedElement.ValueKind == JsonValueKind.Array)
                        {
                            filesToUploadNested = new List<string>();
                            foreach (var file in filesToUploadNestedElement.EnumerateArray())
                            {
                                filesToUploadNested.Add(file.GetString());
                            }
                        }

                        await CreateNestedFolders(parentId, folderPaths, filesToUploadNested);
                        break;
                    case "createTaskWithDetails":
                        try
                        {
                            var datavariable = jsonMessage.GetProperty("data");
                            string buildNumber = datavariable.GetProperty("buildNumber").GetString();
                            string game = datavariable.GetProperty("game").GetString();
                            string os = datavariable.GetProperty("os").GetString();
                            string type = datavariable.GetProperty("type").GetString();
                            string version = datavariable.GetProperty("version").GetString();

                            // Check if a valid game is selected
                            if (string.IsNullOrEmpty(game) || game.Contains("Seçilmedi"))
                            {
                                notificationSendToHtml("Lütfen geçerli bir oyun seçin!", "error");
                                return;
                            }

                            string taskName = game + " " + os + " " + version + " (" + buildNumber + ")";
                            if (type == "studioQA")
                            {
                                redmine.createTask(game, taskName, 39);
                                notificationSendToHtml("Task başarıyla oluşturuldu.", "success");
                            }
                            else if (type == "liveTest")
                            {
                                redmine.createTask(game, taskName, 41);
                                notificationSendToHtml("Task başarıyla oluşturuldu.", "success");
                            }
                        }
                        catch (Exception ex)
                        {
                            logger.Error($"Task oluşturma hatası: {ex.Message}");
                            notificationSendToHtml($"Task oluşturulurken hata: {ex.Message}", "error");
                        }
                        break;
                    case "savePayment":
                        var savePaymentDatas = jsonMessage.GetProperty("data");
                        string savePaymentGame = savePaymentDatas.GetProperty("game").GetString();
                        var savePaymentDetails = savePaymentDatas.GetProperty("details").GetString();
                        // "test\ntest\ntest\ntest\ntest\n1234124"
                        //"GPA.3389-2785-7539-44112\nGPA.3359-0793-6524-32698"
                        var saveallpayments = savePaymentDetails.Split("\n");

                        StringBuilder sb = new StringBuilder();
                        string savePaymentbundleid = getGameDataFromDataTable(savePaymentGame, "bundleid");

                        foreach (var payment in saveallpayments)
                        {
                            if (payment.Length > 5)
                            {
                                sb.Append("INSERT IGNORE INTO googleodemeleri (appname, tester, bundleid, odemepin, status) VALUES (");
                                sb.AppendFormat("'{0}', '{1}', '{2}', '{3}', '{4}');",
                                    MySqlHelper.EscapeString(savePaymentGame),
                                    MySqlHelper.EscapeString(Properties.Settings.Default.testername),
                                    MySqlHelper.EscapeString(savePaymentbundleid),
                                    MySqlHelper.EscapeString(payment.Trim()),
                                    "Bekliyor");

                            }
                        }
                        Console.Write(savePaymentDatas);


                        runSql(sb.ToString());

                        break;
                    case "getPayments":
                        string jsonData = GetPaymentsJson();
                        webView.CoreWebView2.PostWebMessageAsJson(jsonData);
                        break;
                    case "deletePayments":
                        if (true)
                        {
                            var refundPaymentsDatas = jsonMessage.GetProperty("data");
                            var refundPaymentstDetails = JsonSerializer.Deserialize<PaymentData>(refundPaymentsDatas);
                            StringBuilder refundsb = new StringBuilder();
                            foreach (var payment in refundPaymentstDetails.payments)
                            {
                                int refundid = payment.id;
                                string status = payment.status;
                                string tester = payment.tester;
                                if (((status == "Bekliyor" || status == "İşlem Başarısız") && tester == Properties.Settings.Default.testername) || adminse())
                                {

                                    refundsb.AppendFormat("DELETE FROM googleodemeleri WHERE id = '{0}';", refundid);
                                }
                            }
                            runSql(refundsb.ToString());
                        }
                        break;
                    case "refundPayments":
                        if (adminse())
                        {
                            var refundPaymentsDatas = jsonMessage.GetProperty("data");
                            var refundPaymentstDetails = JsonSerializer.Deserialize<PaymentData>(refundPaymentsDatas);
                            StringBuilder refundsb = new StringBuilder();
                            bool mesajgonderildimi = false;
                            string slackWebhook = "*********************************************************************************";

                            foreach (var payment in refundPaymentstDetails.payments)
                            {
                                try
                                {
                                    int refundid = payment.id;
                                    string paymentPin = payment.paymentPin;
                                    string bundleId = payment.bundleId;
                                    string appName = payment.appName;
                                    string tester = payment.tester;

                                    // Her ödeme için sonucu JavaScript'e bildir - Yeni GoogleApiService sınıfını kullan
                                    var result = await GoogleApiService.RefundPayment(paymentPin, bundleId);
                                    var response = new
                                    {
                                        type = "refundStatus",
                                        paymentId = refundid,
                                        status = result.isSuccess ? "success" : "error",
                                        message = result.message
                                    };

                                    if (result.isSuccess)
                                    {
                                        if (!mesajgonderildimi)
                                        {
                                            mesajgonderildimi = true;
                                        }
                                        refundsb.AppendFormat("UPDATE googleodemeleri set status ='{1}' WHERE id = '{0}';", refundid, "Tamamlandı");

                                        var fields = new[]
                                        {
                                            new { name = "Oyun Adı", value = appName, inline = false },
                                            new { name = "Tester", value = tester, inline = false },
                                            new { name = "GPA", value = paymentPin, inline = false }
                                        };

                                        await SendMessageToSlack(slackWebhook, fields, "Geri Ödeme Yapıldı");

                                        webView.CoreWebView2.PostWebMessageAsJson(JsonSerializer.Serialize(response));
                                    }
                                    else
                                    {
                                        refundsb.AppendFormat("UPDATE googleodemeleri set status ='{1}' WHERE id = '{0}';", refundid, "İşlem Başarısız");
                                    }
                                }
                                catch (Exception ex)
                                {
                                    logger.Error($"Payment refund error: {ex}");
                                    var errorResponse = new
                                    {
                                        type = "refundStatus",
                                        paymentId = payment.id,
                                        status = "error",
                                        message = "İşlem sırasında bir hata oluştu"
                                    };
                                    refundsb.AppendFormat("UPDATE googleodemeleri set status ='{1}' WHERE id = '{0}';", payment.id, "İşlem Başarısız");
                                    webView.CoreWebView2.PostWebMessageAsJson(JsonSerializer.Serialize(errorResponse));
                                }
                            }

                            if (refundsb.Length > 0)
                            {
                                runSql(refundsb.ToString());
                            }
                        }
                        break;
                    case "purgeMediaNova":
                        await mediaNovaPurger();
                        notificationSendToHtml("MediaNova cache purge completed");
                        break;
                    case "setContinuousRecording":
                        videoRecorLoop = jsonMessage.GetProperty("value").GetBoolean();
                        break;
                    case "switchToWireless":
                        wificonnection();
                        break;
                    case "disconnectWireless":
                        disconnectWireless();
                        break;
                    case "killAdbServer":
                        killAdbServer();
                        break;
                    case "getAllLocalLogFiles":
                        await GetAllLocalLogFiles();
                        break;
                    case "openLocalLogFile":
                        if (jsonMessage.TryGetProperty("filename", out JsonElement filenameElement))
                        {
                            string filename = filenameElement.GetString();
                            OpenLocalLogFile(filename);
                        }
                        break;
                    case "deleteLocalLogFile":
                        if (jsonMessage.TryGetProperty("filename", out JsonElement deleteFilenameElement))
                        {
                            string filename = deleteFilenameElement.GetString();
                            DeleteLocalLogFile(filename);
                        }
                        break;
                    case "uploadLogFileToFtp":
                        if (jsonMessage.TryGetProperty("filename", out JsonElement uploadFilenameElement))
                        {
                            string filename = uploadFilenameElement.GetString();
                            await UploadLogFileToFtp(filename);
                        }
                        break;
                    case "uploadMultipleLogsToFtp":
                        if (jsonMessage.TryGetProperty("filenames", out JsonElement uploadFilenamesElement))
                        {
                            await UploadMultipleLogsToFtp(uploadFilenamesElement);
                        }
                        break;
                    case "deleteMultipleLocalLogFiles":
                        if (jsonMessage.TryGetProperty("filenames", out JsonElement deleteFilenamesElement))
                        {
                            DeleteMultipleLocalLogFiles(deleteFilenamesElement);
                        }
                        break;
                    case "openLocalLogFileLocation":
                        if (jsonMessage.TryGetProperty("filename", out JsonElement locationFilenameElement))
                        {
                            string filename = locationFilenameElement.GetString();
                            OpenLocalLogFileLocation(filename);
                        }
                        break;
                    case "copyDeviceInfo":
                        if (jsonMessage.TryGetProperty("data", out JsonElement deviceInfoElement))
                        {
                            string deviceInfo = deviceInfoElement.GetString();
                            Clipboard.SetText(deviceInfo);
                            logger.Debug("Device info copied to clipboard");
                        }
                        break;
                    case "getFileHashes":
                        await SendFileHashesToWebView();
                        break;
                    case "checkFiles":
                        await CheckFilesForChanges();
                        break;
                    case "saveHashes":
                        SaveHashChanges();
                        break;
                    case "resetHashTable":
                        ResetHashTable();
                        break;
                    case "updateHashTable":
                        if (jsonMessage.TryGetProperty("data", out JsonElement hashDataElement))
                        {
                            UpdateHashTableFromJson(hashDataElement);
                        }
                        break;
                    case "getAllGamesData":
                        await SendAllGamesDataToWebView();
                        break;
                    case "getAllTestersFromSql":
                        await SendAllTestersDataToWebView();
                        break;
                    case "getVersions":
                        await SendVersionDataToWebView();
                        break;
                    case "updateVersion":
                        string app = jsonMessage.GetProperty("app").GetString();
                        string uversion = jsonMessage.GetProperty("version").GetString();
                        UpdateVersionInDatabase(app, uversion);
                        break;
                    case "getAllStudioQAData":
                        await SendStudioQADataToWebView();
                        break;
                    case "addStudioQA":
                        string newTester = jsonMessage.GetProperty("tester").GetString();
                        string newPriority1 = jsonMessage.GetProperty("priority1").GetString();
                        string newPriority2 = jsonMessage.GetProperty("priority2").GetString();
                        string newPriority3 = jsonMessage.GetProperty("priority3").GetString();
                        string newPriority4 = jsonMessage.GetProperty("priority4").GetString();
                        string newPriority5 = jsonMessage.GetProperty("priority5").GetString();
                        string newPriority6 = jsonMessage.GetProperty("priority6").GetString();
                        AddStudioQAToDatabase(newTester, newPriority1, newPriority2, newPriority3, newPriority4, newPriority5, newPriority6);
                        break;
                    case "updateStudioQA":
                        string updateId = jsonMessage.GetProperty("id").GetString();
                        string updateTester = jsonMessage.GetProperty("tester").GetString();
                        string updatePriority1 = jsonMessage.GetProperty("priority1").GetString();
                        string updatePriority2 = jsonMessage.GetProperty("priority2").GetString();
                        string updatePriority3 = jsonMessage.GetProperty("priority3").GetString();
                        string updatePriority4 = jsonMessage.GetProperty("priority4").GetString();
                        string updatePriority5 = jsonMessage.GetProperty("priority5").GetString();
                        string updatePriority6 = jsonMessage.GetProperty("priority6").GetString();
                        UpdateStudioQAInDatabase(updateId, updateTester, updatePriority1, updatePriority2, updatePriority3, updatePriority4, updatePriority5, updatePriority6);
                        break;
                    case "deleteStudioQA":
                        string deleteId = jsonMessage.GetProperty("id").GetString();
                        DeleteStudioQAFromDatabase(deleteId);
                        break;
                    case "getDataViewerFiles":
                        await SendDataViewerFilesToWebView();
                        break;
                    case "downloadAndOpenFile":
                        string fileName = jsonMessage.GetProperty("fileName").GetString();
                        DownloadAndOpenFile(fileName);
                        break;
                    case "archiveFiles":
                        var fileNamesArray = jsonMessage.GetProperty("fileNames").EnumerateArray();
                        List<string> fileNames = new List<string>();
                        foreach (var fileNameElement in fileNamesArray)
                        {
                            fileNames.Add(fileNameElement.GetString());
                        }
                        ArchiveFiles(fileNames);
                        break;
                    case "addUser":
                        if (adminse())
                        {
                            string username = jsonMessage.GetProperty("user").GetString();
                            string role = jsonMessage.GetProperty("role").GetString();
                            await AddUser(username, role);
                        }
                        break;
                    case "updateUser":
                        if (adminse())
                        {
                            int usid = int.Parse(jsonMessage.GetProperty("id").GetString());
                            string username = jsonMessage.GetProperty("user").GetString();
                            string role = jsonMessage.GetProperty("role").GetString();
                            await UpdateUser(usid, username, role);
                        }
                        break;
                    case "deleteUser":
                        if (adminse())
                        {
                            int duid = int.Parse(jsonMessage.GetProperty("id").GetString());
                            await DeleteUser(duid);
                        }
                        break;
                    case "viewInStore2":
                        if (jsonMessage.TryGetProperty("data", out JsonElement storeDataElement))
                        {
                            if (storeDataElement.TryGetProperty("game", out JsonElement gameElement) && storeDataElement.TryGetProperty("os", out JsonElement osElement))
                            {
                                ViewGameInStore(gameElement.GetString(), osElement.GetString());
                            }
                        }
                        break;

                    case "openUrl":
                        if (jsonMessage.TryGetProperty("url", out JsonElement urlElement))
                        {
                            string url = urlElement.GetString();
                            if (!string.IsNullOrEmpty(url))
                            {
                                try
                                {
                                    // URL'i tarayıcıda aç
                                    Process.Start(new ProcessStartInfo
                                    {
                                        FileName = url,
                                        UseShellExecute = true
                                    });
                                    notificationSendToHtml("URL tarayıcıda açıldı.");
                                }
                                catch (Exception ex)
                                {
                                    logger.Error($"URL açılırken hata oluştu: {ex.Message}");
                                    notificationSendToHtml($"URL açılırken hata oluştu: {ex.Message}", "error");
                                }
                            }
                        }
                        break;
                    case "addGame":
                        if (jsonMessage.TryGetProperty("data", out JsonElement addGameDataElement) && adminse())
                        {
                            AddGame(addGameDataElement);
                        }
                        break;
                    case "updateGame":
                        if (jsonMessage.TryGetProperty("data", out JsonElement updateGameDataElement) && adminse())
                        {
                            UpdateGame(updateGameDataElement);
                        }
                        break;
                    case "deleteGame":
                        if (jsonMessage.TryGetProperty("name", out JsonElement deleteGameNameElement) && adminse())
                        {
                            string gameName = deleteGameNameElement.GetString();
                            DeleteGame(gameName);
                        }
                        break;
                    case "copyToClipboard":
                        if (jsonMessage.TryGetProperty("text", out JsonElement textElement))
                        {
                            string textToCopy = textElement.GetString();
                            // Clipboard işlemleri için STA thread gerekli
                            Thread threads = new Thread(() =>
                            {
                                try
                                {
                                    Clipboard.SetText(textToCopy);
                                    logger.Debug($"Text copied to clipboard: {textToCopy}");
                                    notificationSendToHtml("Metin panoya kopyalandı.");
                                }
                                catch (Exception ex)
                                {
                                    logger.Error($"Error copying to clipboard: {ex.Message}");
                                    notificationSendToHtml("Panoya kopyalama sırasında hata oluştu.", "error");
                                }
                            });
                            threads.SetApartmentState(ApartmentState.STA);
                            threads.Start();
                        }
                        break;
                    case "runMonkey":
                        monkeyRun();
                        break;
                    case "updatePaymentStatus":
                        if (jsonMessage.TryGetProperty("data", out JsonElement paymentDataElement))
                        {
                            if (paymentDataElement.TryGetProperty("ids", out JsonElement idsElement) && paymentDataElement.TryGetProperty("status", out JsonElement statusElement))
                            {
                                string status = statusElement.GetString();
                                var ids = JsonSerializer.Deserialize<List<int>>(idsElement.GetRawText());
                                UpdatePaymentsStatus(ids, status);
                            }
                        }
                        break;
                }
            }
            catch (Exception ex)
            {
                logger.Error($"WebView message handling error: {ex}");
                MessageBox.Show("İşlem sırasında bir hata oluştu.");
            }
        }
        public class Payment
        {
            public int id { get; set; }
            public string paymentPin { get; set; }
            public string bundleId { get; set; }
            public string appName { get; set; }
            public string tester { get; set; }
            public string status { get; set; }
        }

        // TestRail integration methods

        /// <summary>
        /// Handles TestRail-related messages from the JavaScript
        /// </summary>
        private async void HandleTestRailMessage(string message)
        {
            try
            {
                var json = JsonSerializer.Deserialize<JsonElement>(message);
                
                // Get action from JSON
                string action = json.GetProperty("action").GetString();

                switch (action)
                {
                    case "checkTestRailConnection":
                        await CheckTestRailConnection();
                        break;

                    case "getTestRailProjects":
                        await GetTestRailProjects();
                        break;                    case "getTestRailSuites":
                        int projectId = GetIntFromJsonElement(json.GetProperty("projectId"));
                        await GetTestRailSuites(projectId);
                        break;

                    case "getTestRailCases":
                        projectId = GetIntFromJsonElement(json.GetProperty("projectId"));
                        int? suiteId = null;
                        if (json.TryGetProperty("suiteId", out JsonElement suiteIdElement) && 
                            suiteIdElement.ValueKind != JsonValueKind.Null)
                        {
                            suiteId = GetIntFromJsonElement(suiteIdElement);
                        }
                        await GetTestRailCases(projectId, suiteId);
                        break;                    case "getTestRailCase":
                        int caseId = GetIntFromJsonElement(json.GetProperty("caseId"));
                        await GetTestRailCase(caseId);
                        break;

                    case "openurl":
                        string url = json.GetProperty("url").GetString();
                        Process.Start(new ProcessStartInfo
                        {
                            FileName = url,
                            UseShellExecute = true
                        });
                        break;

                    case "addTestRailResult":
                        int runId = GetIntFromJsonElement(json.GetProperty("runId"));
                        caseId = GetIntFromJsonElement(json.GetProperty("caseId"));                        var resultElement = json.GetProperty("result");
                        await AddTestRailResult(runId, caseId, resultElement);
                        break;                          case "getTestRailRuns":
                              if (json.TryGetProperty("projectId", out JsonElement projectIdElement) && 
                                  projectIdElement.ValueKind != JsonValueKind.Null)
                              {
                                  await GetTestRailRuns(GetIntFromJsonElement(projectIdElement));
                              }
                              break;
                        
                    case "getTestRailRun":
                        if (json.TryGetProperty("runId", out JsonElement runIdElement) && 
                            runIdElement.ValueKind != JsonValueKind.Null)
                        {
                            await GetTestRailRun(GetIntFromJsonElement(runIdElement));
                        }
                        break;
                }
            }catch (Exception ex)
            {
                logger.Error($"Error handling TestRail message: {ex.Message}", ex);
                
                // Send error message back to client
                try
                {
                    await this.Invoke(async () => {
                        webView.CoreWebView2.PostWebMessageAsJson(JsonSerializer.Serialize(new {
                            action = "error",
                            message = $"Error processing TestRail request: {ex.Message}"
                        }));
                    });
                }
                catch (Exception postEx)
                {
                    logger.Error($"Failed to post error message back to client: {postEx.Message}", postEx);
                }
            }
        }        /// <summary>
        /// Gets TestRail runs for a project and returns them to the client
        /// </summary>
        private async Task GetTestRailRuns(int projectId)
        {
            try
            {
                logger.Debug($"Getting TestRail runs for project {projectId}...");
                var runs = await testRailClient.GetRunsAsync(projectId);
                logger.Debug($"Retrieved {runs.Count} TestRail runs");
                
                var jsonOptions = new JsonSerializerOptions
                {
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                };
                
                await this.Invoke(async () => {
                    webView.CoreWebView2.PostWebMessageAsJson(JsonSerializer.Serialize(new {
                        action = "runsLoaded",
                        runs = runs
                    }));
                });
            }
            catch (Exception ex)
            {
                logger.Error($"Error getting TestRail runs: {ex.Message}", ex);
                await this.Invoke(async () => {
                    webView.CoreWebView2.PostWebMessageAsJson(JsonSerializer.Serialize(new {
                        action = "error",
                        message = $"Error loading test runs: {ex.Message}"
                    }));
                });
            }
        }

        /// <summary>
        /// Gets a specific TestRail run and returns it to the client
        /// </summary>
        private async Task GetTestRailRun(int runId)
        {
            try
            {
                logger.Debug($"Getting TestRail run {runId}...");
                var run = await testRailClient.GetRunAsync(runId);
                logger.Debug($"Retrieved TestRail run: {run.Name}");
                
                var jsonOptions = new JsonSerializerOptions
                {
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                };
                
                await this.Invoke(async () => {
                    webView.CoreWebView2.PostWebMessageAsJson(JsonSerializer.Serialize(new {
                        action = "runLoaded",
                        run = run
                    }));
                });
            }
            catch (Exception ex)
            {
                logger.Error($"Error getting TestRail run: {ex.Message}", ex);
                await this.Invoke(async () => {
                    webView.CoreWebView2.PostWebMessageAsJson(JsonSerializer.Serialize(new {
                        action = "error",
                        message = $"Error loading test run details: {ex.Message}"
                    }));
                });
            }
        }

        /// <summary>
        /// Checks TestRail connection and returns the status to the client
        /// </summary>
        private async Task CheckTestRailConnection()
        {
            try
            {
                bool isConnected = await testRailClient.CheckConnectionAsync();
                //string script = $"handleTestRailMessage({{ action: 'connectionStatus', status: {isConnected.ToString().ToLower()} }})";
                await this.Invoke(async () => {
                    webView.CoreWebView2.PostWebMessageAsJson(JsonSerializer.Serialize(new {
                        action = "connectionStatus",
                        status = isConnected.ToString().ToLower()
                    }));
                });
                
            }
            catch (Exception ex)
            {
                logger.Error($"Error checking TestRail connection: {ex.Message}", ex);
                await this.Invoke(async () => {
                    webView.CoreWebView2.PostWebMessageAsJson(JsonSerializer.Serialize(new {
                        action = "connectionStatus",
                        status = false
                    }));
                });
            }
        }

        /// <summary>
        /// Gets TestRail projects and returns them to the client
        /// </summary>
        private async Task GetTestRailProjects()
        {
            try
            {
                logger.Debug("Getting TestRail projects...");
                var projects = await testRailClient.GetProjectsAsync();
                logger.Debug($"Retrieved {projects.Count} TestRail projects");
                
                var jsonOptions = new JsonSerializerOptions
                {
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                };
                
                await this.Invoke(async () => {
                    webView.CoreWebView2.PostWebMessageAsJson(JsonSerializer.Serialize(new {
                        action = "projectsLoaded",
                        projects = projects
                    }));
                });
            }
            catch (Exception ex)
            {
                logger.Error($"Error getting TestRail projects: {ex.Message}", ex);
                
            }
        }

        /// <summary>
        /// Gets TestRail suites for a project and returns them to the client
        /// </summary>
        private async Task GetTestRailSuites(int projectId)
        {
            try
            {
                var suites = await testRailClient.GetSuitesAsync(projectId);
                var jsonOptions = new JsonSerializerOptions
                {
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                };
                string suitesJson = JsonSerializer.Serialize(suites, jsonOptions);
                //string script = $"handleTestRailMessage({{ action: 'suitesLoaded', suites: {suitesJson} }})";
                await this.Invoke(async () => {
                    webView.CoreWebView2.PostWebMessageAsJson(JsonSerializer.Serialize(new {
                        action = "suitesLoaded",
                        suites = suites
                    }));
                });

            }
            catch (Exception ex)
            {
                logger.Error($"Error getting TestRail suites: {ex.Message}", ex);
                
            }
        }

        /// <summary>
        /// Gets TestRail cases for a project and suite and returns them to the client
        /// </summary>
        private async Task GetTestRailCases(int projectId, int? suiteId)
        {
            try
            {
                var cases = await testRailClient.GetCasesAsync(projectId, suiteId);
                var jsonOptions = new JsonSerializerOptions
                {
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                };
                string casesJson = JsonSerializer.Serialize(cases, jsonOptions);
                //string script = $"handleTestRailMessage({{ action: 'casesLoaded', cases: {casesJson} }})";
                await this.Invoke(async () => {
                    webView.CoreWebView2.PostWebMessageAsJson(JsonSerializer.Serialize(new {
                        action = "casesLoaded",
                        cases = cases
                    }));
                });

            }
            catch (Exception ex)
            {
                logger.Error($"Error getting TestRail cases: {ex.Message}", ex);
                
            }
        }

        /// <summary>
        /// Gets a specific TestRail case and returns it to the client
        /// </summary>
        private async Task GetTestRailCase(int caseId)
        {
            try
            {
                var caseDetails = await testRailClient.GetCaseAsync(caseId);
                var jsonOptions = new JsonSerializerOptions
                {
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                };
                string caseJson = JsonSerializer.Serialize(caseDetails, jsonOptions);
                //string script = $"handleTestRailMessage({{ action: 'caseDetails', caseDetails: {caseJson} }})";
                await this.Invoke(async () => {
                    webView.CoreWebView2.PostWebMessageAsJson(JsonSerializer.Serialize(new {
                        action = "caseDetails",
                        caseDetails = caseDetails
                    }));
                });

            }
            catch (Exception ex)
            {
                logger.Error($"Error getting TestRail case: {ex.Message}", ex);
                
            }
        }

        /// <summary>
        /// Adds a test result for a TestRail case and returns the result to the client
        /// </summary>
        private async Task AddTestRailResult(int runId, int caseId, JsonElement resultElement)
        {
            try
            {
                var result = new TestRailResult
                {
                    StatusId = resultElement.GetProperty("statusId").GetInt32(),
                    Comment = resultElement.GetProperty("comment").GetString(),
                    Version = resultElement.GetProperty("version").GetString(),
                    Elapsed = resultElement.GetProperty("elapsed").GetString(),
                    Defects = resultElement.GetProperty("defects").GetString()
                };

                bool success = await testRailClient.AddResultForCaseAsync(runId, caseId, result);
                //string script = $"handleTestRailMessage({{ action: 'resultAdded', success: {success.ToString().ToLower()}, caseId: {caseId} }})";
                await this.Invoke(async () => {
                    webView.CoreWebView2.PostWebMessageAsJson(JsonSerializer.Serialize(new {
                        action = "resultAdded",
                        success = success,
                        caseId = caseId
                    }));
                });

            }
            catch (Exception ex)
            {
                logger.Error($"Error adding TestRail result: {ex.Message}", ex);
                
            }
        }
        public class PaymentData
        {
            public List<Payment> payments { get; set; }
        }

        /// <summary>
        /// Helper method to safely get an integer from a JsonElement regardless of its type
        /// </summary>
        private int GetIntFromJsonElement(JsonElement element)
        {
            switch (element.ValueKind)
            {
                case JsonValueKind.Number:
                    return element.GetInt32();
                case JsonValueKind.String:
                    if (int.TryParse(element.GetString(), out int result))
                    {
                        return result;
                    }
                    break;
            }
            
            // Default case
            throw new InvalidOperationException($"Cannot convert JsonElement of type {element.ValueKind} to integer");
        }
        private void UpdatePaymentsStatus(List<int> ids, string status)
        {
            try
            {
                StringBuilder sb = new StringBuilder();
                foreach (int id in ids)
                {
                    sb.AppendFormat("UPDATE googleodemeleri set status ='{1}' WHERE id = '{0}';", id, status);
                }
                runSql(sb.ToString());
            }
            catch (Exception ex)
            {
                logger.Error($"Payment status update error: {ex}");
            }
        }
        public string GetPaymentsJson()
        {
            try
            {
                string sql = "Select * from googleodemeleri"; // and yetki='" & yetki & "'"
                MySqlDataAdapter verial = new MySqlDataAdapter(sql, coon);
                DataTable dataTable = new DataTable();
                verial.Fill(dataTable);

                var payments = new System.Collections.Generic.List<object>();
                // appname, tester, bundleid, odemepin, status)
                foreach (DataRow row in dataTable.Rows)
                {
                    payments.Add(new
                    {
                        id = Convert.ToInt32(row["id"]),
                        appName = row["appname"].ToString(),
                        tester = row["tester"].ToString(),
                        bundleId = row["bundleid"].ToString(),
                        paymentPin = row["odemepin"].ToString(),
                        status = row["status"].ToString()
                    });
                }

                var jsonResponse = new
                {
                    type = "paymentData",
                    payments = payments
                };

                return JsonSerializer.Serialize(jsonResponse);
            }
            catch (Exception ex)
            {
                Console.WriteLine("Error: " + ex.Message);
                return "{}";
            }
        }
        private void sendText(string metin){
            //adb shell input swipe 900 1000 100 1000 300
            Process kaydir = GetProcess("adb", "shell input swipe 500 1600 500 500");
            kaydir.Start();
            Thread.Sleep(1500);
            metin = metin.Replace("ı", "i");
            metin = metin.Replace("ö", "o");
            metin = metin.Replace("ü", "u");
            metin = metin.Replace("ş", "s");
            metin = metin.Replace("ç", "c");
            metin = metin.Replace("ğ", "g");
            metin = metin.Replace("I", "I");
            metin = metin.Replace("Ö", "O");
            metin = metin.Replace("Ü", "U");
            metin = metin.Replace("ş", "S");
            metin = metin.Replace("Ç", "C");
            metin = metin.Replace("Ğ", "G");
            Process textProcess = GetProcess("adb", "shell input text '" + metin + "'");
            textProcess.Start();
            //adb shell input keyevent 66
            Process oktikla = GetProcess("adb", "shell input keyevent 66");
            oktikla.Start();

        }
        private void closeUtility(){
            Properties.Settings.Default.redmineid = null;
            Properties.Settings.Default.redminepw = null;
            Properties.Settings.Default.testername = null;
            Properties.Settings.Default.Save();
            Process.Start("AutoUpdater.exe");
        }
        private void InstallApk(string fileName)
        {
            Task.Run(async () =>
            {
                await setToolStatusLabel("APK Yükleniyor.", "green", true);
                Process runApp = GetProcess("adb", "install \"" + fileName + "\"");
                runApp.Start();
                runApp.WaitForExit();
                await setToolStatusLabel("APK yüklendi!");
                SendNotificationToDevice("Uygulama Yüklendi", fileName + " uygulaması yüklendi.");
            });
        }
        private void InitializeAdbLogger()
        {
            DisconnectDevice();
            getAllGameListFromFtp();

            adbLogger.StartInfo.FileName = "cmd.exe";
            adbLogger.StartInfo.RedirectStandardInput = true;
            adbLogger.StartInfo.RedirectStandardOutput = true;
            adbLogger.StartInfo.CreateNoWindow = true;
            adbLogger.StartInfo.UseShellExecute = false;
            adbLogger.StartInfo.WorkingDirectory = @"./ioslog";
            adbLogger.OutputDataReceived += new DataReceivedEventHandler(adbLogger_OutputDataReceived);
            adbLogger.Start();
            adbLogger.BeginOutputReadLine();

            string role = "tester";
            if (!string.IsNullOrEmpty(Properties.Settings.Default.testername))
            {
                role = getRole(Properties.Settings.Default.testername);
            }
            if (role == "admin")
            {
                this.Size = adminSize;
                this.MinimumSize = adminSize;
            }
            else
            {
                this.Size = userSize;
                this.MinimumSize = userSize;
            }
            if (string.IsNullOrWhiteSpace(Properties.Settings.Default.testername))
            {                            aosPanelVisible = false;
                            iosPanelVisible = false;
            }
            else
            {
                timer1.Start();
            }
        }
        // QA Task ile ilgili metodlar
        private async Task GetQaTasks()
        {
            try
            {
                if (!redmine.getConnectionStatus())
                {
                    // RedmineManager(host, apiKey)
                    await redmine.connectionAsync(Properties.Settings.Default.redmineid, Properties.Settings.Default.redminepw, true);
                }

                if (!redmine.getConnectionStatus())
                {
                    notificationSendToHtml("Redmine bağlantısı kurulamadı", "error");
                    return;
                }

                // API ile bağlantı için tempConnection oluştur
                var redmineTempManager = await redmine.tempConnectionAsync();

                var issues = await redmine.getAllIssuesForGroupsNameAsync("QA", redmineTempManager);
                if (issues == null || issues.Count == 0)
                {
                    webView.CoreWebView2.PostWebMessageAsJson(JsonSerializer.Serialize(new
                    {
                        type = "qaTasksData",
                        tasks = new List<Issue>()
                    }));
                    return;
                }

                webView.CoreWebView2.PostWebMessageAsJson(JsonSerializer.Serialize(new
                {
                    type = "qaTasksData",
                    tasks = issues
                }));
            }
            catch (Exception ex)
            {
                logger.Error($"QA Task verilerini getirirken hata: {ex.Message}");
                notificationSendToHtml("QA Task verilerini getirirken hata oluştu", "error");
            }
        }
        private async Task GetQaTaskStatuses()
        {
            try
            {
                if (!redmine.getConnectionStatus())
                {
                    await redmine.connectionAsync(Properties.Settings.Default.redmineid, Properties.Settings.Default.redminepw);
                }

                if (!redmine.getConnectionStatus())
                {
                    notificationSendToHtml("Redmine bağlantısı kurulamadı", "error");
                    return;
                }

                var statuses = await redmine.getStatusesAsync();
                if (statuses == null || statuses.Count == 0)
                {
                    webView.CoreWebView2.PostWebMessageAsJson(JsonSerializer.Serialize(new
                    {
                        type = "qaTaskStatusesData",
                        statuses = new List<object>()
                    }));
                    return;
                }

                webView.CoreWebView2.PostWebMessageAsJson(JsonSerializer.Serialize(new
                {
                    type = "qaTaskStatusesData",
                    statuses = statuses
                }));
            }
            catch (Exception ex)
            {
                logger.Error($"QA Task durumlarını getirirken hata: {ex.Message}");
                notificationSendToHtml("QA Task durumlarını getirirken hata oluştu", "error");
            }
        }
        private async Task GetQaTaskPriorities()
        {
            try
            {
                if (!redmine.getConnectionStatus())
                {
                    await redmine.connectionAsync(Properties.Settings.Default.redmineid, Properties.Settings.Default.redminepw);
                }

                if (!redmine.getConnectionStatus())
                {
                    notificationSendToHtml("Redmine bağlantısı kurulamadı", "error");
                    return;
                }

                var priorities = await redmine.getPrioritiesAsync();
                if (priorities == null || priorities.Count == 0)
                {
                    webView.CoreWebView2.PostWebMessageAsJson(JsonSerializer.Serialize(new
                    {
                        type = "qaTaskPrioritiesData",
                        priorities = new List<object>()
                    }));
                    return;
                }

                webView.CoreWebView2.PostWebMessageAsJson(JsonSerializer.Serialize(new
                {
                    type = "qaTaskPrioritiesData",
                    priorities = priorities
                }));
            }
            catch (Exception ex)
            {
                logger.Error($"QA Task önceliklerini getirirken hata: {ex.Message}");
                notificationSendToHtml("QA Task önceliklerini getirirken hata oluştu", "error");
            }
        }
        private async Task GetQaTaskUsers()
        {
            try
            {
                if (!redmine.getConnectionStatus())
                {
                    await redmine.connectionAsync(Properties.Settings.Default.redmineid, Properties.Settings.Default.redminepw);
                }

                if (!redmine.getConnectionStatus())
                {
                    notificationSendToHtml("Redmine bağlantısı kurulamadı", "error");
                    return;
                }

                var users = await redmine.getUsersAsync();
                if (users == null || users.Count == 0)
                {
                    webView.CoreWebView2.PostWebMessageAsJson(JsonSerializer.Serialize(new
                    {
                        type = "qaTaskUsersData",
                        users = new List<object>()
                    }));
                    return;
                }

                webView.CoreWebView2.PostWebMessageAsJson(JsonSerializer.Serialize(new
                {
                    type = "qaTaskUsersData",
                    users = users
                }));
            }
            catch (Exception ex)
            {
                logger.Error($"QA Task kullanıcılarını getirirken hata: {ex.Message}");
                notificationSendToHtml("QA Task kullanıcılarını getirirken hata oluştu", "error");
            }
        }
        private async Task UpdateQaTaskStatus(string taskId, string statusId, bool refreshTaskList = false)
        {
            try
            {
                if (!redmine.getConnectionStatus())
                {
                    await redmine.connectionAsync(Properties.Settings.Default.redmineid, Properties.Settings.Default.redminepw);
                }

                if (!redmine.getConnectionStatus())
                {
                    notificationSendToHtml("Redmine bağlantısı kurulamadı", "error");
                    return;
                }

                redmine.updateIssue(taskId, "Status", statusId);
                notificationSendToHtml("Task durumu güncellendi", "success");

                // Task listesini yenile (sadece istenirse)
                if (refreshTaskList)
                {
                    await GetQaTasks();
                }
            }
            catch (Exception ex)
            {
                logger.Error($"QA Task durumunu güncellerken hata: {ex.Message}");
                notificationSendToHtml("QA Task durumunu güncellerken hata oluştu", "error");
            }
        }
        private async Task UpdateQaTask(JsonElement taskData, bool refreshTaskList = false)
        {
            try
            {
                if (!redmine.getConnectionStatus())
                {
                    await redmine.connectionAsync(Properties.Settings.Default.redmineid, Properties.Settings.Default.redminepw);
                }

                if (!redmine.getConnectionStatus())
                {
                    notificationSendToHtml("Redmine bağlantısı kurulamadı", "error");
                    return;
                }

                string taskId = taskData.GetProperty("id").GetString();
                string subject = taskData.GetProperty("subject").GetString();
                string statusId = taskData.GetProperty("statusId").GetString();
                string priorityId = taskData.GetProperty("priorityId").GetString();
                string assignedToId = taskData.GetProperty("assignedToId").GetString();
                string doneRatio = taskData.GetProperty("doneRatio").GetString();
                string startDate = taskData.GetProperty("startDate").GetString();
                string dueDate = taskData.GetProperty("dueDate").GetString();
                string description = taskData.GetProperty("description").GetString();

                // Task'i güncelle
                await redmine.updateIssueAsync(taskId, new Dictionary<string, string>
                {
                    { "Subject", subject },
                    { "Status", statusId },
                    { "Priority", priorityId },
                    { "AssignedTo", assignedToId },
                    { "DoneRatio", doneRatio },
                    { "StartDate", startDate },
                    { "DueDate", dueDate },
                    { "Description", description }
                });

                notificationSendToHtml("Task başarıyla güncellendi", "success");

                // Task listesini yenile (sadece istenirse)
                if (refreshTaskList)
                {
                    await GetQaTasks();
                }
            }
            catch (Exception ex)
            {
                logger.Error($"QA Task güncellenirken hata: {ex.Message}");
                notificationSendToHtml("QA Task güncellenirken hata oluştu", "error");
            }
        }
        private async Task AddQaTaskComment(string taskId, string comment, bool refreshTaskList = false)
        {
            try
            {
                if (!redmine.getConnectionStatus())
                {
                    await redmine.connectionAsync(Properties.Settings.Default.redmineid, Properties.Settings.Default.redminepw);
                }

                if (!redmine.getConnectionStatus())
                {
                    notificationSendToHtml("Redmine bağlantısı kurulamadı", "error");
                    return;
                }

                await redmine.addIssueCommentAsync(taskId, comment);
                notificationSendToHtml("Yorum başarıyla eklendi", "success");

                // Task listesini yenile (sadece istenirse)
                if (refreshTaskList)
                {
                    await GetQaTasks();
                }
            }
            catch (Exception ex)
            {
                logger.Error($"QA Task'e yorum eklenirken hata: {ex.Message}");
                notificationSendToHtml("QA Task'e yorum eklenirken hata oluştu", "error");
            }
        }
        // Studio QA Tasks (ProjectID 39) getiren metod
        private async Task GetStudioQATasks()
        {
            try
            {
                // Redmine bağlantısını kontrol et, bağlı değilse bağlan
                if (!redmine.getConnectionStatus())
                {
                    await redmine.connectionAsync(Properties.Settings.Default.redmineid, Properties.Settings.Default.redminepw, true);
                }

                if (!redmine.getConnectionStatus())
                {
                    notificationSendToHtml("Redmine bağlantısı kurulamadı", "error");
                    return;
                }

                // API ile bağlantı için tempConnection oluştur
                var redmineTempManager = await redmine.tempConnectionAsync();

                // ProjectID 39 olan taskları getir
                var parameters = new NameValueCollection { { "project_id", "39" } };
                var issues = await redmineTempManager.GetObjectsAsync<Issue>(parameters);

                if (issues == null || (int)issues.Count == 0)
                {
                    webView.CoreWebView2.PostWebMessageAsJson(JsonSerializer.Serialize(new
                    {
                        type = "studioQATasksData",
                        tasks = new List<Issue>()
                    }));
                    return;
                }

                webView.CoreWebView2.PostWebMessageAsJson(JsonSerializer.Serialize(new
                {
                    type = "studioQATasksData",
                    tasks = issues
                }));
            }
            catch (Exception ex)
            {
                logger.Error($"Studio QA tasklarını getirirken hata: {ex.Message}");
                notificationSendToHtml("Studio QA tasklarını getirirken hata oluştu", "error");
            }
        }
        // Tüm görevleri getiren metod
        private async Task GetAllTasks()
        {
            try
            {
                // Redmine bağlantısını kontrol et, bağlı değilse bağlan
                if (!redmine.getConnectionStatus())
                {
                    await redmine.connectionAsync(Properties.Settings.Default.redmineid, Properties.Settings.Default.redminepw, true);
                }

                if (!redmine.getConnectionStatus())
                {
                    notificationSendToHtml("Redmine bağlantısı kurulamadı", "error");
                    return;
                }

                // API ile bağlantı için tempConnection oluştur
                var redmineTempManager = await redmine.tempConnectionAsync();

                // Abdulkadir Aktaş'a atanmış görevleri al
                string testername = Properties.Settings.Default.testername;
                if (string.IsNullOrEmpty(testername) || testername == "qa-api")
                {
                    testername = "abdulkadir.aktas";
                }
                var issues = await redmine.getAllIssuesAsync(testername, redmineTempManager);
                if (issues == null || issues.Count == 0)
                {
                    webView.CoreWebView2.PostWebMessageAsJson(JsonSerializer.Serialize(new
                    {
                        type = "allTasksData",
                        tasks = new List<Issue>()
                    }));
                    return;
                }

                webView.CoreWebView2.PostWebMessageAsJson(JsonSerializer.Serialize(new
                {
                    type = "allTasksData",
                    tasks = issues
                }));
            }
            catch (Exception ex)
            {
                logger.Error($"Tüm görevleri getirirken hata: {ex.Message}");
                notificationSendToHtml("Tüm görevleri getirirken hata oluştu", "error");
            }
        }
        // All Tasks için durum güncelleme metodu
        private async Task UpdateAllTaskStatus(string taskId, string statusId, bool refreshTaskList = false)
        {
            try
            {
                if (!redmine.getConnectionStatus())
                {
                    await redmine.connectionAsync(Properties.Settings.Default.redmineid, Properties.Settings.Default.redminepw);
                }

                if (!redmine.getConnectionStatus())
                {
                    notificationSendToHtml("Redmine bağlantısı kurulamadı", "error");
                    return;
                }

                redmine.updateIssue(taskId, "Status", statusId);
                notificationSendToHtml("Task durumu güncellendi", "success");

                // Task listesini yenile (sadece istenirse)
                if (refreshTaskList)
                {
                    await GetAllTasks();
                }
            }
            catch (Exception ex)
            {
                logger.Error($"Task durumunu güncellerken hata: {ex.Message}");
                notificationSendToHtml("Task durumunu güncellerken hata oluştu", "error");
            }
        }
        // Studio QA Tasks için durum güncelleme metodu
        private async Task UpdateStudioQATaskStatus(string taskId, string statusId, bool refreshTaskList = false)
        {
            try
            {
                if (!redmine.getConnectionStatus())
                {
                    await redmine.connectionAsync(Properties.Settings.Default.redmineid, Properties.Settings.Default.redminepw);
                }

                if (!redmine.getConnectionStatus())
                {
                    notificationSendToHtml("Redmine bağlantısı kurulamadı", "error");
                    return;
                }

                redmine.updateIssue(taskId, "Status", statusId);
                notificationSendToHtml("Studio QA Task durumu güncellendi", "success");

                // Task listesini yenile (sadece istenirse)
                if (refreshTaskList)
                {
                    await GetStudioQATasks();
                }
            }
            catch (Exception ex)
            {
                logger.Error($"Studio QA Task durumunu güncellerken hata: {ex.Message}");
                notificationSendToHtml("Studio QA Task durumunu güncellerken hata oluştu", "error");
            }
        }
        // All Tasks için görev güncelleme metodu
        private async Task UpdateAllTask(JsonElement taskData, bool refreshTaskList = false)
        {
            try
            {
                if (!redmine.getConnectionStatus())
                {
                    await redmine.connectionAsync(Properties.Settings.Default.redmineid, Properties.Settings.Default.redminepw);
                }

                if (!redmine.getConnectionStatus())
                {
                    notificationSendToHtml("Redmine bağlantısı kurulamadı", "error");
                    return;
                }

                string taskId = taskData.GetProperty("id").GetString();
                string subject = taskData.GetProperty("subject").GetString();
                string statusId = taskData.GetProperty("statusId").GetString();
                string priorityId = taskData.GetProperty("priorityId").GetString();
                string assignedToId = taskData.GetProperty("assignedToId").GetString();
                string doneRatio = taskData.GetProperty("doneRatio").GetString();
                string startDate = taskData.GetProperty("startDate").GetString();
                string dueDate = taskData.GetProperty("dueDate").GetString();
                string description = taskData.GetProperty("description").GetString();

                // Task'i güncelle
                await redmine.updateIssueAsync(taskId, new Dictionary<string, string>
                {
                    { "Subject", subject },
                    { "Status", statusId },
                    { "Priority", priorityId },
                    { "AssignedTo", assignedToId },
                    { "DoneRatio", doneRatio },
                    { "StartDate", startDate },
                    { "DueDate", dueDate },
                    { "Description", description }
                });

                notificationSendToHtml("Task başarıyla güncellendi", "success");

                // Task listesini yenile (sadece istenirse)
                if (refreshTaskList)
                {
                    await GetAllTasks();
                }
            }
            catch (Exception ex)
            {
                logger.Error($"Task güncellenirken hata: {ex.Message}");
                notificationSendToHtml("Task güncellenirken hata oluştu", "error");
            }
        }
        // All Tasks için yorum ekleme metodu
        private async Task AddAllTaskComment(string taskId, string comment, bool refreshTaskList = false)
        {
            try
            {
                if (!redmine.getConnectionStatus())
                {
                    await redmine.connectionAsync(Properties.Settings.Default.redmineid, Properties.Settings.Default.redminepw);
                }

                if (!redmine.getConnectionStatus())
                {
                    notificationSendToHtml("Redmine bağlantısı kurulamadı", "error");
                    return;
                }

                await redmine.addIssueCommentAsync(taskId, comment);
                notificationSendToHtml("Yorum başarıyla eklendi", "success");

                // Task listesini yenile (sadece istenirse)
                if (refreshTaskList)
                {
                    await GetAllTasks();
                }
            }
            catch (Exception ex)
            {
                logger.Error($"Task'e yorum eklenirken hata: {ex.Message}");
                notificationSendToHtml("Task'e yorum eklenirken hata oluştu", "error");
            }
        }
        private string getVersionFromsql(string app)
        {
            try
            {
                coon.Open();
                string query = "SELECT app, version FROM version where app=\"" + app + "\"";
                MySqlCommand cmd = new(query, coon);
                using (MySqlDataReader reader = cmd.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        string version = reader["version"].ToString();
                        Console.WriteLine($"App: {app}, Version: {version}");
                        coon.Close();
                        return version;
                    }
                }
                coon.Close();
                return "";
            }
            catch (Exception ex)
            {
                logger.Error(ex + " line 267");
                return "";
            }
        }
        private async void Form1_Load(object sender, EventArgs e)
        {
            load();
        }
        private async void load()
        {
            Control.CheckForIllegalCrossThreadCalls = false;
            selectedOS = "AOS";
            // Initialize data tables in the new consolidated service
            GoogleApiService.GetCommentsDataTable();

            await Task.Run(() =>
            {
                getUpdaterLatestVerionFromUrl();
                GetLatestVersionFromUrl();
                if (Properties.Settings.Default.FolderLocation.Length > 0)
                {
                    folderLocation = Properties.Settings.Default.FolderLocation.Replace("\\","/");
                }
            });
            try
            {
                getAllHashesFromSql();
                getAllTestersFromSql();
                KillAdbProcesses();
                UpdateEnvironmentVariable();
                InitializeUIComponents();
                InitializeDirectoriesAndSettings();
                FetchDataFromUrlsAsync();
                HandleRedmineConnectionAsync();
                InitializeLogger();
                UpdateUserLoginDetailsAsync();
            }
            catch (Exception ex)
            {
                logger.Error(ex + " line 392");
            }
        }
        private void KillAdbProcesses()
        {
            Process[] adb = Process.GetProcessesByName("adb");
            foreach (Process process in adb)
            {
                process.Kill();
            }
        }
        private void UpdateEnvironmentVariable()
        {
            bool varmi = false;
            string getEnvs = Environment.GetEnvironmentVariable("Path", EnvironmentVariableTarget.User);
            if (!string.IsNullOrEmpty(getEnvs))
            {
                string[] env = getEnvs.Split(';');
                for (int i = 0; i < env.Length; i++)
                {
                    if (env[i] == Environment.CurrentDirectory + "/platform-tools")
                    {
                        varmi = true;
                    }
                }
            }
            if (!varmi)
            {
                string path = getEnvs + ";" + Environment.CurrentDirectory + "/platform-tools";
                Environment.SetEnvironmentVariable("Path", path, EnvironmentVariableTarget.User);
            }
        }
        private void InitializeUIComponents()
        {
            string redmineID = Properties.Settings.Default.redmineid;
            string redminePW = Properties.Settings.Default.redminepw;
            bool checkBox2 = Properties.Settings.Default.rememberMe;
            if (redmineID.Length > 0 && checkBox2)
            {
                loginAsync(redmineID, redminePW, checkBox2);
            }
            Control.CheckForIllegalCrossThreadCalls = false;
            this.TopMost = Properties.Settings.Default.alwaysTop;

        }
        private async Task HandleRedmineConnectionAsync()
        {
            // WebView2'yi göster
            webView.Show();

            while (!redmine.getConnectionStatus())
            {
                await Task.Delay(2000);
            }

            // Login başarılı olduğunda ana içeriği göster
            if (webView.CoreWebView2 != null)
            {
                webView.CoreWebView2.PostWebMessageAsJson(JsonSerializer.Serialize(new {
                    type = "loginSuccess"
                }));
            }
        }
        private void InitializeDirectoriesAndSettings()
        {
            if (!Directory.Exists(Path.GetTempPath() + "qa_utility"))
            {
                Directory.CreateDirectory(Path.GetTempPath() + "qa_utility");
            }
            wifiAyarla("enable");
        }
        private async Task FetchDataFromUrlsAsync()
        {
            await Task.Run(() =>
            {
                GetAllTesters();
            });
        }
        private void InitializeLogger()
        {
            InitializeAdbLogger();
            fromloaded = true;
        }
        private async Task UpdateUserLoginDetailsAsync()
        {
            try
            {
                string publicIP = await GetPublicIPAddressAsync();
                string updateQuery = "UPDATE login SET `lastlogin` = \"" + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss") + "\", `ip` = \"" + publicIP + "\" WHERE `user` = '" + Properties.Settings.Default.testername + "'";
                await runSqlAsync(updateQuery);
            }
            catch (Exception ex)
            {
                logger.Error($"Error updating user login details: {ex.Message}");
            }
        }
        public static async Task<string> GetPublicIPAddressAsync()
        {
            try
            {
                using (HttpClient client = new HttpClient())
                {
                    client.DefaultRequestHeaders.Add("User-Agent", "C# App");
                    string ip = await client.GetStringAsync("http://api.ipify.org");
                    return ip;
                }
            }
            catch (Exception ex)
            {
                logger.Error(ex + " line 513");
                return "Not available";
            }
        }
        private string getRole(string name)
        {
            if (Properties.Settings.Default.testername == null)
            {
                return "tester";
            }
            else
            {
                //loginUserName
                return dataTableFind(testerList, "user", name, "role");
                //testers.Find(x => x.name == name).role;
            }
        }
        private void getUpdaterLatestVerionFromUrl()
        {
            var version = getVersionFromsql("AutoUpdater");
            if (version != null)
            {
                var versionPart = "0.0";
                if (File.Exists("AutoUpdater.exe"))
                {
                    // autoupdater.exe version
                    var currentVersion = FileVersionInfo.GetVersionInfo("AutoUpdater.exe");
                    versionPart = currentVersion.ProductVersion.Split('+')[0];
                }
                int versionCompare = version.CompareTo(versionPart);
                if (versionCompare < 0)
                {

                }
                else if (versionCompare > 0)
                {
                    downloader downloader = new();
                    try
                    {
                        downloader.downloadLatestVersion(ftpServer + "updaterapp/");
                    }
                    catch (Exception ex)
                    {
                        logger.Error(ex + " line 648");
                        MessageBox.Show("FTP Bağlantısında Bir Sorun Oluştu. Lütfen Daha Sonra Tekrar Deneyin");
                    }
                }
            }
        }
        private bool firstTime = true;
        private void GetLatestVersionFromUrl()
        {
            var version = getVersionFromsql("Qa Utility");
            if (version != null)
            {
                var currentVersion = Application.ProductVersion;
                var versionPart = currentVersion.Split('+')[0];

                int versionCompare = version.CompareTo(versionPart);
                if (versionCompare < 0)
                {

                }
                else if (versionCompare > 0)
                {

                    if (debugMode == false)
                    {
                        if (firstTime)
                        {
                            firstTime = false;
                            Process.Start("AutoUpdater.exe");
                            this.Close();

                            Application.Exit();
                        }
                        else
                        {
                            timer1.Stop();
                            DialogResult mgbox = MessageBox.Show("Yeni bir güncelleme yayımlandı.", "Force Update", MessageBoxButtons.YesNo);
                            if (mgbox == DialogResult.Yes)
                            {
                                firstTime = false;
                                Process.Start("AutoUpdater.exe");
                                this.Close();
                                Application.Exit();
                            }
                            else if (mgbox == DialogResult.No)
                            {
                                timer1.Start();
                            }
                        }
                    }
                }
            }
        }
        private void getAllTestersFromSql()
        {
            testerList.Clear();
            string sql = "Select * from login"; // and yetki='" & yetki & "'"
            MySqlDataAdapter verial = new MySqlDataAdapter(sql, coon);
            verial.Fill(testerList);
        }
        private void getAllHashesFromSql()
        {
            haslist.Clear();
            string sql = "Select * from fileHashes"; // and yetki='" & yetki & "'"
            MySqlDataAdapter verial = new MySqlDataAdapter(sql, coon);
            verial.Fill(haslist);
        }
        private void GetAllTesters()
        {
            try
            {
                string role = "tester";
                if (!string.IsNullOrEmpty(Properties.Settings.Default.testername))
                {
                    role = getRole(Properties.Settings.Default.testername);
                }
            }
            catch (Exception ex)
            {
                logger.Error(ex + " line 753");
                MessageBox.Show("İnternet bağlantınızı kontrol ediniz.");
            }
        }
        private void getAllGamesData()
        {
            allGamesDatas.Clear();
            string sql = "Select * from allGamesData"; // and yetki='" & yetki & "'"
            MySqlDataAdapter verial = new MySqlDataAdapter(sql, coon);
            verial.Fill(allGamesDatas);
        }
        private void getAllGameListFromFtp()
        {
            getAllGamesData();
            //string role = "tester";
            //if (!string.IsNullOrEmpty(Properties.Settings.Default.testername))
            //{
            //    role = getRole(Properties.Settings.Default.testername);
            //}

            // Oyun listesini HTML'e gönder
            try
            {
                if (webView?.CoreWebView2 != null)
                {
                    var gameList = allGamesDatas.AsEnumerable()
                        .Where(row => row.Field<string>("status")?.ToLower() == "aktif")
                        .Select(row => row.Field<string>("name"))
                        .ToList();

                    webView.CoreWebView2.PostWebMessageAsJson(JsonSerializer.Serialize(new
                    {
                        type = "gameList",
                        games = gameList
                    }));
                }
            }
            catch (Exception ex)
            {
                logger.Error($"Game list update error: {ex.Message}");
            }
        }
        string deviceModel = "";
        string deviceMarka = "";
        string asosurum = "";
        string chipset = "";
        string cpu = "";
        string deviceLang = "";
        string cozunurluk = "";
        private void adbLogger_OutputDataReceived(object sender, DataReceivedEventArgs e)
        {
            if (e.Data != null && selectedOS != null)
            {
                logger.Debug($"ADB Output: {e.Data}"); // Debug log ekle

                // UI thread'de işlem yap
                this.BeginInvoke(new Action(() => {
                    // Android cihaz için işlemler
                    if (selectedOS.ToString() == "AOS")
                    {
                        // Kablosuz bağlantı kontrolü
                        if (e.Data.Contains(":5555") && e.Data.Contains("device"))
                        {
                            isWirelessConnection = true;
                            logger.Debug("Wireless connection detected");
                            
                            // Kablosuz bağlantı düğmesinin durumunu güncelle
                            webView?.CoreWebView2?.PostWebMessageAsJson(JsonSerializer.Serialize(new
                            {
                                type = "updateWirelessButtonState",
                                isWireless = true
                            }));
                        }
                        else if (e.Data.Contains("\tdevice") && !e.Data.Contains(":5555"))
                        {
                            isWirelessConnection = false;
                            logger.Debug("USB connection detected");
                            
                            // Kablosuz bağlantı düğmesinin durumunu güncelle
                            webView?.CoreWebView2?.PostWebMessageAsJson(JsonSerializer.Serialize(new
                            {
                                type = "updateWirelessButtonState",
                                isWireless = false
                            }));
                        }

                        if ((e.Data.Contains("\tdevice") || (e.Data.Contains(":5555") && e.Data.Contains("device"))))
                        {
                            logger.Debug("Android device connected detected");
                            //DeviceStatusLabel.BackColor = Color.Green;
                            isDeviceConnected = true;
                            count = 0;
                            if (deviceModel == "" || deviceModel == null)
                            {
                                UpdateDeviceStatus();
                                deviceModel = ExecuteAdbCommand("shell getprop ro.product.model");
                                deviceMarka = ExecuteAdbCommand("shell getprop ro.product.brand");
                                asosurum = ExecuteAdbCommand("shell getprop ro.build.version.release");
                                chipset = ExecuteAdbCommand("shell getprop ro.product.board");
                                cpu = ExecuteAdbCommand("shell getprop ro.product.cpu.abi");
                                deviceLang = ExecuteAdbCommand("shell getprop persist.sys.locale");
                                cozunurluk = ExecuteAdbCommand("shell wm size").Replace("Physical size: ", "");

                                // Cihaz bilgilerini HTML'e gönder
                                UpdateDeviceInfo();

                                SendNotificationToDevice("QA Utility Bağlandı", "Bağlantı başarılı.");
                            }
                            string currentBundle = getCurrentAppPackage();
                            string batteryLevel = getBatteryLevel();
                            string connectionType = isWirelessConnection ? "(Kablosuz)" : "(USB)";
                            //DeviceStatusLabel.Text = "Device Model : " + deviceModel + " " + connectionType + " Şarj :" + batteryLevel;
                            count = 0;
                            aosPanelVisible = true;
                        }
                        else if (e.Data.Contains("List of devices attached"))
                        {
                            count++;
                            if (count >= 2)
                            {
                                logger.Debug("Android device disconnect detected");
                                DisconnectDevice();
                                count = 0;
                                aosPanelVisible = false;
                            }
                        }
                    }
                    // iOS cihaz için işlemler
                    else if (selectedOS.ToString() == "IOS")
                    {
                        // iOS cihaz bağlantı kontrolü
                        // ideviceinfo çıktısında DeviceName değeri varsa cihaz bağlı demektir
                        if (e.Data != "" && !e.Data.Contains("ERROR: No device found!") && !e.Data.Contains("ideviceinfo"))
                        {
                            logger.Debug("iOS device connected detected: " + e.Data);
                            //DeviceStatusLabel.BackColor = Color.Green;
                            isDeviceConnected = true;
                            count = 0;

                            // iOS cihaz bilgilerini al
                            if (deviceModel == "" || deviceModel == null)
                            {
                                UpdateDeviceStatus();

                                // iOS cihaz bilgilerini almak için ideviceinfo komutlarını çalıştır
                                string productType = ExecuteIosCommand("-k ProductType");
                                deviceModel = GetReadableDeviceName(productType);
                                deviceMarka = "Apple";
                                asosurum = ExecuteIosCommand("-k ProductVersion");
                                chipset = ExecuteIosCommand("-k HardwareModel");
                                cpu = ExecuteIosCommand("-k CPUArchitecture");
                                deviceLang = ExecuteIosCommand("-k LanguagesSetting");
                                string deviceResolution = ExecuteIosCommand("-k ScreenWidth") + "x" + ExecuteIosCommand("-k ScreenHeight");
                                cozunurluk = deviceResolution != "x" ? deviceResolution : "";

                                // Cihaz bilgilerini HTML'e gönder
                                UpdateDeviceInfo();

                                notificationSendToHtml("iOS cihaz bağlandı");
                            }

                            // iOS cihaz durumunu göster
                            //DeviceStatusLabel.Text = "iOS Device: " + deviceModel + " (" + asosurum + ")";
                            iosPanelVisible = true;
                        }
                        else if (e.Data.Contains("ERROR: No device found!") || e.Data.Contains("ideviceinfo"))
                        {
                            count++;
                            if (count >= 2)
                            {
                                logger.Debug("iOS device disconnect detected");
                                DisconnectDevice();
                                count = 0;
                                iosPanelVisible = false;
                            }
                        }
                    }
                }));
            }
        }
        private static string getBatteryLevel()
        {
            // adb shell dumpsys battery
            Process batteryProcess = GetProcess("adb", "shell dumpsys battery");
            batteryProcess.Start();
            string batteryOutput = batteryProcess.StandardOutput.ReadToEnd().Trim();
            batteryProcess.WaitForExit();
            string batteryLevel = "";
            if (batteryOutput.Contains("level:"))
            {
                batteryLevel = batteryOutput[(batteryOutput.IndexOf("level:") + 6)..];
                batteryLevel = batteryLevel[..batteryLevel.IndexOf(Environment.NewLine)];
            }
            // temperature: 310
            if (batteryOutput.Contains("temperature:"))
            {
                string batteryTemp = batteryOutput[(batteryOutput.IndexOf("temperature:") + 12)..];
                batteryTemp = batteryTemp[..batteryTemp.IndexOf(Environment.NewLine)];
                batteryLevel += " Sıcaklık :" + (int.Parse(batteryTemp) / 10).ToString();
            }
            return batteryLevel;
        }
        private string ExecuteAdbCommand(string command)
        {
            try
            {
                ProcessStartInfo psi = new ProcessStartInfo
                {
                    FileName = "adb",
                    Arguments = command,
                    RedirectStandardOutput = true,
                    UseShellExecute = false,
                    CreateNoWindow = true
                };
                using (Process process = Process.Start(psi))
                {
                    using (System.IO.StreamReader reader = process.StandardOutput)
                    {
                        return reader.ReadToEnd().Trim();
                    }
                }
            }
            catch (Exception ex)
            {
                logger.Error(ex + " line 940");
                return "Hata: " + ex.Message;
            }
        }
        private string ExecuteIosCommand(string command)
        {
            try
            {
                // Tam dosya yolunu kullan
                string ioslogPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "ioslog", "ideviceinfo");

                ProcessStartInfo psi = new ProcessStartInfo
                {
                    FileName = ioslogPath,
                    Arguments = command,
                    RedirectStandardOutput = true,
                    UseShellExecute = false,
                    CreateNoWindow = true
                };

                using (Process process = Process.Start(psi))
                {
                    if (process == null)
                    {
                        logger.Error("iOS command process could not be started");
                        return "";
                    }

                    using (System.IO.StreamReader reader = process.StandardOutput)
                    {
                        return reader.ReadToEnd().Trim();
                    }
                }
            }
            catch (Exception ex)
            {
                // Hata mesajını bir kez logla ve dön
                logger.Error($"iOS command execution error: {ex.Message}");
                return "";
            }
        }
        private void monkeyRun()
        {
            string AOSbundleID = getGameDataFromDataTable(selectedGame, "bundleid");
            string monkeyOptions = "";
            monkeyOptions += " -p " + AOSbundleID;
            monkeyOptions += " --pct-touch 60";
            monkeyOptions += " --pct-trackball 20";
            monkeyOptions += " --pct-motion 20";
            monkeyOptions += " --pct-nav 0";
            monkeyOptions += " --pct-majornav 0";
            monkeyOptions += " --pct-appswitch 0";
            monkeyOptions += " --throttle 0";
            monkeyOptions += " --pct-anyevent 0";
            monkeyOptions += " -vvv 100000";
            Process monkeyProcess = GetProcess("adb", "shell monkey" + monkeyOptions);
            monkeyProcess.Start();
            monkeyProcess.BeginOutputReadLine();
            monkeyProcess.OutputDataReceived += new DataReceivedEventHandler(monkeyProcess_OutputDataReceived);
            monkeyProcess.WaitForExit();

        }
        private void monkeyProcess_OutputDataReceived(object sender, DataReceivedEventArgs e)
        {
            if (e.Data != null && e.Data.Contains("Monkey finished"))
            {
                Task.Run(() => killProcess("monkey"));
            }
        }
        private static void killProcess(string processName)
        {
            //adb shell ps | findstr logcat
            //adb shell kill 1234
            Process killMonkeyProcess = GetProcess("adb", "shell ps");
            killMonkeyProcess.Start();
            string killMonkeyOutput = killMonkeyProcess.StandardOutput.ReadToEnd().Trim();
            killMonkeyProcess.WaitForExit();
            string[] killMonkeyOutputArray = killMonkeyOutput.Split(new char[] { '\n' });
            foreach (string line in killMonkeyOutputArray)
            {
                if (line.Contains(processName) && !line.Contains("adbd") && !line.Contains("ps"))
                {
                    //line = "u0_a211        685   524   10908   3368 0                   0 S logcat\r"
                    string[] lineArray = line.Split(new char[] { ' ' });
                    for (int i = 1; i < lineArray.Length - 1; i++)
                    {
                        if ((lineArray[0] == "shell" && processName == "logcat") || true)
                        {
                            string pid = lineArray[i];
                            //if (pid != "")
                            //    break;
                            if (pid != "" && pid.Length > 3)
                            {
                                Process killMonkeyProcess2 = GetProcess("adb", "shell kill " + pid);
                                killMonkeyProcess2.Start();
                                _ = killMonkeyProcess2.StandardOutput.ReadToEnd().Trim();
                                killMonkeyProcess2.WaitForExit();
                            }
                        }
                    }

                }
            }

        }
        int logSure = 0;
        private Process readWriteLog;
        private DataTable haslist = new DataTable();
        private string[] getAppVersion(string appbundleid)
        {
            if (appbundleid == "Seçilmedi") { return ["0","0"]; }
            string version = "0";
            string versionCode = "0";
            string targetSdk = "0";
            if (selectedOS.ToString() == "AOS")
            {
                Process versionProcess = GetProcess("adb", "shell dumpsys package " + appbundleid);
                versionProcess.Start();
                string versionOutput = versionProcess.StandardOutput.ReadToEnd().Trim();
                versionProcess.WaitForExit();

                if (versionOutput.Contains("versionName="))
                {
                    version = versionOutput[(versionOutput.IndexOf("versionName=") + 12)..];
                    version = version[..version.IndexOf(Environment.NewLine)];
                }

                if (versionOutput.Contains("versionCode="))
                {
                    versionCode = versionOutput[(versionOutput.IndexOf("versionCode=") + 12)..];
                    versionCode = versionCode[..versionCode.IndexOf(Environment.NewLine)];
                    versionCode = versionCode[..versionCode.IndexOf(" ")];
                }
                if (versionOutput.Contains("targetSdk="))
                {
                    targetSdk = versionOutput[(versionOutput.IndexOf("targetSdk=") + 10)..];
                    targetSdk = targetSdk[..targetSdk.IndexOf(Environment.NewLine)];
                    if (int.Parse(targetSdk) < 33)
                    {
                        MessageBox.Show("Target SDK 33'den küçük!");
                    }
                }
            }
            return new string[] { version, versionCode, targetSdk };
        }
        // Durum güncellemesi için sayaç ve zaman aralığı
        private int logOutputCounter = 0;
        private readonly int logStatusUpdateInterval = 100; // Her 100 satırda bir durum güncellemesi yap
        private DateTime lastStatusUpdateTime = DateTime.MinValue;
        private readonly TimeSpan statusUpdateInterval = TimeSpan.FromSeconds(2); // 2 saniyede bir durum güncellemesi yap
        private void logcatProcess_OutputDataReceived(object sender, DataReceivedEventArgs e)
        {
            if (e.Data != null)
            {
                // Durum güncellemesini sadece belirli aralıklarla yap
                logOutputCounter++;
                DateTime now = DateTime.Now;
                if (logOutputCounter % logStatusUpdateInterval == 0 ||
                    (now - lastStatusUpdateTime) > statusUpdateInterval)
                {
                    _ = setToolStatusLabel("Loglar Alınıyor", "green", true);
                    lastStatusUpdateTime = now;
                }

                string logcatOutput = e.Data;
                EnsureLogDirectoriesExist();
                File.AppendAllText(logcatPath, logcatOutput + Environment.NewLine);
                if (selectedOS.ToString() == "IOS")
                {
                    string appname = logcatOutput.ToString();
                    // buraya bir ara html kısmına veri göndereceğiz
                }
            }
            else
            {
                Thread.Sleep(10000);
            }
        }
        private string GetLogcatPath(string logNote = null)
        {
            string AOSbundleID = getGameDataFromDataTable(selectedGame, "bundleid");
            string[] versionbuildnumber = getAppVersion(AOSbundleID);
            string version = versionbuildnumber[0].Replace(".", "_");
            string versionCode = versionbuildnumber[1].Replace(".", "_");
            string appversionandcode = "version_" + version + "_buildNumber_" + versionCode;
            string selectedos = selectedOS.ToString();
            string selectedgame = CleanFileName(selectedGame);
            if (AOSbundleID.Contains("Seçilmedi"))
            {
                selectedgame = "All_Logs";
            }
            string selectedtester = Properties.Settings.Default.testername;
            string status = "status_" + logNote;
            if (selectedos == "IOS")
            {
                status = "status_" + logNote;
            }
            status = status.Replace("/", "").Replace("\\", "");
            return Path.Combine(Application.StartupPath, "logs", DateTime.Now.ToString("yyyy-MM-dd"), $"{DateTime.Now:yyyy-MM-dd-HH-mm-ss}-{selectedos}-{selectedgame}-{selectedtester}-{appversionandcode}-{status}.txt");

        }
        private static Process GetProcess(string fileName, string arguments)
        {
            return new Process
            {
                StartInfo = new ProcessStartInfo
                {
                    StandardOutputEncoding = Encoding.UTF8,
                    FileName = fileName,
                    Arguments = arguments,
                    UseShellExecute = false,
                    RedirectStandardOutput = true,
                    CreateNoWindow = true
                }
            };
        }
        private void EnsureLogDirectoriesExist()
        {
            string logDirectory = Path.Combine(Application.StartupPath, "logs", DateTime.Now.ToString("yyyy-MM-dd"));

            if (!Directory.Exists(logDirectory))
                Directory.CreateDirectory(logDirectory);

            if (!File.Exists(logcatPath))
                File.Create(logcatPath).Close();
        }
        private void DisconnectDevice()
        {
            //DeviceStatusLabel.BackColor = Color.Red;
            isDeviceConnected = false;
            isWirelessConnection = false;
            deviceModel = "";
            deviceMarka = "";
            asosurum = "";
            chipset = "";
            cpu = "";
            deviceLang = "";
            cozunurluk = "";
            UpdateDeviceStatus();
            UpdateDeviceInfo();
            
            // Kablosuz bağlantı düğmesinin durumunu güncelle
            webView?.CoreWebView2?.PostWebMessageAsJson(JsonSerializer.Serialize(new
            {
                type = "updateWirelessButtonState",
                isWireless = false
            }));
        }
        private (string version, string versionCode, string targetSdk) getsBundleInfo(string bundleId)
        {
            string[] versionbuildnumber = getAppVersion(bundleId);
            string version = versionbuildnumber[0];
            string versionCode = versionbuildnumber[1];
            string targetSdk = versionbuildnumber[2];
            return (version, versionCode, targetSdk);
        }
        private async Task adbDeviceController()
        {
            try
            {
                await Task.Run(() => {
                    adbLogger.StandardInput.WriteLine("adb devices");
                });
            }
            catch (Exception ex)
            {
                logger.Error($"ADB device controller error: {ex.Message}");
            }
        }
        int say = 0;
        private bool isTimerOperationInProgress = false;
        private async void timer1_Tick(object sender, EventArgs e)
        {
            // Skip if an operation is already in progress
            if (isTimerOperationInProgress)
                return;

            try
            {
                isTimerOperationInProgress = true;

                if (selectedOS != null)
                {
                    if (selectedOS.ToString() == "AOS")
                    {
                        await adbDeviceController();
                    }
                    else if (selectedOS.ToString() == "IOS")
                    {
                        await ideviceDeviceController();
                    }
                }

                if (say == 20)
                {
                    await UpdateUserLoginDetailsAsync();
                    say = 0;
                }
                say++;
            }
            finally
            {
                isTimerOperationInProgress = false;
            }
        }
        private async Task ideviceDeviceController()
        {
            try
            {
                await Task.Run(() => {
                    // Tam dosya yolunu kullan
                    string ioslogPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "ioslog", "ideviceinfo");
                    adbLogger.StandardInput.WriteLine($"\"{ioslogPath}\" -k DeviceName");
                });
            }
            catch (Exception ex)
            {
                logger.Error($"iOS device controller error: {ex.Message}");
            }
        }
        public void UploadFileToFTP(string localFilePath, string remoteFileName, string ftpServerAddress, string ftpUsername, string ftpPassword)
        {
            try
            {
                // Dosyanın bulunacağı klasör yolunu al
                string remoteDirectoryPath = Path.GetDirectoryName(remoteFileName)?.Replace("\\", "/");

                // Klasör yolu varsa, FTP'de oluşturmayı sağla
                if (!string.IsNullOrEmpty(remoteDirectoryPath))
                {
                    bool directoryCreated = EnsureFtpDirectoryExists(remoteDirectoryPath, ftpServerAddress, ftpUsername, ftpPassword);
                    if (!directoryCreated)
                    {
                        throw new Exception("FTP klasörü oluşturulamadı: " + remoteDirectoryPath);
                    }
                }

                FtpWebRequest ftpRequest = (FtpWebRequest)WebRequest.Create($"{ftpServerAddress}/{remoteFileName}");
                ftpRequest.Credentials = new NetworkCredential(ftpUsername, ftpPassword);
                ftpRequest.Method = WebRequestMethods.Ftp.UploadFile;
                ftpRequest.UseBinary = true;
                ftpRequest.KeepAlive = true; // Bağlantıyı açık tut
                ftpRequest.UsePassive = true; // Pasif mod genellikle daha hızlıdır

                // Timeout süresini artır
                ftpRequest.Timeout = 30000; // 30 saniye
                ftpRequest.ReadWriteTimeout = 30000; // 30 saniye

                // Dosyayı doğrudan stream olarak oku ve yükle (bellek kullanımını azaltır)
                using (FileStream fileStream = new FileStream(localFilePath, FileMode.Open, FileAccess.Read))
                {
                    ftpRequest.ContentLength = fileStream.Length;

                    using (Stream requestStream = ftpRequest.GetRequestStream())
                    {
                        // Daha büyük buffer size kullan (hızı artırır)
                        int bufferSize = 1048576; // 1024 KB buffer
                        byte[] buffer = new byte[bufferSize];
                        int bytesRead;
                        long totalBytesWritten = 0;

                        while ((bytesRead = fileStream.Read(buffer, 0, bufferSize)) > 0)
                        {
                            requestStream.Write(buffer, 0, bytesRead);
                            totalBytesWritten += bytesRead;

                            double progress = (double)totalBytesWritten / fileStream.Length * 100;

                        }
                    }
                }

                using FtpWebResponse response = (FtpWebResponse)ftpRequest.GetResponse();
                _ = setToolStatusLabel("Dosya FTP'ye Yüklendi!", "green");
                notificationSendToHtml("Dosya FTP'ye yüklendi.");
                }
            catch (WebException ex)
            {
                logger.Error(ex + " line 1399");
                _ = setToolStatusLabel("Dosya yüklenirken bir hata oldu!", "red");
                throw; // Hata yönetimini çağıran metoda bırak
            }
            catch (Exception es)
            {
                logger.Error(es + " line 1405");
                _ = setToolStatusLabel("Dosya yüklenirken bir hata oldu!", "red");
                throw; // Hata yönetimini çağıran metoda bırak
            }
        }
        private void isqadr_utilityExist()
        {
            Process lsProcess = GetProcess("adb", "shell ls "+ folderLocation.Replace("/qadr_utility","").Replace("\\","/"));
            lsProcess.Start();
            string lsOutput = lsProcess.StandardOutput.ReadToEnd().Trim();
            lsProcess.WaitForExit();
            if (!lsOutput.Contains("qadr_utility"))
            {
                Process mkdirProcess = GetProcess("adb", "shell mkdir " + folderLocation.Replace("\\","/"));
                mkdirProcess.Start();
                mkdirProcess.WaitForExit();
            }
        }
        // Get available storage locations method
        private void GetStorageLocations()
        {
            try
            {
                List<Dictionary<string, string>> locations = new List<Dictionary<string, string>>();

                // Android: Get storage locations
                if (selectedOS == "AOS")
                {
                    // Add internal storage
                    Dictionary<string, string> internalStorage = new Dictionary<string, string>();
                    internalStorage["name"] = "Internal Storage";
                    internalStorage["path"] = "/sdcard/qadr_utility";
                    internalStorage["size"] = "Internal Storage";
                    locations.Add(internalStorage);

                    // Check for external SD Card
                    string storagePath = "/storage";

                    try
                    {
                        Process process = new Process
                        {
                            StartInfo = new ProcessStartInfo
                            {
                                FileName = "adb",
                                Arguments = "shell ls " + storagePath,
                                RedirectStandardOutput = true,
                                UseShellExecute = false,
                                CreateNoWindow = true
                            }
                        };

                        process.Start();
                        string output = process.StandardOutput.ReadToEnd();
                        process.WaitForExit();

                        // Parse the output to find external storage
                        string[] entries = output.Split('\n');
                        foreach (string entry in entries)
                        {
                            string trimmed = entry.Trim();
                            if (!string.IsNullOrEmpty(trimmed) && !trimmed.Contains("emulated") && !trimmed.Contains("self") && !trimmed.Contains("Android"))
                            {
                                // This could be external storage
                                string externalPath = Path.Combine(storagePath, trimmed).Replace("\\","/");

                                // Get storage info
                                Process infoProcess = new Process
                                {
                                    StartInfo = new ProcessStartInfo
                                    {
                                        FileName = "adb",
                                        Arguments = $"shell df -h {externalPath}",
                                        RedirectStandardOutput = true,
                                        UseShellExecute = false,
                                        CreateNoWindow = true
                                    }
                                };

                                infoProcess.Start();
                                string infoOutput = infoProcess.StandardOutput.ReadToEnd();
                                infoProcess.WaitForExit();

                                // Extract size info if possible
                                string sizeInfo = "External Storage";
                                string[] infoLines = infoOutput.Split('\n');
                                if (infoLines.Length > 1)
                                {
                                    string[] parts = infoLines[1].Split(new char[] { ' ' }, StringSplitOptions.RemoveEmptyEntries);
                                    if (parts.Length >= 4)
                                    {
                                        sizeInfo = parts[1] + " / " + parts[3] + " free";
                                    }
                                }

                                Dictionary<string, string> externalStorage = new Dictionary<string, string>();
                                externalStorage["name"] = "SD Card (" + trimmed + ")";
                                externalStorage["path"] = Path.Combine(externalPath, "qadr_utility").Replace("\\", "/");
                                externalStorage["size"] = sizeInfo;
                                locations.Add(externalStorage);
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        logger.Error("Error getting external storage: " + ex.Message);
                    }                }
                
                // Use Invoke to ensure WebView operations run on the UI thread
                this.Invoke(new Action(() =>
                {
                    try
                    {
                        webView.CoreWebView2.PostWebMessageAsJson(JsonSerializer.Serialize(new
                        {
                            type = "storageLocations",
                            locations = locations
                        }));
                    }
                    catch (Exception invokeEx)
                    {
                        logger.Error("Error posting WebMessage on UI thread: " + invokeEx.Message);
                    }
                }));
            }
            catch (Exception ex)
            {
                logger.Error("Error in GetStorageLocations: " + ex.Message);
            }
        }
        // Save the selected storage location
        private void SaveStorageLocation(string location)
        {
            try
            {
                // Save to settings
                folderLocation = location.Replace("\\", "/");
                Properties.Settings.Default.FolderLocation = folderLocation;
                Properties.Settings.Default.Save();

                // Update the current folder location

                // webView.CoreWebView2.PostWebMessageAsJson(JsonSerializer.Serialize(new
                // {
                //     type = "storageLocations",
                //     locations = location
                // }));

                // Create the directory if it doesn't exist
                if (!string.IsNullOrEmpty(location))
                {
                    if (selectedOS == "AOS")
                    {
                        // For Android path
                        Process process = new Process
                        {
                            StartInfo = new ProcessStartInfo
                            {
                                FileName = "adb",
                                Arguments = $"shell mkdir -p {location}",
                                UseShellExecute = false,
                                CreateNoWindow = true
                            }
                        };
                        process.Start();
                        process.WaitForExit();
                    }
                    else
                    {
                        // For Windows path
                        if (!Directory.Exists(location))
                        {
                            Directory.CreateDirectory(location);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                logger.Error("Error saving storage location: " + ex.Message);
            }
        }
        // Send current folder location to WebView
        private void SendCurrentFolderLocation()
        {
            try
            {
                webView.CoreWebView2.PostWebMessageAsJson(JsonSerializer.Serialize(new
                {
                    type = "currentFolderLocation",
                    location = folderLocation
                }));
            }
            catch (Exception ex)
            {
                logger.Error("Error sending current folder location: " + ex.Message);
            }
        }
        private void getSS(bool isSave = false)
        {
            string fileName = "screenshot.png";
            string gameName = CleanFileName(selectedGame, "-");
            string imagePath = Path.Combine(Path.GetTempPath(), "qa_utility");
            if (isSave)
            {
                fileName = $"{gameName}_{Properties.Settings.Default.testername}_{DateTime.Now:yyyy-MM-dd-HH-mm-ss}.png".Replace(" ", "_");
                imagePath = Path.Combine(Application.StartupPath, "images/" + gameName + "/");
            }
            isqadr_utilityExist();

            Process screencapProcess = GetProcess("adb", "shell screencap -p " + folderLocation + "/" + fileName);
            screencapProcess.Start();
            screencapProcess.WaitForExit();
            if (!Directory.Exists(imagePath))
            {
                Directory.CreateDirectory(imagePath);
            }
            Process pullProcess = GetProcess("adb", "pull " + folderLocation + "/" + fileName + " \"" + imagePath + "\"");
            pullProcess.Start();
            pullProcess.WaitForExit();
            SendNotificationToDevice("Ekran Görüntüsü Alındı", "Ekran görüntüsü alındı.");
        }
        public void SendNotificationToDevice(string title, string message)
        {
            string tag = Guid.NewGuid().ToString("N");
            //adb shell am broadcast -a com.joygame.qautility.SHOW_NOTIFICATION -n com.joygame.qa_utility/.NotificationReceiver --es title \"Merhaba Dünya\" --es message \"Kendi Mesajınız\"
            string notificationCommand = $"adb shell cmd notification post -t \\\"{title}\\\" \\\"{tag}\\\" \\\"{message + DateTime.Now}\\\" -s bigtext";

            Process notificationProcess = new Process
            {
                StartInfo = new ProcessStartInfo
                {
                    FileName = "cmd.exe",
                    Arguments = $"/C {notificationCommand}",
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    UseShellExecute = false,
                    CreateNoWindow = true
                }
            };

            try
            {
                notificationProcess.Start();
                string output = notificationProcess.StandardOutput.ReadToEnd();
                string error = notificationProcess.StandardError.ReadToEnd();
                notificationProcess.WaitForExit();

                if (!string.IsNullOrEmpty(error))
                {
                    MessageBox.Show($"Bildirim gönderilemedi: {error}", "Hata", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Bildirim gönderilirken bir hata oluştu: {ex.Message}", "Hata", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        bool videoStatus = false;
        private int videoRecordCounter = 1;
        private string lastVideoBaseName = "";
        private void videoRecords()
        {
            // "Video Kaydını Durdur" durumunda işlem sonlandır
            if (videoStatus)
            {
                videoStatus = false;
                try
                {
                    Process killallProcess = GetProcess("adb", "shell killall -2 screenrecord");
                    killallProcess.Start();
                    killallProcess.WaitForExit();

                    // Reset counter when stopping recording
                    //videoRecordCounter = 1;
                    lastVideoBaseName = "";

                    // If we're stopping a continuous recording, make sure to disable continuous recording mode
                    if (videoRecorLoop)
                    {
                        videoRecorLoop = false;

                        // Update the checkbox in the UI
                        this.Invoke(new Action(() => {
                            if (webView?.CoreWebView2 != null)
                            {
                                webView.CoreWebView2.PostWebMessageAsJson(JsonSerializer.Serialize(new
                                {
                                    type = "updateContinuousRecording",
                                    value = false
                                }));
                            }
                        }));
                    }

                    try
                    {
                        // Use Invoke to ensure WebView2 operations run on the UI thread
                        this.Invoke(new Action(() => {
                            if (webView?.CoreWebView2 != null)
                            {
                                webView.CoreWebView2.PostWebMessageAsJson(JsonSerializer.Serialize(new
                                {
                                    type = "videoRecordingStatus",
                                    status = "stopped",
                                    message = "Video kaydı durduruldu"
                                }));
                            }
                        }));
                    }
                    catch (Exception webViewEx)
                    {
                        logger.Error($"WebView mesaj gönderme hatası: {webViewEx.Message} (Type: videoRecordingStatus, Status: stopped)");
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Video kaydını durdururken bir hata oluştu: {ex.Message}", "Hata", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            else // "Video Kaydını Başlat" durumunda yeni işlem başlat
            {
                videoStatus = true;
                try
                {
                    // Use Invoke to ensure WebView2 operations run on the UI thread
                    this.Invoke(new Action(() => {
                        if (webView?.CoreWebView2 != null)
                        {
                            webView.CoreWebView2.PostWebMessageAsJson(JsonSerializer.Serialize(new
                            {
                                type = "videoRecordingStatus",
                                status = "started",
                                message = "Video kaydı başlatıldı"
                            }));
                        }
                    }));
                }
                catch (Exception webViewEx)
                {
                    logger.Error($"WebView mesaj gönderme hatası: {webViewEx.Message} (Type: videoRecordingStatus, Status: started)");
                }
                isqadr_utilityExist();
                string gameName = CleanFileName(selectedGame, "-");
                string testerName = Properties.Settings.Default.testername;
                string timestamp = DateTime.Now.ToString("yyyy-MM-dd-HH-mm-ss");

                string counterSuffix = videoRecorLoop ? $"_{videoRecordCounter}" : "";
                string fileName = $"{folderLocation}/{gameName}_{Properties.Settings.Default.testername}_{DateTime.Now:yyyy-MM-dd-HH-mm-ss}{counterSuffix}.mp4";

                // Cihazın ekran çözünürlüğünü al
                string resolution = currentDeviceInfo?.Resolution;
                if (string.IsNullOrEmpty(resolution))
                {
                    resolution = "1280x720"; // Varsayılan çözünürlük
                }

                // ADB screenrecord için en iyi parametreler
                // --bit-rate: Yüksek kalite için 8Mbps (8000000)
                // --time-limit: Maksimum kayıt süresi (saniye cinsinden, varsayılan 180)
                string screenrecordParams = $"shell screenrecord --bit-rate=8000000 --time-limit=180 {fileName}";

                Process screenrecordProcess = GetProcess("adb", screenrecordParams);

                screenrecordProcess.EnableRaisingEvents = true;
                screenrecordProcess.Exited += (sender, args) =>
                {
                    try
                    {
                        Invoke(new Action(() =>
                        {
                            SendNotificationToDevice("Video Kaydı", "Video kaydı sona erdi.");

                            if (!videoRecorLoop) // Only show message box if not in continuous mode
                            {
                                MessageBox.Show("Video kaydı sona erdi.", "Bilgi", MessageBoxButtons.OK, MessageBoxIcon.Information);
                            }
                        }));
                    }
                    catch (Exception invokeEx)
                    {
                        logger.Error($"Video kaydı UI güncelleme hatası: {invokeEx.Message}");
                    }
                    try
                    {
                        if (videoRecorLoop)
                        {
                            // Use BeginInvoke to ensure videoRecords runs on the UI thread
                            this.BeginInvoke(new Action(() =>
                            {
                                try
                                {
                                    // Yeni kaydı başlatmak için kısa bir gecikme ekleyelim
                                    videoRecordCounter += 1;
                                    // videoStatus is set to false when the process exits, so we need to set it back to true
                                    videoStatus = false;
                                    videoRecords(); // Run videoRecords directly on the UI thread

                                }
                                catch (Exception recordEx)
                                {
                                    logger.Error($"Sürekli kayıt başlatma hatası: {recordEx.Message}");
                                }
                            }));
                        }
                        else
                        {
                            // Reset counter when not in continuous mode
                            videoRecordCounter = 1;
                            lastVideoBaseName = "";
                        }
                    }
                    catch (Exception loopEx)
                    {
                        logger.Error($"Sürekli kayıt kontrol hatası: {loopEx.Message}");
                        // Hata durumunda sayıcıları sıfırlayalım
                        videoRecordCounter = 1;
                        lastVideoBaseName = "";
                    }
                    // Use Invoke to ensure WebView2 operations run on the UI thread
                        this.Invoke(new Action(() => {
                            if (webView?.CoreWebView2 != null)
                            {
                                webView.CoreWebView2.PostWebMessageAsJson(JsonSerializer.Serialize(new
                                {
                                    type = "videoRecordingStatus",
                                    status = "stopped",
                                    message = "Video kaydı durduruldu"
                                }));
                            }
                        }));

                };

                try
                {
                    screenrecordProcess.Start();

                    // UI işleminin donmasını önlemek için arka planda bekle
                    Task.Run(() => screenrecordProcess.WaitForExit());
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Video kaydını başlatırken bir hata oluştu: {ex.Message}", "Hata", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }
        static string CleanFileName(string fileName, string type = "_")
        {
            string cleaned = Regex.Replace(fileName, @"\s+", type);
            cleaned = Regex.Replace(cleaned, @"[^\w\-]", "");
            return cleaned;
        }
        private static bool areYouSure(string subTitle = "Bu işlem sonrasında cihazın yeniden başlayacak!", string title = "Emin Misin?")
        {
            DialogResult dialogResult = MessageBox.Show(subTitle, title, MessageBoxButtons.YesNo);
            if (dialogResult == DialogResult.Yes)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        private void aosRestart()
        {
            Process rebootProcess = GetProcess("adb", "reboot");
            rebootProcess.Start();
        }
        private void iosRestart()
        {
            try
            {
                // Tam dosya yolunu kullan
                string ioslogPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "ioslog", "idevicediagnostics");

                Process restartProcess = GetProcess(ioslogPath, "restart");
                restartProcess.Start();
                notificationSendToHtml("iOS cihaz yeniden başlatılıyor...");
            }
            catch (Exception ex)
            {
                logger.Error($"iOS restart error: {ex.Message}");
                notificationSendToHtml("iOS cihaz yeniden başlatılamadı: " + ex.Message);
            }
        }
        private string GetReadableDeviceName(string productType)
        {
            // iOS cihaz kodlarını okunabilir isimlere dönüştür
            Dictionary<string, string> deviceNames = new Dictionary<string, string>
            {
                // iPhones
                {"iPhone1,1", "iPhone"},
                {"iPhone1,2", "iPhone 3G"},
                {"iPhone2,1", "iPhone 3GS"},
                {"iPhone3,1", "iPhone 4"},
                {"iPhone3,2", "iPhone 4 GSM Rev A"},
                {"iPhone3,3", "iPhone 4 CDMA"},
                {"iPhone4,1", "iPhone 4S"},
                {"iPhone5,1", "iPhone 5 (GSM)"},
                {"iPhone5,2", "iPhone 5 (GSM+CDMA)"},
                {"iPhone5,3", "iPhone 5c (GSM)"},
                {"iPhone5,4", "iPhone 5c (Global)"},
                {"iPhone6,1", "iPhone 5s (GSM)"},
                {"iPhone6,2", "iPhone 5s (Global)"},
                {"iPhone7,1", "iPhone 6 Plus"},
                {"iPhone7,2", "iPhone 6"},
                {"iPhone8,1", "iPhone 6s"},
                {"iPhone8,2", "iPhone 6s Plus"},
                {"iPhone8,4", "iPhone SE (1st gen)"},
                {"iPhone9,1", "iPhone 7"},
                {"iPhone9,2", "iPhone 7 Plus"},
                {"iPhone9,3", "iPhone 7"},
                {"iPhone9,4", "iPhone 7 Plus"},
                {"iPhone10,1", "iPhone 8"},
                {"iPhone10,2", "iPhone 8 Plus"},
                {"iPhone10,3", "iPhone X"},
                {"iPhone10,4", "iPhone 8"},
                {"iPhone10,5", "iPhone 8 Plus"},
                {"iPhone10,6", "iPhone X"},
                {"iPhone11,2", "iPhone XS"},
                {"iPhone11,4", "iPhone XS Max"},
                {"iPhone11,6", "iPhone XS Max"},
                {"iPhone11,8", "iPhone XR"},
                {"iPhone12,1", "iPhone 11"},
                {"iPhone12,3", "iPhone 11 Pro"},
                {"iPhone12,5", "iPhone 11 Pro Max"},
                {"iPhone12,8", "iPhone SE (2nd gen)"},
                {"iPhone13,1", "iPhone 12 mini"},
                {"iPhone13,2", "iPhone 12"},
                {"iPhone13,3", "iPhone 12 Pro"},
                {"iPhone13,4", "iPhone 12 Pro Max"},
                {"iPhone14,2", "iPhone 13 Pro"},
                {"iPhone14,3", "iPhone 13 Pro Max"},
                {"iPhone14,4", "iPhone 13 mini"},
                {"iPhone14,5", "iPhone 13"},
                {"iPhone14,6", "iPhone SE (3rd gen)"},
                {"iPhone14,7", "iPhone 14"},
                {"iPhone14,8", "iPhone 14 Plus"},
                {"iPhone15,2", "iPhone 14 Pro"},
                {"iPhone15,3", "iPhone 14 Pro Max"},
                {"iPhone15,4", "iPhone 15"},
                {"iPhone15,5", "iPhone 15 Plus"},
                {"iPhone16,1", "iPhone 15 Pro"},
                {"iPhone16,2", "iPhone 15 Pro Max"},

                // iPads
                {"iPad1,1", "iPad"},
                {"iPad2,1", "iPad 2 (WiFi)"},
                {"iPad2,2", "iPad 2 (GSM)"},
                {"iPad2,3", "iPad 2 (CDMA)"},
                {"iPad2,4", "iPad 2 (WiFi Rev A)"},
                {"iPad2,5", "iPad mini (WiFi)"},
                {"iPad2,6", "iPad mini (GSM)"},
                {"iPad2,7", "iPad mini (CDMA)"},
                {"iPad3,1", "iPad 3 (WiFi)"},
                {"iPad3,2", "iPad 3 (CDMA)"},
                {"iPad3,3", "iPad 3 (GSM)"},
                {"iPad3,4", "iPad 4 (WiFi)"},
                {"iPad3,5", "iPad 4 (GSM)"},
                {"iPad3,6", "iPad 4 (CDMA)"},
                {"iPad4,1", "iPad Air (WiFi)"},
                {"iPad4,2", "iPad Air (Cellular)"},
                {"iPad4,3", "iPad Air (China)"},
                {"iPad4,4", "iPad mini 2 (WiFi)"},
                {"iPad4,5", "iPad mini 2 (Cellular)"},
                {"iPad4,6", "iPad mini 2 (China)"},
                {"iPad4,7", "iPad mini 3 (WiFi)"},
                {"iPad4,8", "iPad mini 3 (Cellular)"},
                {"iPad4,9", "iPad mini 3 (China)"},
                {"iPad5,1", "iPad mini 4 (WiFi)"},
                {"iPad5,2", "iPad mini 4 (Cellular)"},
                {"iPad5,3", "iPad Air 2 (WiFi)"},
                {"iPad5,4", "iPad Air 2 (Cellular)"},
                {"iPad6,3", "iPad Pro 9.7-inch (WiFi)"},
                {"iPad6,4", "iPad Pro 9.7-inch (Cellular)"},
                {"iPad6,7", "iPad Pro 12.9-inch (WiFi)"},
                {"iPad6,8", "iPad Pro 12.9-inch (Cellular)"},
                {"iPad6,11", "iPad 5th gen (WiFi)"},
                {"iPad6,12", "iPad 5th gen (Cellular)"},
                {"iPad7,1", "iPad Pro 12.9-inch 2nd gen (WiFi)"},
                {"iPad7,2", "iPad Pro 12.9-inch 2nd gen (Cellular)"},
                {"iPad7,3", "iPad Pro 10.5-inch (WiFi)"},
                {"iPad7,4", "iPad Pro 10.5-inch (Cellular)"},
                {"iPad7,5", "iPad 6th gen (WiFi)"},
                {"iPad7,6", "iPad 6th gen (Cellular)"},
                {"iPad7,11", "iPad 7th gen (WiFi)"},
                {"iPad7,12", "iPad 7th gen (Cellular)"},
                {"iPad8,1", "iPad Pro 11-inch (WiFi)"},
                {"iPad8,2", "iPad Pro 11-inch (WiFi, 1TB)"},
                {"iPad8,3", "iPad Pro 11-inch (Cellular)"},
                {"iPad8,4", "iPad Pro 11-inch (Cellular, 1TB)"},
                {"iPad8,5", "iPad Pro 12.9-inch 3rd gen (WiFi)"},
                {"iPad8,6", "iPad Pro 12.9-inch 3rd gen (WiFi, 1TB)"},
                {"iPad8,7", "iPad Pro 12.9-inch 3rd gen (Cellular)"},
                {"iPad8,8", "iPad Pro 12.9-inch 3rd gen (Cellular, 1TB)"},
                {"iPad8,9", "iPad Pro 11-inch 2nd gen (WiFi)"},
                {"iPad8,10", "iPad Pro 11-inch 2nd gen (Cellular)"},
                {"iPad8,11", "iPad Pro 12.9-inch 4th gen (WiFi)"},
                {"iPad8,12", "iPad Pro 12.9-inch 4th gen (Cellular)"},
                {"iPad11,1", "iPad mini 5th gen (WiFi)"},
                {"iPad11,2", "iPad mini 5th gen (Cellular)"},
                {"iPad11,3", "iPad Air 3rd gen (WiFi)"},
                {"iPad11,4", "iPad Air 3rd gen (Cellular)"},
                {"iPad11,6", "iPad 8th gen (WiFi)"},
                {"iPad11,7", "iPad 8th gen (Cellular)"},
                {"iPad12,1", "iPad 9th gen (WiFi)"},
                {"iPad12,2", "iPad 9th gen (Cellular)"},
                {"iPad13,1", "iPad Air 4th gen (WiFi)"},
                {"iPad13,2", "iPad Air 4th gen (Cellular)"},
                {"iPad13,4", "iPad Pro 11-inch 3rd gen (WiFi)"},
                {"iPad13,5", "iPad Pro 11-inch 3rd gen (Cellular)"},
                {"iPad13,6", "iPad Pro 11-inch 3rd gen (Cellular, US)"},
                {"iPad13,7", "iPad Pro 11-inch 3rd gen (Cellular, Global)"},
                {"iPad13,8", "iPad Pro 12.9-inch 5th gen (WiFi)"},
                {"iPad13,9", "iPad Pro 12.9-inch 5th gen (Cellular)"},
                {"iPad13,10", "iPad Pro 12.9-inch 5th gen (Cellular, US)"},
                {"iPad13,11", "iPad Pro 12.9-inch 5th gen (Cellular, Global)"},
                {"iPad13,16", "iPad Air 5th gen (WiFi)"},
                {"iPad13,17", "iPad Air 5th gen (Cellular)"},
                {"iPad13,18", "iPad 10th gen (WiFi)"},
                {"iPad13,19", "iPad 10th gen (Cellular)"},
                {"iPad14,1", "iPad mini 6th gen (WiFi)"},
                {"iPad14,2", "iPad mini 6th gen (Cellular)"},
                {"iPad14,3", "iPad Pro 11-inch 4th gen (WiFi)"},
                {"iPad14,4", "iPad Pro 11-inch 4th gen (Cellular)"},
                {"iPad14,5", "iPad Pro 12.9-inch 6th gen (WiFi)"},
                {"iPad14,6", "iPad Pro 12.9-inch 6th gen (Cellular)"},

                // iPod touch
                {"iPod1,1", "iPod touch"},
                {"iPod2,1", "iPod touch 2nd gen"},
                {"iPod3,1", "iPod touch 3rd gen"},
                {"iPod4,1", "iPod touch 4th gen"},
                {"iPod5,1", "iPod touch 5th gen"},
                {"iPod7,1", "iPod touch 6th gen"},
                {"iPod9,1", "iPod touch 7th gen"}
            };

            // Eğer sözlükte varsa okunabilir ismi döndür, yoksa orijinal kodu döndür
            return deviceNames.ContainsKey(productType) ? deviceNames[productType] : productType;
        }
        private void appKapaniyo()
        {
            try
            {
                tempDelete();
                killProcess("shell");
                adbLogger.CancelOutputRead();
                adbLogger.Close();
            }
            catch (Exception ex)
            {
                logger.Error(ex + " line 1552");
            }
            taskKiller("idevicesyslog.exe");
            taskKiller("adb.exe");
        }
        private static void taskKiller(string name)
        {
            try
            {
                Process taskKill = GetProcess("taskkill", "/f /im " + name);
                taskKill.Start();
                taskKill.WaitForExit();
            }
            catch (Exception ex)
            {
                logger.Error(ex + " line 1567");
                MessageBox.Show("Bir soun oluştu. Geliştiriciye bilgi verin! Hata Kodu : 3358", "Hata");
            }
        }
        private void Form1_FormClosing(object sender, FormClosingEventArgs e)
        {
            //appKapaniyo();
        }
        private static void wifiAyarla(string state)
        {
            Process wifiProcess = GetProcess("adb", "shell svc wifi " + state);
            wifiProcess.Start();
        }
        private void removeApp(string bundleid)
        {
            SendNotificationToDevice("Uygulama Kaldırıldı", bundleid + " kaldırıldı.");
            Process runApp = GetProcess("adb", "uninstall " + bundleid);
            runApp.Start();
            runApp.WaitForExit();
        }
        public void wificonnection(string port = "5555"){
            Process runApp = GetProcess("adb", "tcpip " + port);
            runApp.Start();
            runApp.WaitForExit();
            Thread.Sleep(3000);
            Process runApp2 = GetProcess("adb", "shell ip addr show wlan0");
            runApp2.Start();
            runApp2.WaitForExit();
            string returndata = runApp2.StandardOutput.ReadToEnd();
            string[] lines = returndata.Split('\n');
            string[] words = lines[2].Split(' ');
            string ip = words[5].Split('/')[0];
            Process runApp3 = GetProcess("adb", "connect " + ip + ":"+ port);
            runApp3.Start();
            runApp3.WaitForExit();
            isWirelessConnection = true;
            UpdateDeviceStatus();
            notificationSendToHtml("Wireless bağlantısı kuruldu.");
            
            // Kablosuz bağlantı düğmesinin durumunu güncelle
            webView?.CoreWebView2?.PostWebMessageAsJson(JsonSerializer.Serialize(new
            {
                type = "updateWirelessButtonState",
                isWireless = true
            }));
        }
        public void disconnectWireless()
        {
            // Sadece kablosuz bağlantı varsa kes
            if (isWirelessConnection)
            {
                // ADB'yi USB moduna geri döndür
                Process runApp = GetProcess("adb", "usb");
                runApp.Start();
                runApp.WaitForExit();
                Thread.Sleep(2000);
                
                // Kablosuz bağlantı durumunu güncelle
                isWirelessConnection = false;
                UpdateDeviceStatus();
                notificationSendToHtml("USB bağlantısına geçildi.");
                
                // Kablosuz bağlantı düğmesinin durumunu güncelle
                webView?.CoreWebView2?.PostWebMessageAsJson(JsonSerializer.Serialize(new
                {
                    type = "updateWirelessButtonState",
                    isWireless = false
                }));
            }
            else
            {
                notificationSendToHtml("Zaten USB bağlantısı kullanılıyor.");
            }
        }
        public void notificationSendToHtml(string message = "", string type = "notification")
        {
            try
            {
                // UI thread'de çalıştığından emin ol
                this.Invoke(new Action(() => {
                    // WebView2 kontrolünün hazır olduğundan emin ol
                    if (webView?.CoreWebView2 != null)
                    {
                        webView.CoreWebView2.PostWebMessageAsJson(JsonSerializer.Serialize(new
                        {
                            type = type,
                            message = message
                        }));
                    }
                    else
                    {
                        logger.Warn($"WebView2 is not ready for notification: {message}");
                    }
                }));
            }
            catch (Exception ex)
            {
                logger.Error($"Error sending notification to HTML: {ex.Message}");
            }
        }
        private void Form1_DragEnter(object sender, DragEventArgs e)
        {

        }
        public async Task setToolStatusLabel(string text = "", string renk = "green", bool manuelupdate = false, int sleep = 3000)
        {
            UpdateStatus(text,renk);
            if (manuelupdate == false)
            {
                await Task.Delay(sleep);
                UpdateStatus("Hazır",renk);
            }
        }
        private static void discordaGonder(string json)
        {
            string url = "https://discord.com/api/webhooks/1189848358261637150/K6tFAGllJYm1qthPg4tP_-gujcABir30C_E27QScFIFARdukIVAYk4NR78dzk5EVtrcD";
            var httpWebRequest = (HttpWebRequest)WebRequest.Create(url);
            httpWebRequest.ContentType = "application/json";
            httpWebRequest.Method = "POST";
            using (var streamWriter = new StreamWriter(httpWebRequest.GetRequestStream()))
            {
                streamWriter.Write(json);
            }
            var httpResponse = (HttpWebResponse)httpWebRequest.GetResponse();
            using var streamReader = new StreamReader(httpResponse.GetResponseStream());
            var result = streamReader.ReadToEnd();
        }
        private static void tempDelete()
        {
            string tempPath = Path.Combine(Path.GetTempPath(), "qa_utility");
            if (Directory.Exists(tempPath))
            {
                Directory.Delete(tempPath, true);
            }
        }
        private void openUrl(string link)
        {
            Process runApp = GetProcess("adb", "shell am start -a android.intent.action.VIEW -d " + link);
            runApp.Start();
            runApp.WaitForExit();
            SendNotificationToDevice("Url Açıldı", link + " açıldı.");
        }
        public void runSql(string queryString)
        {
            try
            {
                if (coon.State != ConnectionState.Open)
                {
                    coon.Open();
                }
                using (MySqlCommand cmd = new MySqlCommand(queryString, coon))
                {
                    cmd.ExecuteNonQuery();
                }
                coon.Close();
            }
            catch (Exception ex)
            {
                logger.Error($"SQL error: {ex.Message}");
                // Only show message box if it's a critical error
                // Avoid closing the application for non-critical errors
                if (ex.Message.Contains("Unable to connect") || ex.Message.Contains("Connection"))
                {
                    MessageBox.Show("İnternet bağlantısında bir sorun oluştu.");
                    this.Close();
                }
            }
        }
        public async Task runSqlAsync(string queryString)
        {
            try
            {
                await Task.Run(() => {
                    if (coon.State != ConnectionState.Open)
                    {
                        coon.Open();
                    }
                    using (MySqlCommand cmd = new MySqlCommand(queryString, coon))
                    {
                        cmd.ExecuteNonQuery();
                    }
                    coon.Close();
                });
            }
            catch (Exception ex)
            {
                logger.Error($"Async SQL error: {ex.Message}");
                // Only show message box if it's a critical error
                // Avoid closing the application for non-critical errors
                if (ex.Message.Contains("Unable to connect") || ex.Message.Contains("Connection"))
                {
                    MessageBox.Show("İnternet bağlantısında bir sorun oluştu.");
                    this.Close();
                }
            }
        }
        // Öncelikle video/ss verilerini temsil eden yardımcı bir sınıf tanımlayalım:
        public class VideoSsData
            {
                public string OyunAdi { get; set; }
                public string Tarih { get; set; }
                public string Testci { get; set; }
                public string Tip { get; set; }
                public string DosyaAdi { get; set; }
                public string Status { get; set; }
                public string DriveUrl { get; set; }
        }
        // Ardından verileri toplayıp WebView2'ye gönderelim:
        private void SendVideoSsData()
        {
            try
            {
                // ADB ile cihazdaki video/ss dosyalarını listele
                Process listProcess = GetProcess("adb", "shell ls " + folderLocation + "/");
                listProcess.Start();
                string output = listProcess.StandardOutput.ReadToEnd();
                listProcess.WaitForExit();

                // Satırları ayrıştır
                string[] files = output.Split(new[] { '\r', '\n' }, StringSplitOptions.RemoveEmptyEntries);
                List<VideoSsData> dataList = new List<VideoSsData>();

                // Yükleme durumlarını yükle
                VideoSsUploadStatus uploadStatus = LoadVideoSsUploadStatus();

                foreach (var file in files)
                {
                    string fileTrimmed = file.Trim();
                    if (string.IsNullOrEmpty(fileTrimmed))
                        continue;

                    // Sadece .mp4 ve .png dosyalarını işleyelim
                    string ext = Path.GetExtension(fileTrimmed).ToLower();
                    if (ext != ".mp4" && ext != ".png")
                        continue;

                    // Dosya adından verileri ayrıştıralım.
                    // Örneğin: "OyunAdi_Testci_Tarih.mp4" şeklinde isimlendirilmiş olduğunu varsayıyoruz.
                    string fileNameWithoutExt = Path.GetFileNameWithoutExtension(fileTrimmed);
                    string[] parts = fileNameWithoutExt.Split('_');
                    if (parts.Length < 3)
                        continue;

                    string oyunAdi = parts[0].Replace("-", " ");
                    string testci = parts[1];
                    string tarih = parts[2]; // Tarihi daha okunabilir formata dönüştürebilirsiniz
                    string tip = (ext == ".mp4") ? "Video" : "Screenshot";

                    // Dosyanın yükleme durumunu kontrol et
                    string status = "";
                    string driveUrl = "";

                    if (uploadStatus.FileStatuses.TryGetValue(fileTrimmed, out FileUploadInfo fileInfo))
                    {
                        status = fileInfo.Status;
                        driveUrl = fileInfo.DriveUrl;
                    }

                    dataList.Add(new VideoSsData
                    {
                        OyunAdi = oyunAdi,
                        Testci = testci,
                        Tarih = tarih,
                        Tip = tip,
                        DosyaAdi = fileTrimmed,
                        Status = status,
                        DriveUrl = driveUrl
                    });
                }

                // WebView2'ye JSON mesajı gönderelim - UI thread'de çalıştır
                this.Invoke(new Action(() => {
                    if (webView?.CoreWebView2 != null)
                    {
                        var message = new
                        {
                            type = "videoSsData",
                            data = dataList
                        };

                        string jsonMessage = JsonSerializer.Serialize(message);
                        webView.CoreWebView2.PostWebMessageAsJson(jsonMessage);
                    }
                }));
            }
            catch (Exception ex)
            {
                MessageBox.Show("Video ve SS verileri gönderilirken hata oluştu: " + ex.Message);
            }
        }
        private void killAdbServer(){
            Process runApp = GetProcess("adb", "kill-server");
            runApp.Start();
            runApp.WaitForExit();
            
            // ADB server kapatıldığında wireless bağlantı durumunu sıfırla
            isWirelessConnection = false;
            
            // Kablosuz bağlantı düğmesinin durumunu güncelle
            webView?.CoreWebView2?.PostWebMessageAsJson(JsonSerializer.Serialize(new
            {
                type = "updateWirelessButtonState",
                isWireless = false
            }));
            
            notificationSendToHtml("ADB Server kapatıldı.");
        }
        private void dosyayiCihazdanTempeGetir(string selectedItem, bool start = false)
        {
            string folderPath = Path.Combine(Path.GetTempPath(), "qa_utility");
            string filePath = Path.Combine(folderPath, selectedItem);
            _ = setToolStatusLabel("Dosya kopyalanıyor.", manuelupdate: true, renk: "yellow");

            if (!Directory.Exists(folderPath))
            {
                Directory.CreateDirectory(folderPath);
            }
            if (File.Exists(filePath))
            {

            }
            else
            {
                Process pullProcess = GetProcess("adb", "pull " + folderLocation + "/" + selectedItem + " \"" + filePath + "\"");
                pullProcess.Start();
                pullProcess.WaitForExit();
            }
            if (start)
            {
                Process.Start(new ProcessStartInfo(filePath) { UseShellExecute = true });
            }
            else
            {
                //Clipboard.SetFileDropList(new System.Collections.Specialized.StringCollection() { filePath });
                // support multiple file copy

            }
            _ = setToolStatusLabel("Dosya kopyalandı.", "green");
        }
        private void deleteFunction(string filename)
        {
            Process runApp = GetProcess("adb", "shell rm " + folderLocation + "/" + filename);
            runApp.Start();
            runApp.WaitForExit();

            // Dosyayı yükleme durumlarından da kaldır
            VideoSsUploadStatus uploadStatus = LoadVideoSsUploadStatus();
            if (uploadStatus.FileStatuses.ContainsKey(filename))
            {
                uploadStatus.FileStatuses.Remove(filename);
                SaveVideoSsUploadStatus(uploadStatus);
            }

            _ = setToolStatusLabel("Dosya silindi.", "red");
            SendNotificationToDevice("Dosya silindi", filename + " dosyası cihazdan silindi.");

            // Video ve SS verilerini güncelle
            SendVideoSsData();
        }
        private string getCurrentAppPackage()
        {
            Process runApp = GetProcess("adb", "shell");
            runApp.StartInfo.RedirectStandardInput = true;
            runApp.Start();
            runApp.StandardInput.WriteLine("dumpsys activity activities | grep \"mCurrentFocus\"");
            runApp.StandardInput.Flush();
            runApp.StandardInput.Close();
            runApp.WaitForExit();
            string returndata = runApp.StandardOutput.ReadToEnd();
            string[] lines = returndata.Split('\n');
            string[] words = lines[0].Split(' ');
            if (words.Length < 4)
            {
                return "";
            }
            string bundleid = words[4].Split('/')[0];
            return bundleid;
        }
        private string dataTableFind(DataTable dt, string aramaAnahtari, string aranandeger, string aranancolumn)
        {
            var result = dt.AsEnumerable().FirstOrDefault(row => row.Field<string>(aramaAnahtari) == aranandeger);
            if (result != null)
            {
                return result.Field<string>(aranancolumn);
            }
            return null;
        }
        private string getGameDataFromDataTable(string gameName, string aranancolumn = "aosinternal")
        {
            var result = allGamesDatas.AsEnumerable().FirstOrDefault(row => row.Field<string>("name") == gameName);
            if (result != null)
            {
                return result.Field<string>(aranancolumn);
            }
            return null;
        }
        private void Form1_FormClosed(object sender, FormClosedEventArgs e)
        {
            appKapaniyo();
        }
        private async Task SendVersionDataToWebView()
        {
            try
            {
                // Sürüm verilerini al
                DataTable versionTable = new DataTable();
                string sql = "SELECT * FROM version";
                MySqlDataAdapter adapter = new MySqlDataAdapter(sql, coon);
                adapter.Fill(versionTable);

                // Verileri JSON formatına dönüştür
                var versions = new List<object>();
                foreach (DataRow row in versionTable.Rows)
                {
                    versions.Add(new
                    {
                        app = row["app"].ToString(),
                        version = row["version"].ToString()
                    });
                }

                // WebView2'ye JSON mesajı gönder
                if (webView?.CoreWebView2 != null)
                {
                    var message = new
                    {
                        type = "versionData",
                        data = versions
                    };

                    string jsonMessage = JsonSerializer.Serialize(message);
                    this.Invoke(new Action(() => webView.CoreWebView2.PostWebMessageAsJson(jsonMessage)));

                    // Bildirim göster
                    notificationSendToHtml("Sürüm verileri başarıyla yüklendi.");
                }
            }
            catch (Exception ex)
            {
                logger.Error($"Version data sending error: {ex.Message}");
                notificationSendToHtml("Sürüm verileri yüklenirken hata oluştu.", "error");
            }
        }
        private async void UpdateVersionInDatabase(string app, string version)
        {
            try
            {
                if (string.IsNullOrEmpty(app) || string.IsNullOrEmpty(version))
                {
                    notificationSendToHtml("Uygulama adı veya sürüm boş olamaz!", "error");
                    return;
                }

                string updateQuery = $"UPDATE version SET version = '{version}' WHERE app = '{app}'";
                runSql(updateQuery);

                notificationSendToHtml($"{app} uygulamasının sürümü başarıyla güncellendi.", "success");

                // Güncelleme işleminden sonra verileri otomatik olarak yenile
                await SendVersionDataToWebView();
            }
            catch (Exception ex)
            {
                logger.Error($"Version update error: {ex.Message}");
                notificationSendToHtml("Sürüm güncellenirken hata oluştu.", "error");
            }
        }
        private async Task SendStudioQADataToWebView()
        {
            try
            {
                // Studio QA verilerini al
                DataTable studioQATable = new DataTable();
                string sql = "SELECT * FROM studioQA";
                MySqlDataAdapter adapter = new MySqlDataAdapter(sql, coon);
                adapter.Fill(studioQATable);

                // Verileri JSON formatına dönüştür
                var studioQAData = new List<object>();
                foreach (DataRow row in studioQATable.Rows)
                {
                    studioQAData.Add(new
                    {
                        id = row["id"].ToString(),
                        tester = row["Tester"].ToString(),
                        priority1 = row["Priority1"].ToString(),
                        priority2 = row["Priority2"].ToString(),
                        priority3 = row["Priority3"].ToString(),
                        priority4 = row["Priority4"].ToString(),
                        priority5 = row["Priority5"].ToString(),
                        priority6 = row["Priority6"].ToString()
                    });
                }

                // WebView2'ye JSON mesajı gönder
                if (webView?.CoreWebView2 != null)
                {
                    var message = new
                    {
                        type = "studioQAData",
                        data = studioQAData
                    };

                    string jsonMessage = JsonSerializer.Serialize(message);
                    this.Invoke(new Action(() => webView.CoreWebView2.PostWebMessageAsJson(jsonMessage)));

                    // Bildirim göster
                    notificationSendToHtml("Studio QA verileri başarıyla yüklendi.");
                }
            }
            catch (Exception ex)
            {
                logger.Error($"Studio QA data sending error: {ex.Message}");
                notificationSendToHtml("Studio QA verileri yüklenirken hata oluştu.", "error");
            }
        }
        private async void AddStudioQAToDatabase(string tester, string priority1, string priority2, string priority3, string priority4, string priority5, string priority6)
        {
            try
            {
                if (string.IsNullOrEmpty(tester))
                {
                    notificationSendToHtml("Tester alanı boş olamaz!", "error");
                    return;
                }

                string insertQuery = $"INSERT INTO studioQA (Tester, Priority1, Priority2, Priority3, Priority4, Priority5, Priority6) VALUES ('{tester}', '{priority1}', '{priority2}', '{priority3}', '{priority4}', '{priority5}', '{priority6}')";
                runSql(insertQuery);

                notificationSendToHtml("Yeni Studio QA kaydı başarıyla eklendi.", "success");

                // Ekleme işleminden sonra verileri otomatik olarak yenile
                await SendStudioQADataToWebView();
            }
            catch (Exception ex)
            {
                logger.Error($"Studio QA add error: {ex.Message}");
                notificationSendToHtml("Studio QA kaydı eklenirken hata oluştu.", "error");
            }
        }
        private async void UpdateStudioQAInDatabase(string id, string tester, string priority1, string priority2, string priority3, string priority4, string priority5, string priority6)
        {
            try
            {
                if (string.IsNullOrEmpty(id) || string.IsNullOrEmpty(tester))
                {
                    notificationSendToHtml("ID veya Tester alanı boş olamaz!", "error");
                    return;
                }

                string updateQuery = $"UPDATE studioQA SET Tester = '{tester}', Priority1 = '{priority1}', Priority2 = '{priority2}', Priority3 = '{priority3}', Priority4 = '{priority4}', Priority5 = '{priority5}', Priority6 = '{priority6}' WHERE id = {id}";
                runSql(updateQuery);

                notificationSendToHtml("Studio QA kaydı başarıyla güncellendi.", "success");

                // Güncelleme işleminden sonra verileri otomatik olarak yenile
                await SendStudioQADataToWebView();
            }
            catch (Exception ex)
            {
                logger.Error($"Studio QA update error: {ex.Message}");
                notificationSendToHtml("Studio QA kaydı güncellenirken hata oluştu.", "error");
            }
        }
        private async void DeleteStudioQAFromDatabase(string id)
        {
            try
            {
                if (string.IsNullOrEmpty(id))
                {
                    notificationSendToHtml("ID alanı boş olamaz!", "error");
                    return;
                }

                string deleteQuery = $"DELETE FROM studioQA WHERE id = {id}";
                runSql(deleteQuery);

                notificationSendToHtml("Studio QA kaydı başarıyla silindi.", "success");

                // Silme işleminden sonra verileri otomatik olarak yenile
                await SendStudioQADataToWebView();
            }
            catch (Exception ex)
            {
                logger.Error($"Studio QA delete error: {ex.Message}");
                notificationSendToHtml("Studio QA kaydı silinirken hata oluştu.", "error");
            }
        }
        private async Task SendDataViewerFilesToWebView()
        {
            try
            {
                // FTP'den dosyaları listele
                List<FtpFileInfo> files = await GetAllFilesFromFtp();

                // Verileri JSON formatına dönüştür
                var fileList = new List<object>();
                foreach (var file in files)
                {
                    fileList.Add(new
                    {
                        fileName = file.Name,
                        date = file.LastModified.ToString("yyyy-MM-dd HH:mm:ss"),
                        size = file.Size
                    });
                }

                // WebView2'ye JSON mesajı gönder
                if (webView?.CoreWebView2 != null)
                {
                    var message = new
                    {
                        type = "dataViewerFiles",
                        data = fileList
                    };

                    string jsonMessage = JsonSerializer.Serialize(message);
                    this.Invoke(new Action(() => webView.CoreWebView2.PostWebMessageAsJson(jsonMessage)));

                    // Bildirim göster
                    notificationSendToHtml("Dosyalar başarıyla yüklendi.");
                }
            }
            catch (Exception ex)
            {
                logger.Error($"Data viewer files sending error: {ex.Message}");
                notificationSendToHtml("Dosyalar yüklenirken hata oluştu.", "error");
            }
        }
        private async Task<List<FtpFileInfo>> GetAllFilesFromFtp()
        {
            List<FtpFileInfo> fileList = new List<FtpFileInfo>();

            try
            {
                // FTP bağlantısı oluştur - loglar klasörünü hedefle
                string logsFolderPath = $"{ftpServer}/loglar";
                FtpWebRequest request = (FtpWebRequest)WebRequest.Create(logsFolderPath);
                request.Method = WebRequestMethods.Ftp.ListDirectoryDetails;
                request.Credentials = new NetworkCredential(ftpUsername, ftpPassword);

                // FTP'den dosya listesini al
                using (FtpWebResponse response = (FtpWebResponse)await request.GetResponseAsync())
                using (Stream responseStream = response.GetResponseStream())
                using (StreamReader reader = new StreamReader(responseStream))
                {
                    string line;
                    while ((line = await reader.ReadLineAsync()) != null)
                    {
                        try
                        {
                            // FTP liste formatını parse et
                            // Örnek: -rw-r--r-- 1 <USER> <GROUP> 12345 Jan 1 12:34 filename.txt
                            string[] tokens = line.Split(new[] { ' ' }, StringSplitOptions.RemoveEmptyEntries);
                            if (tokens.Length >= 9)
                            {
                                string permissions = tokens[0];
                                // Dizin değilse (dosya ise) ekle
                                if (!permissions.StartsWith("d"))
                                {
                                    string sizeStr = tokens[4];
                                    long size = long.Parse(sizeStr);

                                    // Dosya adını al (son token'dan itibaren)
                                    string fileName = string.Join(" ", tokens.Skip(8));

                                    DateTime lastModified;
                                    try
                                    {
                                        // FTP sunucusundan gelen tarih formatını analiz et
                                        // Önce tüm tarih bilgilerini birleştir
                                        string dateString = string.Join(" ", tokens.Skip(5).Take(tokens.Length - 8));

                                        // Tarih formatını ayrıştırmayı dene
                                        if (DateTime.TryParse(dateString, out lastModified))
                                        {
                                            // Başarılı ayrıştırma
                                        }
                                        else
                                        {
                                            // Varsayılan tarih kullan
                                            lastModified = DateTime.Now;
                                            logger.Warn($"Could not parse date: {dateString}, using current date instead.");
                                        }
                                    }
                                    catch (Exception ex)
                                    {
                                        // Hata durumunda varsayılan tarih kullan
                                        lastModified = DateTime.Now;
                                        logger.Warn($"Error parsing date: {ex.Message}, using current date instead.");
                                    }

                                    fileList.Add(new FtpFileInfo
                                    {
                                        Name = fileName,
                                        Size = size,
                                        LastModified = lastModified
                                    });
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            logger.Error($"Error parsing FTP line: {ex.Message}");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                logger.Error($"Error getting files from FTP: {ex.Message}");
                throw;
            }

            return fileList;
        }
        private void DownloadAndOpenFile(string fileName)
        {
            try
            {
                // Temp klasörünü kontrol et
                string tempFolder = Path.Combine(Path.GetTempPath(), "qa_utility");
                if (!Directory.Exists(tempFolder))
                {
                    Directory.CreateDirectory(tempFolder);
                }

                // Dosya yolunu oluştur
                string localFilePath = Path.Combine(tempFolder, fileName);

                // Dosya zaten indirilmiş mi kontrol et
                if (!File.Exists(localFilePath))
                {
                    // FTP'den dosyayı indir - loglar klasöründen
                    string ftpFilePath = $"{ftpServer}/loglar/{fileName}";
                    FtpWebRequest request = (FtpWebRequest)WebRequest.Create(ftpFilePath);
                    request.Method = WebRequestMethods.Ftp.DownloadFile;
                    request.Credentials = new NetworkCredential(ftpUsername, ftpPassword);

                    using (FtpWebResponse response = (FtpWebResponse)request.GetResponse())
                    using (Stream responseStream = response.GetResponseStream())
                    using (FileStream fileStream = File.Create(localFilePath))
                    {
                        byte[] buffer = new byte[8192];
                        int bytesRead;
                        while ((bytesRead = responseStream.Read(buffer, 0, buffer.Length)) > 0)
                        {
                            fileStream.Write(buffer, 0, bytesRead);
                        }
                    }

                    notificationSendToHtml($"{fileName} dosyası başarıyla indirildi.", "success");
                }
                else
                {
                    notificationSendToHtml($"{fileName} dosyası zaten indirilmiş.", "info");
                }

                // Dosyayı aç
                Process.Start(new ProcessStartInfo(localFilePath) { UseShellExecute = true });
            }
            catch (Exception ex)
            {
                logger.Error($"Error downloading and opening file: {ex.Message}");
                notificationSendToHtml($"Dosya indirilirken veya açılırken hata oluştu: {ex.Message}", "error");
            }
        }
        // FTP dosya bilgilerini tutan yardımcı sınıf
        private class FtpFileInfo
        {
            public string Name { get; set; }
            public long Size { get; set; }
            public DateTime LastModified { get; set; }
        }
        // Seçili dosyaları arşive taşıyan metod
        private void ArchiveFiles(List<string> fileNames)
        {
            try
            {
                if (fileNames == null || fileNames.Count == 0)
                {
                    SendArchiveResult(false, "Arşive taşınacak dosya seçilmedi.");
                    return;
                }

                // Arşiv klasörünü kontrol et, yoksa oluştur
                string archiveFolderPath = $"{ftpServer}/logArchive";
                try
                {
                    FtpWebRequest request = (FtpWebRequest)WebRequest.Create(archiveFolderPath);
                    request.Method = WebRequestMethods.Ftp.ListDirectory;
                    request.Credentials = new NetworkCredential(ftpUsername, ftpPassword);
                    using (FtpWebResponse response = (FtpWebResponse)request.GetResponse())
                    {
                        // Klasör var, bir şey yapmaya gerek yok
                    }
                }
                catch (WebException)
                {
                    // Klasör yoksa oluştur
                    FtpWebRequest request = (FtpWebRequest)WebRequest.Create(archiveFolderPath);
                    request.Method = WebRequestMethods.Ftp.MakeDirectory;
                    request.Credentials = new NetworkCredential(ftpUsername, ftpPassword);
                    using (FtpWebResponse response = (FtpWebResponse)request.GetResponse())
                    {
                        logger.Info($"Archive directory created: {archiveFolderPath}");
                    }
                }

                int successCount = 0;
                List<string> failedFiles = new List<string>();

                // Her dosyayı arşive taşı
                foreach (string fileName in fileNames)
                {
                    try
                    {
                        // Kaynak ve hedef dosya yolları
                        string sourcePath = $"{ftpServer}/loglar/{fileName}";
                        string destPath = $"{ftpServer}/logArchive/{fileName}";

                        // Dosyayı indir
                        byte[] fileData;
                        FtpWebRequest downloadRequest = (FtpWebRequest)WebRequest.Create(sourcePath);
                        downloadRequest.Method = WebRequestMethods.Ftp.DownloadFile;
                        downloadRequest.Credentials = new NetworkCredential(ftpUsername, ftpPassword);

                        using (FtpWebResponse response = (FtpWebResponse)downloadRequest.GetResponse())
                        using (MemoryStream memoryStream = new MemoryStream())
                        {
                            response.GetResponseStream().CopyTo(memoryStream);
                            fileData = memoryStream.ToArray();
                        }

                        // Arşiv klasörüne yükle
                        FtpWebRequest uploadRequest = (FtpWebRequest)WebRequest.Create(destPath);
                        uploadRequest.Method = WebRequestMethods.Ftp.UploadFile;
                        uploadRequest.Credentials = new NetworkCredential(ftpUsername, ftpPassword);
                        uploadRequest.ContentLength = fileData.Length;

                        using (Stream requestStream = uploadRequest.GetRequestStream())
                        {
                            requestStream.Write(fileData, 0, fileData.Length);
                        }

                        using (FtpWebResponse response = (FtpWebResponse)uploadRequest.GetResponse())
                        {
                            logger.Info($"File uploaded to archive: {fileName}");
                        }

                        // Orijinal dosyayı sil
                        FtpWebRequest deleteRequest = (FtpWebRequest)WebRequest.Create(sourcePath);
                        deleteRequest.Method = WebRequestMethods.Ftp.DeleteFile;
                        deleteRequest.Credentials = new NetworkCredential(ftpUsername, ftpPassword);

                        using (FtpWebResponse response = (FtpWebResponse)deleteRequest.GetResponse())
                        {
                            logger.Info($"Original file deleted: {fileName}");
                        }

                        successCount++;
                    }
                    catch (Exception ex)
                    {
                        logger.Error($"Error archiving file {fileName}: {ex.Message}");
                        failedFiles.Add(fileName);
                    }
                }

                // Sonuç mesajını oluştur
                string resultMessage;
                bool success = true;

                if (successCount == fileNames.Count)
                {
                    resultMessage = $"{successCount} dosya başarıyla arşive taşındı.";
                }
                else if (successCount > 0)
                {
                    resultMessage = $"{successCount} dosya arşive taşındı, {failedFiles.Count} dosya taşınamadı.";
                    success = false;
                }
                else
                {
                    resultMessage = "Dosyalar arşive taşınırken hata oluştu.";
                    success = false;
                }

                // Sonuç bilgisini gönder
                SendArchiveResult(success, resultMessage);
            }
            catch (Exception ex)
            {
                logger.Error($"Archive operation failed: {ex.Message}");
                SendArchiveResult(false, $"Arşivleme işlemi başarısız: {ex.Message}");
            }
        }
        // Arşivleme işlemi sonucunu WebView'e gönderen metod
        private void SendArchiveResult(bool success, string message)
        {
            try
            {
                if (webView?.CoreWebView2 != null)
                {
                    var resultMessage = new
                    {
                        type = "archiveFilesResult",
                        success = success,
                        message = message
                    };

                    string jsonMessage = JsonSerializer.Serialize(resultMessage);
                    this.Invoke(new Action(() => webView.CoreWebView2.PostWebMessageAsJson(jsonMessage)));
                }
            }
            catch (Exception ex)
            {
                logger.Error($"Error sending archive result: {ex.Message}");
            }
        }
        private void getStudioQADAta(string text)
        {
            string sql = "Select Priority1,Priority2,Priority3,Priority4,Priority5,Priority6 from studioQA where `Tester`='" + text.ToString() + "'";
            MySqlDataAdapter verial = new MySqlDataAdapter(sql, coon);
            DataTable studioqa = new DataTable();
            verial.Fill(studioqa);

            // Verileri HTML'e gönder
            if (webView.CoreWebView2 != null && studioqa.Rows.Count > 0)
            {
                var row = studioqa.Rows[0];
                var studioQAData = new
                {
                    type = "updateStudioQA",
                    data = new[]
                    {
                        new { title = "Priority 1", value = row["Priority1"].ToString() },
                        new { title = "Priority 2", value = row["Priority2"].ToString() },
                        new { title = "Priority 3", value = row["Priority3"].ToString() },
                        new { title = "Priority 4", value = row["Priority4"].ToString() },
                        new { title = "Priority 5", value = row["Priority5"].ToString() },
                        new { title = "Priority 6", value = row["Priority6"].ToString() }
                    }
                };

                webView.CoreWebView2.PostWebMessageAsJson(JsonSerializer.Serialize(studioQAData));
            }
        }
        private bool adminse()
        {
            Console.WriteLine(Properties.Settings.Default.testername);
            return getRole(Properties.Settings.Default.testername) == "admin";
        }
        static async Task SendMessageToSlack(string webhookUrl, object[]? fields = null, string message = "a")
        {
            using (HttpClient client = new HttpClient())
            {
                var slackFields = fields?.Select(field =>
                {
                    // Discord `field` objesini Slack `field` formatına dönüştürüyoruz.
                    dynamic dynamicField = field;
                    return new
                    {
                        type = "mrkdwn",
                        text = $"*{dynamicField.name}:*\n{dynamicField.value}"
                    };
                }).ToArray();

                var payload = new
                {
                    blocks = new object[]
                    {
                new
                {
                    type = "section",
                    text = new
                    {
                        type = "mrkdwn",
                        text = message
                    }
                },
                new
                {
                    type = "divider"
                },
                new
                {
                    type = "section",
                    fields = slackFields
                }
                    }
                };

                var jsonPayload = System.Text.Json.JsonSerializer.Serialize(payload);
                var content = new StringContent(jsonPayload, Encoding.UTF8, "application/json");

                HttpResponseMessage response = await client.PostAsync(webhookUrl, content);
                if (response.IsSuccessStatusCode)
                {
                    Console.WriteLine("Message sent successfully.");
                }
                else
                {
                    Console.WriteLine($"Failed to send message. Status code: {response.StatusCode}");
                }
            }
        }
        private string getDeviceInfo()
        {
            string marka = currentDeviceInfo.Brand;
            string model = "\n" + currentDeviceInfo.Model;
            string surum = "\n" + currentDeviceInfo.Version;
            string chip = "\n" + currentDeviceInfo.Chipset;
            string cpu = "\n" + currentDeviceInfo.Cpu;
            string cozunurluk = "\n" + currentDeviceInfo.Resolution;
            string dil = "\n" + currentDeviceInfo.Language;
            return marka + model + surum + chip + cpu + cozunurluk + dil;
        }
        private string hashSql = "";
        private string GetLocalFileHash(string localPath)
        {
            using var md5 = MD5.Create();
            using var fileStream = File.OpenRead(localPath);
            byte[] hashData = md5.ComputeHash(fileStream);

            return BitConverter.ToString(hashData).Replace("-", "").ToLowerInvariant();
        }
        // File Hasher functionality for WebView
        private async Task SendFileHashesToWebView()
        {
            try
            {
                // Clear the haslist DataTable
                haslist.Clear();

                // Get file hashes from SQL
                string sql = "Select * from fileHashes";
                MySqlDataAdapter verial = new MySqlDataAdapter(sql, coon);
                verial.Fill(haslist);

                // Convert DataTable to list of objects for JSON serialization
                var fileHashes = new List<object>();
                foreach (DataRow row in haslist.Rows)
                {
                    fileHashes.Add(new
                    {
                        id = Convert.ToInt32(row["id"]),
                        filehash = row["filehash"].ToString(),
                        path = row["path"].ToString(),
                        filename = row["filename"].ToString(),
                        status = "file ok"
                    });
                }

                // Send data to WebView
                if (webView?.CoreWebView2 != null)
                {
                    webView.CoreWebView2.PostWebMessageAsJson(JsonSerializer.Serialize(new
                    {
                        type = "fileHashData",
                        data = fileHashes
                    }));

                    // Show notification
                    notificationSendToHtml("Dosya hash verileri başarıyla yüklendi.");
                }
            }
            catch (Exception ex)
            {
                logger.Error($"Error getting file hashes: {ex.Message}");
                notificationSendToHtml("Dosya hash verileri yüklenirken hata oluştu.", "error");
            }
        }
        // Oyun verilerini WebView'e gönderen fonksiyon
        private async Task SendAllGamesDataToWebView()
        {
            try
            {
                // Oyun verilerini al
                getAllGamesData();

                // Verileri JSON formatına dönüştür
                var games = new List<object>();
                foreach (DataRow row in allGamesDatas.Rows)
                {
                    games.Add(new
                    {
                        name = row["name"].ToString(),
                        bundleid = row["bundleid"].ToString(),
                        bundleidios = row["bundleidios"].ToString(),
                        iosappname = row["iosappname"].ToString(),
                        aosinternal = row["aosinternal"].ToString(),
                        tfurl = row["tfurl"].ToString(),
                        publisher = row["publisher"].ToString(),
                        status = row["status"].ToString()
                    });
                }

                // WebView2'ye JSON mesajı gönder
                if (webView?.CoreWebView2 != null)
                {
                    var message = new
                    {
                        type = "gamesData",
                        data = games
                    };

                    string jsonMessage = JsonSerializer.Serialize(message);
                    this.Invoke(new Action(() => webView.CoreWebView2.PostWebMessageAsJson(jsonMessage)));

                    // Bildirim göster
                    notificationSendToHtml("Oyun verileri başarıyla yüklendi.");
                }
            }
            catch (Exception ex)
            {
                logger.Error($"Games data sending error: {ex.Message}");
                notificationSendToHtml("Oyun verileri yüklenirken hata oluştu.", "error");
            }
        }
        // Oyunu mağazada gösteren fonksiyon
        private void ViewGameInStore(string gameName, string os)
        {
            try
            {
                if (string.IsNullOrEmpty(gameName) || string.IsNullOrEmpty(os))
                {
                    logger.Error("Game name or OS is empty");
                    return;
                }

                string bundleId = "";

                // Oyun adına göre bundle ID'yi bul
                if (os == "AOS")
                {
                    bundleId = getGameDataFromDataTable(gameName, "bundleid");
                    if (!string.IsNullOrEmpty(bundleId))
                    {
                        // Android için Play Store'da aç
                        Process.Start("cmd.exe", $"/c adb shell am start -a android.intent.action.VIEW -d 'market://details?id={bundleId}'");
                        //notificationSendToHtml($"{gameName} Play Store'da açıldı.");
                    }
                    else
                    {
                        notificationSendToHtml($"{gameName} için bundle ID bulunamadı.", "error");
                    }
                }
                else if (os == "IOS")
                {
                    // iOS için App Store URL'ini bul
                    string tfurl = getGameDataFromDataTable(gameName, "tfurl");
                    if (!string.IsNullOrEmpty(tfurl))
                    {
                        // URL'i tarayıcıda aç
                        Process.Start(new ProcessStartInfo
                        {
                            FileName = tfurl,
                            UseShellExecute = true
                        });
                        //notificationSendToHtml($"{gameName} App Store'da açıldı.");
                    }
                    else
                    {
                        notificationSendToHtml($"{gameName} için App Store URL'i bulunamadı.", "error");
                    }
                }
            }
            catch (Exception ex)
            {
                logger.Error($"View in store error: {ex.Message}");
                notificationSendToHtml("Oyun mağazada gösterilirken hata oluştu.", "error");
            }
        }
        // Yeni oyun ekleme fonksiyonu
        private void AddGame(JsonElement gameData)
        {
            try
            {
                // Gerekli alanları kontrol et
                if (!gameData.TryGetProperty("name", out JsonElement nameElement) ||
                    !gameData.TryGetProperty("bundleid", out JsonElement bundleidElement) ||
                    !gameData.TryGetProperty("bundleidios", out JsonElement bundleidiosElement) ||
                    !gameData.TryGetProperty("iosappname", out JsonElement iosappnameElement))
                {
                    notificationSendToHtml("Gerekli alanlar eksik.", "error");
                    return;
                }

                // Diğer alanları al
                gameData.TryGetProperty("aosinternal", out JsonElement aosinternalElement);
                gameData.TryGetProperty("tfurl", out JsonElement tfurlElement);
                gameData.TryGetProperty("publisher", out JsonElement publisherElement);
                gameData.TryGetProperty("status", out JsonElement statusElement);

                // SQL sorgusu oluştur
                StringBuilder sb = new StringBuilder();
                sb.Append("INSERT INTO allGamesData (`name`,`bundleid`,`bundleidios`,`iosappname`,`aosinternal`,`tfurl`,`publisher`,`status`) VALUES (");
                sb.AppendFormat("'{0}', '{1}', '{2}', '{3}', '{4}', '{5}', '{6}', '{7}');",
                    MySqlHelper.EscapeString(nameElement.GetString()),
                    MySqlHelper.EscapeString(bundleidElement.GetString()),
                    MySqlHelper.EscapeString(bundleidiosElement.GetString()),
                    MySqlHelper.EscapeString(iosappnameElement.GetString()),
                    MySqlHelper.EscapeString(aosinternalElement.ValueKind != JsonValueKind.Undefined ? aosinternalElement.GetString() : ""),
                    MySqlHelper.EscapeString(tfurlElement.ValueKind != JsonValueKind.Undefined ? tfurlElement.GetString() : ""),
                    MySqlHelper.EscapeString(publisherElement.ValueKind != JsonValueKind.Undefined ? publisherElement.GetString() : ""),
                    MySqlHelper.EscapeString(statusElement.ValueKind != JsonValueKind.Undefined ? statusElement.GetString() : "Aktif")
                );

                // SQL sorgusunu çalıştır
                runSql(sb.ToString());

                // Verileri yenile ve bildirim göster
                SendAllGamesDataToWebView();
                notificationSendToHtml($"{nameElement.GetString()} oyunu başarıyla eklendi.", "success");
            }
            catch (Exception ex)
            {
                logger.Error($"Add game error: {ex.Message}");
                notificationSendToHtml("Oyun eklenirken hata oluştu.", "error");
            }
        }
        // Oyun güncelleme fonksiyonu
        private void UpdateGame(JsonElement gameData)
        {
            try
            {
                // Gerekli alanları kontrol et
                if (!gameData.TryGetProperty("name", out JsonElement nameElement) ||
                    !gameData.TryGetProperty("bundleid", out JsonElement bundleidElement) ||
                    !gameData.TryGetProperty("bundleidios", out JsonElement bundleidiosElement) ||
                    !gameData.TryGetProperty("iosappname", out JsonElement iosappnameElement))
                {
                    notificationSendToHtml("Gerekli alanlar eksik.", "error");
                    return;
                }

                // Diğer alanları al
                gameData.TryGetProperty("aosinternal", out JsonElement aosinternalElement);
                gameData.TryGetProperty("tfurl", out JsonElement tfurlElement);
                gameData.TryGetProperty("publisher", out JsonElement publisherElement);
                gameData.TryGetProperty("status", out JsonElement statusElement);

                // SQL sorgusu oluştur
                StringBuilder sb = new StringBuilder();
                sb.Append("UPDATE allGamesData SET ");
                sb.AppendFormat("`bundleid` = '{0}', `bundleidios` = '{1}', `iosappname` = '{2}', `aosinternal` = '{3}', `tfurl` = '{4}', `publisher` = '{5}', `status` = '{6}' WHERE `name` = '{7}';",
                    MySqlHelper.EscapeString(bundleidElement.GetString()),
                    MySqlHelper.EscapeString(bundleidiosElement.GetString()),
                    MySqlHelper.EscapeString(iosappnameElement.GetString()),
                    MySqlHelper.EscapeString(aosinternalElement.ValueKind != JsonValueKind.Undefined ? aosinternalElement.GetString() : ""),
                    MySqlHelper.EscapeString(tfurlElement.ValueKind != JsonValueKind.Undefined ? tfurlElement.GetString() : ""),
                    MySqlHelper.EscapeString(publisherElement.ValueKind != JsonValueKind.Undefined ? publisherElement.GetString() : ""),
                    MySqlHelper.EscapeString(statusElement.ValueKind != JsonValueKind.Undefined ? statusElement.GetString() : "Aktif"),
                    MySqlHelper.EscapeString(nameElement.GetString())
                );

                // SQL sorgusunu çalıştır
                runSql(sb.ToString());

                // Verileri yenile ve bildirim göster
                SendAllGamesDataToWebView();
                notificationSendToHtml($"{nameElement.GetString()} oyunu başarıyla güncellendi.", "success");
            }
            catch (Exception ex)
            {
                logger.Error($"Update game error: {ex.Message}");
                notificationSendToHtml("Oyun güncellenirken hata oluştu.", "error");
            }
        }
        // Oyun silme fonksiyonu
        private void DeleteGame(string gameName)
        {
            try
            {
                if (string.IsNullOrEmpty(gameName))
                {
                    notificationSendToHtml("Oyun adı boş olamaz.", "error");
                    return;
                }

                // SQL sorgusu oluştur
                string sql = $"DELETE FROM allGamesData WHERE `name` = '{MySqlHelper.EscapeString(gameName)}';";

                // SQL sorgusunu çalıştır
                runSql(sql);

                // Verileri yenile ve bildirim göster
                SendAllGamesDataToWebView();
                notificationSendToHtml($"{gameName} oyunu başarıyla silindi.", "success");
            }
            catch (Exception ex)
            {
                logger.Error($"Delete game error: {ex.Message}");
                notificationSendToHtml("Oyun silinirken hata oluştu.", "error");
            }
        }
        private async Task CheckFilesForChanges()
        {
            try
            {
                // Clear and prepare the DataTable to store results
                hasTable.Clear();
                hasTable.Columns.Clear();
                hasTable.Columns.Add("id");
                hasTable.Columns.Add("filehash");
                hasTable.Columns.Add("path");
                hasTable.Columns.Add("filename");
                hasTable.Columns.Add("status");

                string path = Application.StartupPath;
                StringBuilder sb = new StringBuilder();

                // List to store results for WebView
                var fileHashes = new List<object>();

                // Process directory function
                void ProcessDirectory(string dirPath, string relativePath)
                {
                    // Klasör yolu blacklist'te mi kontrol et
                    foreach (var blacklistedItem in blacklist)
                    {
                        if (dirPath.Contains(blacklistedItem) || relativePath.Contains(blacklistedItem))
                        {
                            return; // Blacklist'teki bir klasörse işleme devam etme
                        }
                    }
                    // Skip applog directory
                    if (dirPath.Contains("applog"))
                    {
                        return;
                    }

                    // Process files in directory
                    foreach (var file in Directory.GetFiles(dirPath))
                    {
                        string fileName = Path.GetFileName(file);
                        string fileHash = GetLocalFileHash(file);
                        string status = "file ok";

                        // Dosya adı blacklist'te mi kontrol et
                        bool isBlacklisted = blacklist.Any(item => fileName.Contains(item));

                        // Spesifik klasör/dosya yolu kontrolü
                        string fullRelativePath = Path.Combine(relativePath, fileName).Replace("\\", "/");
                        bool isSpecificPathBlacklisted = specificPathBlacklist.Any(path => fullRelativePath.EndsWith(path));

                        if (isBlacklisted || isSpecificPathBlacklisted)
                        {
                            continue; // Blacklist'teki bir dosyaysa bu dosyayı atla
                        }

                        // Check if file exists in database
                        DataRow[] foundRows = haslist.Select($"filename = '{fileName}' and path = '{relativePath}'");
                        if (foundRows.Length > 0)
                        {
                            string localhash = foundRows[0]["filehash"].ToString();
                            int id = Convert.ToInt32(foundRows[0]["id"]);

                            // Check if hash has changed
                            if (localhash != fileHash)
                            {
                                sb.Append($"UPDATE fileHashes SET `filehash` = \"{MySqlHelper.EscapeString(fileHash)}\" WHERE `id` = '{id}';\n");
                                status = "updated";
                            }

                            // Add to results
                            fileHashes.Add(new
                            {
                                id = id,
                                filehash = fileHash,
                                path = relativePath,
                                filename = fileName,
                                status = status
                            });

                            // hasTable'a da ekle
                            DataRow newRow = hasTable.NewRow();
                            newRow["id"] = id;
                            newRow["filehash"] = fileHash;
                            newRow["path"] = relativePath;
                            newRow["filename"] = fileName;
                            newRow["status"] = status;
                            hasTable.Rows.Add(newRow);
                        }
                        else
                        {
                            // New file
                            sb.Append($"INSERT INTO fileHashes (`filehash`, `path`, `filename`) VALUES (\"{MySqlHelper.EscapeString(fileHash)}\", \"{MySqlHelper.EscapeString(relativePath)}\", \"{MySqlHelper.EscapeString(fileName)}\");\n");

                            // Add to results with temporary ID
                            fileHashes.Add(new
                            {
                                id = 0, // Temporary ID
                                filehash = fileHash,
                                path = relativePath,
                                filename = fileName,
                                status = "new"
                            });

                            // hasTable'a da ekle
                            DataRow newRow = hasTable.NewRow();
                            newRow["id"] = 0; // Temporary ID
                            newRow["filehash"] = fileHash;
                            newRow["path"] = relativePath;
                            newRow["filename"] = fileName;
                            newRow["status"] = "new";
                            hasTable.Rows.Add(newRow);
                        }
                    }

                    // Process subdirectories
                    foreach (var subDir in Directory.GetDirectories(dirPath))
                    {
                        string subDirName = Path.GetFileName(subDir);
                        // Alt klasör adı blacklist'te mi kontrol et
                        bool isBlacklisted = blacklist.Any(item => subDirName.Contains(item));
                        if (isBlacklisted)
                        {
                            continue; // Blacklist'teki bir klasörse bu klasörü atla
                        }
                        string newRelativePath = string.IsNullOrEmpty(relativePath) ? subDirName : Path.Combine(relativePath, subDirName);
                        ProcessDirectory(subDir, newRelativePath);
                    }
                }

                // Start processing from root directory
                await Task.Run(() => ProcessDirectory(path, ""));

                // Store SQL commands for later use
                hashSql = sb.ToString();

                // Send data to WebView
                if (webView?.CoreWebView2 != null)
                {
                    webView.CoreWebView2.PostWebMessageAsJson(JsonSerializer.Serialize(new
                    {
                        type = "fileHashData",
                        data = fileHashes
                    }));

                    // Show notification
                    notificationSendToHtml("Dosya kontrolü tamamlandı.");
                }
            }
            catch (Exception ex)
            {
                logger.Error($"Error checking files: {ex.Message}");
                notificationSendToHtml("Dosya kontrolü sırasında hata oluştu.", "error");
            }
        }
        private void SaveHashChanges()
        {
            try
            {
                if (string.IsNullOrEmpty(hashSql))
                {
                    notificationSendToHtml("Kaydedilecek değişiklik bulunamadı.", "warning");
                    return;
                }

                // "updated" veya "new file" statüsündeki dosyaları FTP'ye yükleme
                List<DataRow> filesToUpload = new List<DataRow>();
                foreach (DataRow row in hasTable.Rows)
                {
                    string status = row["status"].ToString();
                    if (status == "updated" || status == "new")
                    {
                        filesToUpload.Add(row);
                    }
                }

                if (filesToUpload.Count > 0 && areYouSure(filesToUpload.Count + " adet dosya FTP'ye yüklenecek. Onaylıyor musunuz?"))
                {
                    // Paralel yükleme için maksimum eşzamanlı işlem sayısı
                    int maxConcurrentUploads = 5; // Aynı anda en fazla 3 dosya yükle
                    SemaphoreSlim semaphore = new SemaphoreSlim(maxConcurrentUploads);

                    int uploadedCount = 0;
                    int totalFiles = filesToUpload.Count;
                    int processedFiles = 0;

                    // Tüm yükleme görevlerini takip etmek için liste
                    List<Task> uploadTasks = new List<Task>();

                    foreach (DataRow row in filesToUpload)
                    {
                        string filePath = row["path"].ToString();
                        string fileName = row["filename"].ToString();
                        string localFilePath = Path.Combine(Application.StartupPath, filePath, fileName);

                        if (File.Exists(localFilePath))
                        {
                            // Blacklist kontrolü
                            bool isBlacklisted = false;
                            foreach (string blacklistItem in blacklist)
                            {
                                if (filePath.Contains(blacklistItem) || fileName.Contains(blacklistItem) ||
                                    (filePath + "/" + fileName).Contains(blacklistItem))
                                {
                                    isBlacklisted = true;
                                    logger.Debug($"Skipping blacklisted file: {filePath}/{fileName}");
                                    break;
                                }
                            }

                            // Spesifik klasör/dosya yolu kontrolü
                            string fullRelativePath = Path.Combine(filePath, fileName).Replace("\\", "/");
                            bool isSpecificPathBlacklisted = specificPathBlacklist.Any(path => fullRelativePath.EndsWith(path));

                            string remoteFilePath = "apptest/" + (string.IsNullOrEmpty(filePath) ? fileName : filePath.Replace("\\", "/") + "/" + fileName);

                            // Yeni bir Task oluştur ve listeye ekle
                            Task uploadTask = Task.Run(async () =>
                            {
                                try
                                {
                                    // Semaphore ile eşzamanlı yükleme sayısını sınırla
                                    await semaphore.WaitAsync();

                                    try
                                    {
                                        UploadFileToFTP(localFilePath, remoteFilePath, ftpServer, ftpUsername, ftpPassword);

                                        // Thread-safe bir şekilde sayıyı artır
                                        Interlocked.Increment(ref uploadedCount);

                                        // Başarılı yükleme durumunda kullanıcıya bilgi ver
                                        _ = setToolStatusLabel(fileName + " dosyası FTP'ye yüklendi", "green", true);
                                    }
                                    catch (Exception ex)
                                    {
                                        logger.Error(ex + " FTP yükleme hatası: " + localFilePath);
                                        MessageBox.Show("Dosya yüklenirken hata oluştu: " + fileName + "\n\nHata: " + ex.Message, "Hata", MessageBoxButtons.OK, MessageBoxIcon.Error);
                                    }
                                    finally
                                    {
                                        // İşlem bittiğinde semaphore'u serbest bırak
                                        semaphore.Release();
                                    }
                                }
                                catch (Exception ex)
                                {
                                    logger.Error(ex + " Task hatası: " + localFilePath);
                                }
                            });

                            uploadTasks.Add(uploadTask);
                        }
                        else
                        {
                            // Dosya bulunamadıysa ilerleme çubuğunu güncelle
                            processedFiles++;
                        }
                    }

                    // Tüm yükleme görevlerinin tamamlanmasını bekle
                    Task.Run(async () =>
                    {
                        await Task.WhenAll(uploadTasks);

                        // Tüm işlemler tamamlandığında UI'yı güncelle
                        this.Invoke((MethodInvoker)delegate {
                            MessageBox.Show(uploadedCount + " adet dosya başarıyla FTP'ye yüklendi.", "Bilgi", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        });
                    });
                }

                // Execute SQL commands
                runSql(hashSql);

                // Clear SQL commands
                hashSql = "";

                // Show notification
                notificationSendToHtml("Değişiklikler başarıyla kaydedildi.");

                // Refresh data
                SendFileHashesToWebView();
            }
            catch (Exception ex)
            {
                logger.Error($"Error saving hash changes: {ex.Message}");
                notificationSendToHtml("Değişiklikler kaydedilirken hata oluştu.", "error");
            }
        }
        private void ResetHashTable()
        {
            try
            {
                // Truncate the table
                if (coon.State != ConnectionState.Open)
                {
                    coon.Open();
                }

                using (MySqlCommand cmd = new MySqlCommand("TRUNCATE TABLE `fileHashes`;\n", coon))
                {
                    cmd.ExecuteNonQuery();
                }

                coon.Close();

                // Show notification
                notificationSendToHtml("Hash tablosu başarıyla sıfırlandı.");

                // Refresh data
                SendFileHashesToWebView();
            }
            catch (Exception ex)
            {
                logger.Error($"Error resetting hash table: {ex.Message}");
                notificationSendToHtml("Hash tablosu sıfırlanırken hata oluştu.", "error");
            }
        }
        /// <summary>
        /// JSON verilerinden hasTable DataTable'ını günceller
        /// </summary>
        private void UpdateHashTableFromJson(JsonElement hashDataElement)
        {
            try
            {
                // Mevcut tabloyu temizle
                hasTable.Clear();

                // JSON dizisini döngüye al
                foreach (JsonElement item in hashDataElement.EnumerateArray())
                {
                    DataRow newRow = hasTable.NewRow();

                    // JSON özelliklerini DataRow'a aktar
                    if (item.TryGetProperty("id", out JsonElement idElement))
                    {
                        newRow["id"] = idElement.GetInt32();
                    }

                    if (item.TryGetProperty("filehash", out JsonElement hashElement))
                    {
                        newRow["filehash"] = hashElement.GetString();
                    }

                    if (item.TryGetProperty("path", out JsonElement pathElement))
                    {
                        newRow["path"] = pathElement.GetString();
                    }

                    if (item.TryGetProperty("filename", out JsonElement filenameElement))
                    {
                        newRow["filename"] = filenameElement.GetString();
                    }

                    if (item.TryGetProperty("status", out JsonElement statusElement))
                    {
                        newRow["status"] = statusElement.GetString();
                    }

                    // DataTable'a ekle
                    hasTable.Rows.Add(newRow);
                }

                logger.Debug($"Hash table updated from JSON with {hasTable.Rows.Count} rows");
            }
            catch (Exception ex)
            {
                logger.Error($"Error updating hash table from JSON: {ex.Message}");
                notificationSendToHtml("Hash tablosu güncellenirken hata oluştu.", "info");
            }
        }
        private async Task mediaNovaPurger()
        {
            var apiClient = new MedianovaApiClient(medianovaclient, medianovasecret);

            // Authentication işlemini gerçekleştir
            bool isAuthenticated = await apiClient.AuthenticateAsync();
            if (isAuthenticated)
            {
                Console.WriteLine("Authentication başarılı.");

                // Önbellek temizleme işlemi için URL belirtin
                bool isPurged = await apiClient.PurgeCacheAsync("751475a9-9721-44f0-ba23-767f02d8808a", "d30985d4-a7a2-4a92-bbd3-176ed412cde9");
                if (isPurged)
                {

                    Console.WriteLine("Purge işlemi başarılı.");
                }
                else
                {
                    Console.WriteLine("Purge işlemi başarısız.");
                }
            }
            else
            {
                Console.WriteLine("Authentication başarısız.");
            }
        }
        private void refreshAppInfo()
        {
            if (!string.IsNullOrEmpty(selectedGame) && selectedGame != "!Seçilmedi")
            {
                currentBundleId = getGameDataFromDataTable(selectedGame, "bundleid");
                if (!string.IsNullOrEmpty(currentBundleId))
                {
                    var bundleInfo = getsBundleInfo(currentBundleId);
                    this.BeginInvoke((Action)(() =>
                    {
                        webView.CoreWebView2.PostWebMessageAsJson(JsonSerializer.Serialize(new
                        {
                            type = "bundleInfo",
                            bundleInfo = new
                            {
                                version = bundleInfo.version,
                                buildNumber = bundleInfo.versionCode,
                                bundleId = currentBundleId,
                                targetApi = bundleInfo.targetSdk
                            }
                        }));
                    }));
                }
            }
        }
        private async Task loginAsync(string id, string pw, bool remember)
        {
            var logincheck = await redmine.connectionAsync(id, pw);
            if (logincheck == false)
            {
                // Login başarısız olduğunda HTML'e bildir
                if (webView.CoreWebView2 != null)
                {
                    // UI thread'de çalıştır
                    await this.Invoke(async () => {
                        webView.CoreWebView2.PostWebMessageAsJson(JsonSerializer.Serialize(new {
                            type = "loginResult",
                            success = false,
                            message = "Kullanıcı adı veya şifre hatalı!"
                        }));
                    });
                }
            }
            else
            {
                // Login başarılı olduğunda HTML'e bildir
                if (webView.CoreWebView2 != null)
                {
                    // UI thread'de çalıştır
                    await this.Invoke(async () => {
                        webView.CoreWebView2.PostWebMessageAsJson(JsonSerializer.Serialize(new {
                            type = "loginResult",
                            success = true,
                            username = id,
                            userId = redmine.getCurrentUserId(),
                            adminmi = adminse()
                        }));
                    });
                }
                Properties.Settings.Default.testername = id;
                if (remember)
                {
                    Properties.Settings.Default.redmineid = id;
                    Properties.Settings.Default.redminepw = pw;
                    Properties.Settings.Default.rememberMe = true;
                }
                Properties.Settings.Default.Save();
                await Task.Delay(1000);
                getAllGameListFromFtp();
                timer1.Start();
                if (!string.IsNullOrEmpty(Properties.Settings.Default.testername))
                {
                    string role = getRole(Properties.Settings.Default.testername);
                    if (role == "admin")
                    {
                        fromloaded = false;
                        //this.Size = adminSize;
                        this.MinimumSize = adminSize;
                        GetAllTesters();
                    }
                    else
                    {
                        //this.Size = userSize;
                        this.MinimumSize = userSize;
                    }
                }
                getStudioQADAta(id);
            }
        }
        private async void InitializeWebView2()
        {
            try
            {
                webView = new WebView2
                {
                    Dock = DockStyle.Fill
                };
                panel16.Controls.Add(webView);

                // Web güvenlik ayarlarını yapılandır
                var envOptions = new CoreWebView2EnvironmentOptions("--disable-web-security");
                var env = await CoreWebView2Environment.CreateAsync(null, "WebView2Cache", envOptions);

                await webView.EnsureCoreWebView2Async(env);

                // Varsayılan sağ tık menüsünü devre dışı bırak
                webView.CoreWebView2.Settings.AreDefaultContextMenusEnabled = false;

                // Varsayılan hata sayfalarını devre dışı bırak
                webView.CoreWebView2.Settings.AreDefaultScriptDialogsEnabled = false;

                // DevTools'u devre dışı bırak (F12 tuşu)
                webView.CoreWebView2.Settings.AreDevToolsEnabled = true;

                // F5 tuşunu ve diğer yenileme kısayollarını devre dışı bırak
                webView.CoreWebView2.AddHostObjectToScript("preventRefresh", new PreventRefreshHelper());
                webView.CoreWebView2.AddScriptToExecuteOnDocumentCreatedAsync(
                    "window.addEventListener('keydown', function(e) {" +
                    "  if (e.key === 'F5' || (e.ctrlKey && e.key === 'r')) {" +
                    "    e.preventDefault();" +
                    "    return false;" +
                    "  }" +
                    "});");

                // HTML dosyasını yükle
                LoadLocalHtml();

                // WebView2 mesaj dinleyicisini ekle
                webView.WebMessageReceived += WebView_WebMessageReceived;
            }
            catch (Exception ex)
            {
                logger.Error($"WebView2 initialization failed: {ex.Message}");
                MessageBox.Show("WebView2 başlatılamadı!", "Hata", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        private async void LoadLocalHtml()
        {
            try
            {
                string htmlFilePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Resources", "index.html");
                if (File.Exists(htmlFilePath))
                {
                    await webView.EnsureCoreWebView2Async();
                    webView.Source = new Uri($"file:///{htmlFilePath.Replace("\\", "/")}");

                    // Sayfa yüklendiğinde başlangıç ayarlarını gönder
                    webView.NavigationCompleted += async (s, e) =>
                    {
                        webView.CoreWebView2.PostWebMessageAsJson(JsonSerializer.Serialize(new
                        {
                            type = "initSettings",
                            data = new
                            {
                                alwaysTop = Properties.Settings.Default.alwaysTop,
                                autoBundle = Properties.Settings.Default.autoGetBundle,
                                continuousRecording = videoRecorLoop,
                                version = Application.ProductVersion.Split('+')[0],
                                status = "Ready"
                            }
                        }));
                        
                        // Kablosuz bağlantı düğmesinin başlangıç durumunu gönder
                        webView.CoreWebView2.PostWebMessageAsJson(JsonSerializer.Serialize(new
                        {
                            type = "updateWirelessButtonState",
                            isWireless = isWirelessConnection
                        }));
                    };
                }
                else
                {
                    throw new FileNotFoundException("HTML dosyası bulunamadı", htmlFilePath);
                }
            }
            catch (Exception ex)
            {
                logger.Error($"HTML loading failed: {ex.Message}");
                MessageBox.Show("Arayüz yüklenemedi!", "Hata", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        // Status güncelleme metodu ekleyelim
        private void UpdateStatus(string status, string color = "green")
        {
            // Use Invoke to ensure WebView2 operations run on the UI thread
            this.Invoke(new Action(() => {
                if (webView?.CoreWebView2 != null)
                {
                    webView.CoreWebView2.PostWebMessageAsJson(JsonSerializer.Serialize(new
                    {
                        type = "updateStatus",
                        status = status,
                        color = color
                    }));
                }
            }));
        }
        // Form1.cs'de isDeviceConnected değişkeni değiştiğinde HTML'e bildirelim
        private async void UpdateDeviceStatus()
        {
            try
            {
                // Use Invoke to ensure WebView2 operations run on the UI thread
                await this.Invoke(async () => {
                    if (webView?.CoreWebView2 != null)
                    {
                        logger.Debug($"Updating device status: {isDeviceConnected}, Wireless: {isWirelessConnection}");
                        webView.CoreWebView2.PostWebMessageAsJson(JsonSerializer.Serialize(new
                        {
                            type = "updateDeviceStatus",
                            connected = isDeviceConnected,
                            wireless = isWirelessConnection
                        }));
                        
                        // Kablosuz bağlantı düğmesinin durumunu da güncelle
                        webView.CoreWebView2.PostWebMessageAsJson(JsonSerializer.Serialize(new
                        {
                            type = "updateWirelessButtonState",
                            isWireless = isWirelessConnection
                        }));
                    }
                });
            }
            catch (Exception ex)
            {
                logger.Error("UpdateDeviceStatus error: " + ex.Message);
            }
        }
        private void UpdateDeviceInfo()
        {
            try
            {
                // Use Invoke to ensure WebView2 operations run on the UI thread
                this.Invoke(new Action(() => {
                    if (webView?.CoreWebView2 != null)
                    {
                        currentDeviceInfo = new DeviceInfo
                        {
                            Brand = deviceMarka,
                            Model = deviceModel,
                            Version = asosurum,
                            Chipset = chipset,
                            Cpu = cpu,
                            Resolution = cozunurluk,
                            Language = deviceLang
                        };

                        webView.CoreWebView2.PostWebMessageAsJson(JsonSerializer.Serialize(new
                        {
                            type = "updateDeviceInfo",
                            data = currentDeviceInfo
                        }));
                    }
                }));
            }
            catch (Exception ex)
            {
                logger.Error($"Device info update error: {ex.Message}");
            }
        }
        // Yerel log dosyalarını al
        private async Task GetAllLocalLogFiles()
        {
            try
            {
                // Log dosyalarının bulunduğu ana klasör
                string logDirectory = Path.Combine(Application.StartupPath, "logs");

                // Klasör yoksa oluştur
                if (!Directory.Exists(logDirectory))
                {
                    Directory.CreateDirectory(logDirectory);
                }

                List<object> logFiles = new List<object>();

                // Tüm alt klasörleri ve dosyaları tara
                foreach (var dir in Directory.GetDirectories(logDirectory))
                {
                    // Tüm .txt dosyalarını al
                    foreach (var file in Directory.GetFiles(dir, "*.txt"))
                    {
                        var fileInfo = new FileInfo(file);
                        logFiles.Add(new
                        {
                            FileName = fileInfo.Name,
                            FullPath = file,  // Tam dosya yolu
                            RelativePath = file.Replace(Application.StartupPath + "\\", ""),  // Göreceli yol
                            Date = fileInfo.LastWriteTime.ToString("yyyy-MM-dd HH:mm:ss"),
                            Size = fileInfo.Length
                        });
                    }
                }

                // Ana klasördeki dosyaları da ekle
                foreach (var file in Directory.GetFiles(logDirectory, "*.txt"))
                {
                    var fileInfo = new FileInfo(file);
                    logFiles.Add(new
                    {
                        FileName = fileInfo.Name,
                        FullPath = file,  // Tam dosya yolu
                        RelativePath = file.Replace(Application.StartupPath + "\\", ""),  // Göreceli yol
                        Date = fileInfo.LastWriteTime.ToString("yyyy-MM-dd HH:mm:ss"),
                        Size = fileInfo.Length
                    });
                }

                // WebView2'ye JSON mesajı gönder
                await this.Invoke(async () => {
                    if (webView?.CoreWebView2 != null)
                    {
                        webView.CoreWebView2.PostWebMessageAsJson(JsonSerializer.Serialize(new
                        {
                            type = "localLogFiles",
                            files = logFiles
                        }));
                    }
                });
            }
            catch (Exception ex)
            {
                logger.Error($"Get local log files error: {ex.Message}");
                MessageBox.Show($"Log dosyaları alınırken hata oluştu: {ex.Message}", "Hata", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        // Yerel log dosyasını aç
        private void OpenLocalLogFile(string filename)
        {
            try
            {
                // Önce tam yol olarak dene
                string fullPath = Path.Combine(Application.StartupPath, filename);

                // Eğer dosya yoksa, sadece dosya adını kullanarak logs klasöründe ara
                if (!File.Exists(fullPath))
                {
                    // Tüm logs klasöründe ve alt klasörlerinde ara
                    string logDirectory = Path.Combine(Application.StartupPath, "logs");
                    string[] foundFiles = Directory.GetFiles(logDirectory, filename, SearchOption.AllDirectories);

                    if (foundFiles.Length > 0)
                    {
                        fullPath = foundFiles[0]; // İlk bulunan dosyayı kullan
                    }
                    else
                    {
                        // Son çare olarak logs klasöründe direkt ara
                        fullPath = Path.Combine(Application.StartupPath, "logs", filename);
                    }
                }

                if (File.Exists(fullPath))
                {
                    Process.Start("notepad.exe", fullPath);
                }
                else
                {
                    MessageBox.Show($"\"{filename}\" dosyası bulunamadı.", "Hata", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                logger.Error($"Open local log file error: {ex.Message}");
                MessageBox.Show($"Log dosyası açılırken hata oluştu: {ex.Message}", "Hata", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        // Yerel log dosyasını sil
        private void DeleteLocalLogFile(string filename)
        {
            try
            {
                // Önce tam yol olarak dene
                string fullPath = Path.Combine(Application.StartupPath, filename);

                // Eğer dosya yoksa, sadece dosya adını kullanarak logs klasöründe ara
                if (!File.Exists(fullPath))
                {
                    // Tüm logs klasöründe ve alt klasörlerinde ara
                    string logDirectory = Path.Combine(Application.StartupPath, "logs");
                    string[] foundFiles = Directory.GetFiles(logDirectory, filename, SearchOption.AllDirectories);

                    if (foundFiles.Length > 0)
                    {
                        fullPath = foundFiles[0]; // İlk bulunan dosyayı kullan
                    }
                    else
                    {
                        // Son çare olarak logs klasöründe direkt ara
                        fullPath = Path.Combine(Application.StartupPath, "logs", filename);
                    }
                }

                if (File.Exists(fullPath))
                {
                    File.Delete(fullPath);

                    // WebView2'ye JSON mesajı gönder
                    this.Invoke(new Action(() => {
                        if (webView?.CoreWebView2 != null)
                        {
                            webView.CoreWebView2.PostWebMessageAsJson(JsonSerializer.Serialize(new
                            {
                                type = "localLogFileDeleted",
                                filename = filename
                            }));
                        }
                    }));
                }
                else
                {
                    MessageBox.Show($"\"{filename}\" dosyası bulunamadı.", "Hata", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                logger.Error($"Delete local log file error: {ex.Message}");
                MessageBox.Show($"Log dosyası silinirken hata oluştu: {ex.Message}", "Hata", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        // Yerel log dosyasını FTP'ye yükle
        private async Task UploadLogFileToFtp(string filename)
        {
            try
            {
                // Önce tam yol olarak dene
                string fullPath = Path.Combine(Application.StartupPath, filename);

                // Eğer dosya yoksa, sadece dosya adını kullanarak logs klasöründe ara
                if (!File.Exists(fullPath))
                {
                    // Tüm logs klasöründe ve alt klasörlerinde ara
                    string logDirectory = Path.Combine(Application.StartupPath, "logs");
                    string[] foundFiles = Directory.GetFiles(logDirectory, filename, SearchOption.AllDirectories);

                    if (foundFiles.Length > 0)
                    {
                        fullPath = foundFiles[0]; // İlk bulunan dosyayı kullan
                    }
                    else
                    {
                        // Son çare olarak logs klasöründe direkt ara
                        fullPath = Path.Combine(Application.StartupPath, "logs", filename);
                    }
                }

                if (File.Exists(fullPath))
                {
                    string ftpServerIP = ftpServer;
                    string ftpUserName = ftpUsername;
                    string ftpPass = ftpPassword;

                    // Dosyayı FTP'ye yükle
                    await Task.Run(() => UploadFileToFTP(fullPath, "loglar/" + filename, ftpServerIP, ftpUserName, ftpPass));

                    // Bildirim göster
                    this.Invoke(new Action(() => {
                        if (webView?.CoreWebView2 != null)
                        {
                            webView.CoreWebView2.PostWebMessageAsJson(JsonSerializer.Serialize(new
                            {
                                type = "notification",
                                message = $"\"{filename}\" dosyası FTP'ye yüklendi."
                            }));
                        }
                    }));
                }
                else
                {
                    MessageBox.Show($"\"{filename}\" dosyası bulunamadı.", "Hata", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                logger.Error($"Upload log file to FTP error: {ex.Message}");
                MessageBox.Show($"Log dosyası FTP'ye yüklenirken hata oluştu: {ex.Message}", "Hata", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        // Birden fazla log dosyasını FTP'ye yükle
        private async Task UploadMultipleLogsToFtp(JsonElement filenames)
        {
            try
            {
                int totalFiles = filenames.GetArrayLength();
                int uploadedFiles = 0;
                int failedFiles = 0;

                // Durum bilgisini güncelle
                _ = setToolStatusLabel($"FTP'ye yükleniyor: 0/{totalFiles}", manuelupdate: true, renk: "yellow");

                foreach (var filenameElement in filenames.EnumerateArray())
                {
                    string filename = filenameElement.GetString();

                    try
                    {
                        // Önce tam yol olarak dene
                        string fullPath = Path.Combine(Application.StartupPath, filename);

                        // Eğer dosya yoksa, sadece dosya adını kullanarak logs klasöründe ara
                        if (!File.Exists(fullPath))
                        {
                            // Tüm logs klasöründe ve alt klasörlerinde ara
                            string logDirectory = Path.Combine(Application.StartupPath, "logs");
                            string[] foundFiles = Directory.GetFiles(logDirectory, filename, SearchOption.AllDirectories);

                            if (foundFiles.Length > 0)
                            {
                                fullPath = foundFiles[0]; // İlk bulunan dosyayı kullan
                            }
                            else
                            {
                                // Son çare olarak logs klasöründe direkt ara
                                fullPath = Path.Combine(Application.StartupPath, "logs", filename);
                            }
                        }

                        if (File.Exists(fullPath))
                        {
                            string ftpServerIP = ftpServer;
                            string ftpUserName = ftpUsername;
                            string ftpPass = ftpPassword;

                            // Dosyayı FTP'ye yükle
                            await Task.Run(() => UploadFileToFTP(fullPath, "loglar/" + filename, ftpServerIP, ftpUserName, ftpPass));
                            uploadedFiles++;

                            // Durum bilgisini güncelle
                            _ = setToolStatusLabel($"FTP'ye yükleniyor: {uploadedFiles}/{totalFiles}", manuelupdate: true, renk: "yellow");
                        }
                        else
                        {
                            failedFiles++;
                            logger.Error($"File not found: {filename}");
                        }
                    }
                    catch (Exception ex)
                    {
                        failedFiles++;
                        logger.Error($"Error uploading {filename}: {ex.Message}");
                    }
                }

                // İşlem tamamlandığında bildirim göster
                string resultMessage = failedFiles > 0
                    ? $"{uploadedFiles} dosya başarıyla yüklendi, {failedFiles} dosya yüklenemedi."
                    : $"{uploadedFiles} dosya başarıyla yüklendi.";

                _ = setToolStatusLabel(resultMessage, manuelupdate: true, renk: "green");

                this.Invoke(new Action(() => {
                    if (webView?.CoreWebView2 != null)
                    {
                        webView.CoreWebView2.PostWebMessageAsJson(JsonSerializer.Serialize(new
                        {
                            type = "notification",
                            message = resultMessage
                        }));
                    }
                }));
            }
            catch (Exception ex)
            {
                logger.Error($"Upload multiple log files to FTP error: {ex.Message}");
                MessageBox.Show($"Log dosyaları FTP'ye yüklenirken hata oluştu: {ex.Message}", "Hata", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        // Birden fazla log dosyasını sil
        private void DeleteMultipleLocalLogFiles(JsonElement filenames)
        {
            try
            {
                int totalFiles = filenames.GetArrayLength();
                int deletedFiles = 0;
                int failedFiles = 0;

                foreach (var filenameElement in filenames.EnumerateArray())
                {
                    string filename = filenameElement.GetString();

                    try
                    {
                        // Önce tam yol olarak dene
                        string fullPath = Path.Combine(Application.StartupPath, filename);

                        // Eğer dosya yoksa, sadece dosya adını kullanarak logs klasöründe ara
                        if (!File.Exists(fullPath))
                        {
                            // Tüm logs klasöründe ve alt klasörlerinde ara
                            string logDirectory = Path.Combine(Application.StartupPath, "logs");
                            string[] foundFiles = Directory.GetFiles(logDirectory, filename, SearchOption.AllDirectories);

                            if (foundFiles.Length > 0)
                            {
                                fullPath = foundFiles[0]; // İlk bulunan dosyayı kullan
                            }
                            else
                            {
                                // Son çare olarak logs klasöründe direkt ara
                                fullPath = Path.Combine(Application.StartupPath, "logs", filename);
                            }
                        }

                        if (File.Exists(fullPath))
                        {
                            File.Delete(fullPath);
                            deletedFiles++;
                        }
                        else
                        {
                            failedFiles++;
                            logger.Error($"File not found for deletion: {filename}");
                        }
                    }
                    catch (Exception ex)
                    {
                        failedFiles++;
                        logger.Error($"Error deleting {filename}: {ex.Message}");
                    }
                }

                // İşlem tamamlandığında bildirim göster
                string resultMessage = failedFiles > 0
                    ? $"{deletedFiles} dosya başarıyla silindi, {failedFiles} dosya silinemedi."
                    : $"{deletedFiles} dosya başarıyla silindi.";

                this.Invoke(new Action(() => {
                    if (webView?.CoreWebView2 != null)
                    {
                        // Bildirim gönder
                        webView.CoreWebView2.PostWebMessageAsJson(JsonSerializer.Serialize(new
                        {
                            type = "notification",
                            message = resultMessage
                        }));

                        // Dosya listesini yenile
                        GetAllLocalLogFiles();
                    }
                }));
            }
            catch (Exception ex)
            {
                logger.Error($"Delete multiple log files error: {ex.Message}");
                MessageBox.Show($"Log dosyaları silinirken hata oluştu: {ex.Message}", "Hata", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        // Dosya konumunu aç
        private void OpenLocalLogFileLocation(string filename)
        {
            try
            {
                // Önce tam yol olarak dene
                string fullPath = Path.Combine(Application.StartupPath, filename);

                // Eğer dosya yoksa, sadece dosya adını kullanarak logs klasöründe ara
                if (!File.Exists(fullPath))
                {
                    // Tüm logs klasöründe ve alt klasörlerinde ara
                    string logDirectory = Path.Combine(Application.StartupPath, "logs");
                    string[] foundFiles = Directory.GetFiles(logDirectory, filename, SearchOption.AllDirectories);

                    if (foundFiles.Length > 0)
                    {
                        fullPath = foundFiles[0]; // İlk bulunan dosyayı kullan
                    }
                    else
                    {
                        // Son çare olarak logs klasöründe direkt ara
                        fullPath = Path.Combine(Application.StartupPath, "logs", filename);
                    }
                }

                if (File.Exists(fullPath))
                {
                    // Dosya konumunu aç
                    string argument = $"/select,\"{fullPath}\"";
                    Process.Start("explorer.exe", argument);
                }
                else
                {
                    MessageBox.Show($"\"{filename}\" dosyası bulunamadı.", "Hata", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                logger.Error($"Open log file location error: {ex.Message}");
                MessageBox.Show($"Dosya konumu açılırken hata oluştu: {ex.Message}", "Hata", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        // Uygulama işlemleri için yardımcı metodlar
        private async Task LaunchApplication()
        {
            try
            {
                if (selectedGame.Contains("Seçilmedi"))
                {
                    MessageBox.Show("Önce oyun seçin!");
                    return;
                }
                string AOSbundleID = getGameDataFromDataTable(selectedGame, "bundleid");
                Process runApp = GetProcess("adb", "shell monkey -p " + AOSbundleID + " -v 1");
                runApp.Start();
                runApp.WaitForExit();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Uygulama başlatılırken hata oluştu: {ex.Message}", "Hata", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        private async Task CloseApplication()
        {
            try
            {
                if (selectedGame.Contains("Seçilmedi"))
                {
                    MessageBox.Show("Önce oyun seçin!");
                    return;
                }
                string AOSbundleID = getGameDataFromDataTable(selectedGame, "bundleid");
                Process runApp = GetProcess("adb", "shell am force-stop " + AOSbundleID);
                runApp.Start();
                runApp.WaitForExit();
                _ = setToolStatusLabel("Uygulama Kapatıldı", "green");
                SendNotificationToDevice("Uygulama Kapatıldı", selectedGame + " uygulaması zorla kapatıldı.");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Uygulama kapatılırken hata oluştu: {ex.Message}", "Hata", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        private async Task ViewInStore()
        {
            try
            {
                if (selectedGame.Contains("Seçilmedi"))
                {
                    MessageBox.Show("Önce oyun seçin!");
                    return;
                }
                string AOSbundleID = getGameDataFromDataTable(selectedGame, "bundleid");
                Process runApp = GetProcess("adb", "shell am start -a android.intent.action.VIEW -d 'market://details?id=" + AOSbundleID + "'");
                runApp.Start();
                runApp.WaitForExit();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Mağaza açılırken hata oluştu: {ex.Message}", "Hata", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        private async Task UninstallApplication()
        {
            try
            {
                if (selectedGame.Contains("Seçilmedi"))
                {
                    MessageBox.Show("Önce oyun seçin!");
                    return;
                }
                string AOSbundleID = getGameDataFromDataTable(selectedGame, "bundleid");
                removeApp(AOSbundleID);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Uygulama kaldırılırken hata oluştu: {ex.Message}", "Hata", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        private async Task OpenInternalLink()
        {

            if (selectedGame.Contains("Seçilmedi"))
            {
                MessageBox.Show("Önce oyun seçin!");
                return;
            }
            var aosinternal = getGameDataFromDataTable(selectedGame);
            if (aosinternal != null)
            {
                if (Uri.IsWellFormedUriString(aosinternal, UriKind.Absolute))
                {
                    Uri link = new Uri(aosinternal);
                    openUrl(link.ToString());
                }
                else
                {
                    MessageBox.Show("Bu uygulama için internal linki tanımlanmamış. İnternal linkine sahipseniz hata bildir ekranında bunu bildirin.");
                }
            }
        }
        private async void logBaslatfromHtml(string logNote)
        {
            if (logStatus == true)
            {
                logStatus = false;
                // Use Invoke to ensure WebView2 operations run on the UI thread
                this.Invoke(new Action(() => {
                    webView.CoreWebView2.PostWebMessageAsJson(JsonSerializer.Serialize(new
                    {
                        type = "updateLogButton",
                        timerReset = true,
                        text = "Log Kaydını Başlat"
                    }));
                }));
                logSure = 0;
                if (selectedOS == "AOS")
                {
                    SendNotificationToDevice("Log Kaydı Durdu", "Log kaydı durdu.");
                    logcatProcess.CancelOutputRead();
                    logcatProcess.Dispose();
                    await Task.Run(() => killProcess("logcat"));
                }
                else if (selectedOS == "IOS")
                {
                    readWriteLog.CancelOutputRead();
                    readWriteLog.Dispose();
                    taskKiller("idevicesyslog.exe");
                }
                await setToolStatusLabel("Loglar Alındı");
                await Task.Delay(1000);
                string ftpServerIP = ftpServer;
                string ftpUserName = ftpUsername;
                string filename = logcatPath;
                if (File.Exists(filename))
                {
                    filename = Path.GetFileName(logcatPath);
                    //filename = filename.Replace(Application.StartupPath, "").Replace("logs\\", "").Replace(DateTime.Now.ToString("yyyy-MM-dd") + "\\", "").Replace(":", "");
                    await Task.Run(() => UploadFileToFTP(logcatPath, "loglar/" + filename, ftpServerIP, ftpUserName, ftpPassword));
                }
            }
            else
            {
                logStatus = true;
                // Use Invoke to ensure WebView2 operations run on the UI thread
                this.Invoke(new Action(() => {
                    webView.CoreWebView2.PostWebMessageAsJson(JsonSerializer.Serialize(new
                    {
                        type = "updateLogButton",
                        text = "Log Kaydını Durdur"
                    }));
                }));
                logcatPath = GetLogcatPath(logNote);
                string bundleid = getGameDataFromDataTable(selectedGame, "bundleid");
                if (selectedOS == "AOS")
                {
                    EnsureLogDirectoriesExist();
                    File.AppendAllText(logcatPath, getDeviceInfo());
                    if (bundleid.Contains("Seçilmedi"))
                    {
                        logcatProcess = GetProcess("adb", $"logcat");
                        logcatProcess.Start();
                        logcatProcess.BeginOutputReadLine();
                        logcatProcess.OutputDataReceived += new DataReceivedEventHandler(logcatProcess_OutputDataReceived);
                    }
                    else
                    {
                        Process runApp = GetProcess("adb", "shell monkey -p " + bundleid + " -v 1");
                        runApp.Start();
                        runApp.WaitForExit();
                        await Task.Delay(2000);
                        Process pidofProcess = GetProcess("adb", $"shell pidof {bundleid}");
                        pidofProcess.Start();
                        string pidofOutput = pidofProcess.StandardOutput.ReadToEnd().Trim();
                        pidofProcess.WaitForExit();
                        if (pidofOutput == "")
                        {
                            await setToolStatusLabel("Uygulamanın cihazda yüklü olduğuna emin olun!", "red");
                            return;
                        }
                        logcatProcess = GetProcess("adb", $"logcat --pid={pidofOutput}");
                        logcatProcess.Start();
                        logcatProcess.BeginOutputReadLine();
                        logcatProcess.OutputDataReceived += new DataReceivedEventHandler(logcatProcess_OutputDataReceived);
                    }
                    SendNotificationToDevice("Log Kaydı Başladı", "Log kaydı başladı.");
                }
                else if (selectedOS == "IOS")
                {
                    if (bundleid.Contains("Seçilmedi"))
                    {
                        readWriteLog = GetProcess("./ioslog/idevicesyslog", "");
                        readWriteLog.Start();
                        readWriteLog.BeginOutputReadLine();
                        readWriteLog.OutputDataReceived += new DataReceivedEventHandler(logcatProcess_OutputDataReceived);
                    }
                    else
                    {
                        string iosAppname = getGameDataFromDataTable(selectedGame, "iosappname");
                        string argue = $"-p \"{iosAppname}\"";
                        if (iosAppname == null)
                        {
                            string AOSbundleID = getGameDataFromDataTable(selectedGame, "bundleid");
                            argue = "";
                            //
                            string json = @"{
                            ""embeds"": [
                                {
                                            ""title"": ""Hata Bildirimi"",
                                            ""description"": """ + AOSbundleID + @"'in IOS App Name Girilmemiş."",
                                            ""color"": 16711680,
                                            ""fields"": [
                                                {
                                                    ""name"": ""Bundle"",
                                                    ""value"": """ + AOSbundleID + @"""
                                                },
                                                {
                                                    ""name"": ""Hata"",
                                                    ""value"": ""Bu uygulamanın IOS App Name girilmemiş.""
                                                },
                                                {
                                                    ""name"": ""Cihaz"",
                                                    ""value"": """ + selectedOS + @""",
                                                    ""inline"": true
                                                }
                                            ],
                                            ""footer"": {
                                                ""text"": ""Hata Bildirimi""
                                            },
                                            ""timestamp"": """ + DateTime.Now.ToString("yyyy-MM-ddTHH:mm:sszzz") + @""",
                                            ""author"": {
                                                ""name"": """ + Properties.Settings.Default.testername + @"""
                                            }
                                        }
                                    ]
                                }";

                            //discordaGonder(json);
                        }
                        readWriteLog = GetProcess("./ioslog/idevicesyslog", argue);
                        readWriteLog.Start();
                        readWriteLog.BeginOutputReadLine();
                        readWriteLog.OutputDataReceived += new DataReceivedEventHandler(logcatProcess_OutputDataReceived);
                    }

                }
            }

        }
        private async Task ClearAppData()
        {
            try
            {
                if (selectedGame.Contains("Seçilmedi"))
                {
                    MessageBox.Show("Önce oyun seçin!");
                    return;
                }
                if (areYouSure(selectedGame + " uygulamasına ait tüm veriler silinecek", "Onaylıyor Musun?"))
                {
                    SendNotificationToDevice("Uygulama Verisi", selectedGame + " uygulamasına ait veriler temizlendi.");
                    string AOSbundleID = getGameDataFromDataTable(selectedGame, "bundleid");
                    Process runApp = GetProcess("adb", "shell pm clear " + AOSbundleID);
                    runApp.Start();
                    runApp.WaitForExit();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Uygulama kaldırılırken hata oluştu: {ex.Message}", "Hata", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        /// <summary>
        /// Bir dosyanın yükleme durumu ve Drive bilgilerini saklamak için kullanılan sınıf
        /// </summary>
        public class FileUploadInfo
        {
            public string Status { get; set; } = "";
            public string DriveId { get; set; } = "";
            public string DriveUrl { get; set; } = "";
            public string FolderId { get; set; } = "";
        }
        /// <summary>
        /// Video ve SS dosyalarının yükleme durumlarını saklamak için kullanılan sınıf
        /// </summary>
        public class VideoSsUploadStatus
        {
            public Dictionary<string, FileUploadInfo> FileStatuses { get; set; } = new Dictionary<string, FileUploadInfo>();
        }
        /// <summary>
        /// Video ve SS dosyalarının yükleme durumlarını JSON dosyasından yükler
        /// </summary>
        /// <returns>Yükleme durumları</returns>
        private VideoSsUploadStatus LoadVideoSsUploadStatus()
        {
            try
            {
                string statusFilePath = Path.Combine(Application.StartupPath, "video_ss_upload_status.json");
                if (File.Exists(statusFilePath))
                {
                    string json = File.ReadAllText(statusFilePath);
                    return JsonSerializer.Deserialize<VideoSsUploadStatus>(json) ?? new VideoSsUploadStatus();
                }
            }
            catch (Exception ex)
            {
                logger.Error($"Yükleme durumları yüklenirken hata oluştu: {ex.Message}");
            }

            return new VideoSsUploadStatus();
        }
        /// <summary>
        /// Video ve SS dosyalarının yükleme durumlarını JSON dosyasına kaydeder
        /// </summary>
        /// <param name="status">Kaydedilecek yükleme durumları</param>
        private void SaveVideoSsUploadStatus(VideoSsUploadStatus status)
        {
            try
            {
                string statusFilePath = Path.Combine(Application.StartupPath, "video_ss_upload_status.json");
                string json = JsonSerializer.Serialize(status, new JsonSerializerOptions { WriteIndented = true });
                File.WriteAllText(statusFilePath, json);
            }
            catch (Exception ex)
            {
                logger.Error($"Yükleme durumları kaydedilirken hata oluştu: {ex.Message}");
            }
        }
        /// <summary>
        /// Seçili dosyaları Google Drive'a yükler
        /// </summary>
        /// <param name="filenames">Yüklenecek dosya adları</param>
        /// <param name="folderId">Yüklenecek klasör ID'si</param>
        private async Task UploadFilesToDrive(JsonElement filenames, string folderId)
        {
            // JsonElement'i List<string>'e dönüştür
            List<string> fileList = new List<string>();
            foreach (JsonElement element in filenames.EnumerateArray())
            {
                fileList.Add(element.GetString());
            }

            // List<string> parametreli metodu çağır
            await UploadFilesToDrive(fileList, folderId);
        }
        /// <summary>
        /// Seçili dosyaları Google Drive'a yükler
        /// </summary>
        /// <param name="filenames">Yüklenecek dosya adları</param>
        /// <param name="folderId">Yüklenecek klasör ID'si</param>
        private async Task UploadFilesToDrive(List<string> filenames, string folderId)
        {
            try
            {
                // Dosyaları geçici klasöre çek
                string tempFolderPath = Path.Combine(Path.GetTempPath(), "qa_utility");
                int totalFiles = filenames.Count;
                int uploadedFiles = 0;

                // Durum bilgisini güncelle
                _ = setToolStatusLabel($"Drive'a yükleniyor: 0/{totalFiles}", manuelupdate: true, renk: "yellow");

                // Yükleme durumlarını yükle
                VideoSsUploadStatus uploadStatus = LoadVideoSsUploadStatus();

                // Her dosya için ayrı ayrı işlem yap
                foreach (string filename in filenames)
                {
                    string localFilePath = Path.Combine(tempFolderPath, filename);

                    // Dosya geçici klasörde yoksa cihazdan çek
                    if (!File.Exists(localFilePath))
                    {
                        dosyayiCihazdanTempeGetir(filename);
                    }

                    // Dosyanın durumunu "Yükleniyor" olarak güncelle
                    if (!uploadStatus.FileStatuses.ContainsKey(filename))
                    {
                        uploadStatus.FileStatuses[filename] = new FileUploadInfo { Status = "Yükleniyor" };
                    }
                    else
                    {
                        uploadStatus.FileStatuses[filename].Status = "Yükleniyor";
                    }

                    // Durum bilgisini kaydet ve UI'yi güncelle
                    SaveVideoSsUploadStatus(uploadStatus);
                    SendVideoSsData();

                    // Dosyayı Drive'a yükle - Yeni GoogleApiService sınıfını kullan
                    var uploadResult = await GoogleApiService.UploadFileToDrive(localFilePath, folderId);
                    if (uploadResult.Success)
                    {
                        uploadedFiles++;
                        _ = setToolStatusLabel($"Drive'a yükleniyor: {uploadedFiles}/{totalFiles}", manuelupdate: true, renk: "yellow");

                        // Dosyanın durumunu ve Drive bilgilerini güncelle
                        uploadStatus.FileStatuses[filename] = new FileUploadInfo
                        {
                            Status = "Yüklendi",
                            DriveId = uploadResult.FileId,
                            DriveUrl = uploadResult.WebViewLink,
                            FolderId = folderId
                        };

                        // Durum bilgisini kaydet ve UI'yi güncelle
                        SaveVideoSsUploadStatus(uploadStatus);
                        SendVideoSsData();
                    }
                    else
                    {
                        // Yükleme başarısız olduysa durumu güncelle
                        uploadStatus.FileStatuses[filename].Status = "Hata";
                        SaveVideoSsUploadStatus(uploadStatus);
                        SendVideoSsData();
                    }
                }

                // İşlem tamamlandı
                _ = setToolStatusLabel($"Drive'a yükleme tamamlandı: {uploadedFiles}/{totalFiles}", manuelupdate: true, renk: "green");

                // Bildirim gönder
                notificationSendToHtml($"{uploadedFiles} dosya Drive'a yüklendi.");
            }
            catch (Exception ex)
            {
                logger.Error($"Drive'a yükleme hatası: {ex.Message}");
                MessageBox.Show($"Drive'a yükleme sırasında hata oluştu: {ex.Message}", "Hata", MessageBoxButtons.OK, MessageBoxIcon.Error);

                // Hata bildirimini gönder
                notificationSendToHtml("Drive'a yükleme sırasında hata oluştu.");
            }
        }
        /// <summary>
        /// Google Drive klasörlerini getirir ve HTML tarafına gönderir
        /// </summary>
        /// <param name="parentId">Parent klasör ID'si</param>
        private async Task GetDriveFolders(string parentId)
        {
            try
            {
                // Drive servisini başlat - Yeni GoogleApiService sınıfını kullan
                if (!await GoogleApiService.InitializeDriveService())
                {
                    notificationSendToHtml("Google Drive servisine bağlanılamadı.");
                    return;
                }

                // Klasörleri getir - Yeni GoogleApiService sınıfını kullan
                var folders = await GoogleApiService.GetFolders(parentId);

                // HTML tarafına gönder
                webView.CoreWebView2.PostWebMessageAsJson(JsonSerializer.Serialize(new
                {
                    type = "driveFolders",
                    parentId = parentId,
                    folders = folders
                }));
            }
            catch (Exception ex)
            {
                logger.Error($"Drive klasörlerini getirme hatası: {ex.Message}");
                notificationSendToHtml("Drive klasörlerini getirirken hata oluştu.");
            }
        }
        /// <summary>
        /// Google Drive'da yeni klasör oluşturur
        /// </summary>
        /// <param name="parentId">Parent klasör ID'si</param>
        /// <param name="folderName">Oluşturulacak klasör adı</param>
        /// <param name="isNested">Alt klasör oluşturma işlemi mi?</param>
        /// <param name="currentIndex">Mevcut klasör indeksi</param>
        /// <param name="totalParts">Toplam klasör sayısı</param>
        /// <param name="uploadAfterCreate">Klasör oluşturduktan sonra dosyaları yükle</param>
        /// <param name="filesToUpload">Yüklenecek dosyaların listesi</param>
        private async Task CreateDriveFolder(string parentId, string folderName, bool isNested = false, int currentIndex = 0, int totalParts = 0, bool uploadAfterCreate = false, List<string> filesToUpload = null)
        {
            try
            {
                // Drive servisini başlat - Yeni GoogleApiService sınıfını kullan
                if (!await GoogleApiService.InitializeDriveService())
                {
                    notificationSendToHtml("Google Drive servisine bağlanılamadı.");
                    return;
                }

                // Önce aynı isimde klasör var mı kontrol et
                bool isExistingFolder = false;
                var existingFolder = await GoogleApiService.GetFolderByName(parentId, folderName);
                if (existingFolder != null)
                {
                    // Aynı isimde klasör varsa onu kullan
                    notificationSendToHtml($"'{folderName}' klasörü zaten mevcut, kullanılıyor.");
                    isExistingFolder = true;
                }

                // Klasörü oluştur veya mevcut klasörü kullan
                var folder = isExistingFolder ? existingFolder : await GoogleApiService.CreateFolder(parentId, folderName);

                if (folder != null)
                {
                    // Klasör oluşturduktan sonra dosyaları yükle
                    if (uploadAfterCreate && filesToUpload != null && filesToUpload.Count > 0)
                    {
                        notificationSendToHtml($"Dosyalar '{folderName}' klasörüne yükleniyor...");
                        await UploadFilesToDrive(filesToUpload, folder.Id);
                    }

                    // HTML tarafına gönder
                    webView.CoreWebView2.PostWebMessageAsJson(JsonSerializer.Serialize(new
                    {
                        type = "newDriveFolder",
                        parentId = parentId,
                        folder = folder,
                        isNested = isNested,
                        currentIndex = currentIndex,
                        totalParts = totalParts,
                        uploadAfterCreate = uploadAfterCreate,
                        isExistingFolder = isExistingFolder
                    }));
                }
                else
                {
                    notificationSendToHtml("Drive klasörü oluşturulamadı.");
                }
            }
            catch (Exception ex)
            {
                logger.Error($"Drive klasörü oluşturma hatası: {ex.Message}");
                notificationSendToHtml("Drive klasörü oluşturulurken hata oluştu.");
            }
        }
        /// <summary>
        /// Google Drive'da iç içe klasörler oluşturur
        /// </summary>
        /// <param name="parentId">Başlangıç klasör ID'si</param>
        /// <param name="folderPath">Oluşturulacak klasör yolu (/ ile ayrılmış)</param>
        /// <param name="filesToUpload">Yüklenecek dosyaların listesi</param>
        private async Task CreateNestedFolders(string parentId, string folderPath, List<string> filesToUpload = null)
        {
            try
            {
                // Klasör yolunu parçalara ayır
                string[] folderParts = folderPath.Split('/');

                // Boş parçaları filtrele
                folderParts = folderParts.Where(p => !string.IsNullOrWhiteSpace(p)).ToArray();

                if (folderParts.Length == 0)
                {
                    notificationSendToHtml("Geçerli bir klasör yolu belirtilmedi.");
                    return;
                }

                // Klasör oluşturma işlemi başladı
                notificationSendToHtml($"Klasörler oluşturuluyor: {folderPath}");

                // İç içe klasör oluşturma işlemi için recursive yardımcı metod
                await CreateNestedFoldersRecursive(parentId, folderParts, 0, filesToUpload);
            }
            catch (Exception ex)
            {
                logger.Error($"Drive iç içe klasör oluşturma hatası: {ex.Message}");
                notificationSendToHtml("Drive klasörleri oluşturulurken hata oluştu.");
            }
        }
        /// <summary>
        /// Google Drive'da iç içe klasörler oluşturmak için recursive yardımcı metod
        /// </summary>
        private async Task CreateNestedFoldersRecursive(string parentId, string[] folderParts, int index, List<string> filesToUpload)
        {
            // Tüm klasörler oluşturulduysa işlemi tamamla
            if (index >= folderParts.Length)
            {
                notificationSendToHtml("Klasörler başarıyla oluşturuldu.");
                return;
            }

            // Mevcut seviyedeki klasörü oluştur veya varsa mevcut klasörü kullan
            bool isLastFolder = (index == folderParts.Length - 1);
            bool shouldUploadFiles = isLastFolder && filesToUpload != null && filesToUpload.Count > 0;
            bool isExistingFolder = false;

            // Önce aynı isimde klasör var mı kontrol et
            var existingFolder = await GoogleApiService.GetFolderByName(parentId, folderParts[index]);
            if (existingFolder != null)
            {
                // Aynı isimde klasör varsa onu kullan
                notificationSendToHtml($"'{folderParts[index]}' klasörü zaten mevcut, kullanılıyor.");
                isExistingFolder = true;
            }

            // Klasörü oluştur veya mevcut klasörü kullan
            var folder = isExistingFolder ? existingFolder : await GoogleApiService.CreateFolder(parentId, folderParts[index]);

            if (folder != null)
            {
                // Son klasörse ve dosyalar varsa yükle
                if (shouldUploadFiles)
                {
                    notificationSendToHtml($"Dosyalar '{folderParts[index]}' klasörüne yükleniyor...");
                    await UploadFilesToDrive(filesToUpload, folder.Id);
                }

                // Bir sonraki seviyeye geç
                if (!isLastFolder)
                {
                    await CreateNestedFoldersRecursive(folder.Id, folderParts, index + 1, filesToUpload);
                }

                // HTML tarafına bilgi gönder
                webView.CoreWebView2.PostWebMessageAsJson(JsonSerializer.Serialize(new
                {
                    type = "newDriveFolder",
                    parentId = parentId,
                    folder = folder,
                    isNested = true,
                    currentIndex = index,
                    totalParts = folderParts.Length,
                    uploadAfterCreate = shouldUploadFiles,
                    isExistingFolder = isExistingFolder
                }));
            }
            else
            {
                notificationSendToHtml($"'{folderParts[index]}' klasörü oluşturulamadı.");
            }
        }
        // FTP'de klasör oluşturmak için yardımcı metod
        public bool CreateFtpDirectory(string directoryPath, string ftpServerAddress, string ftpUsername, string ftpPassword)
        {
            try
            {
                FtpWebRequest request = (FtpWebRequest)WebRequest.Create($"{ftpServerAddress}/{directoryPath}");
                request.Credentials = new NetworkCredential(ftpUsername, ftpPassword);
                request.Method = WebRequestMethods.Ftp.MakeDirectory;

                using (FtpWebResponse response = (FtpWebResponse)request.GetResponse())
                {
                    return true;
                }
            }
            catch (WebException ex)
            {
                // 550 hatası klasörün zaten var olduğunu gösterebilir
                if (ex.Response is FtpWebResponse response && response.StatusCode == FtpStatusCode.ActionNotTakenFileUnavailable)
                {
                    return true; // Klasör zaten var
                }

                logger.Error(ex + " FTP klasör oluşturma hatası: " + directoryPath);
                return false;
            }
        }
        // FTP'de klasör yapısını oluşturmak için yardımcı metod
        public bool EnsureFtpDirectoryExists(string directoryPath, string ftpServerAddress, string ftpUsername, string ftpPassword)
        {
            // Klasör yolunu parçalara ayırıyoruz
            string[] parts = directoryPath.Split('/');
            string currentPath = "";

            // Her bir seviyedeki klasörü oluşturuyoruz
            for (int i = 0; i < parts.Length; i++)
            {
                if (string.IsNullOrEmpty(parts[i]))
                    continue;

                currentPath += parts[i] + "/";
                bool success = CreateFtpDirectory(currentPath, ftpServerAddress, ftpUsername, ftpPassword);
                if (!success)
                    return false;
            }

            return true;
        }
        // Kullanıcı verilerini WebView'e gönderen fonksiyon
        private async Task SendAllTestersDataToWebView()
        {
            try
            {
                // Kullanıcı verilerini al
                testerList.Clear();
                string sql = "Select * from login";
                MySqlDataAdapter verial = new MySqlDataAdapter(sql, coon);
                verial.Fill(testerList);

                // Verileri JSON formatına dönüştür
                var users = new List<object>();
                foreach (DataRow row in testerList.Rows)
                {
                    users.Add(new
                    {
                        id = row["id"].ToString(),
                        user = row["user"].ToString(),
                        ip = row["ip"].ToString(),
                        lastlogin = row["lastlogin"].ToString(),
                        role = row["role"].ToString()
                    });
                }

                // WebView2'ye JSON mesajı gönder
                if (webView?.CoreWebView2 != null)
                {
                    var message = new
                    {
                        type = "userData",
                        data = users
                    };

                    string jsonMessage = JsonSerializer.Serialize(message);
                    this.Invoke(new Action(() => webView.CoreWebView2.PostWebMessageAsJson(jsonMessage)));

                    // Bildirim göster
                    notificationSendToHtml("Kullanıcı verileri başarıyla yüklendi.");
                }
            }
            catch (Exception ex)
            {
                logger.Error($"User data sending error: {ex.Message}");
                notificationSendToHtml("Kullanıcı verileri yüklenirken hata oluştu.", "error");
            }
        }
        // Yeni kullanıcı ekleyen fonksiyon
        private async Task AddUser(string username, string role)
        {
            try
            {
                // Kullanıcı adı kontrolü
                string checkSql = $"SELECT COUNT(*) FROM login WHERE user = '{username}'";
                if (coon.State != ConnectionState.Open)
                {
                    coon.Open();
                }

                using (MySqlCommand cmd = new MySqlCommand(checkSql, coon))
                {
                    int count = Convert.ToInt32(cmd.ExecuteScalar());
                    if (count > 0)
                    {
                        notificationSendToHtml($"'{username}' kullanıcı adı zaten kullanılıyor!", "error");
                        coon.Close();
                        return;
                    }
                }

                // Yeni kullanıcı ekle
                string insertSql = $"INSERT INTO login (user, role) VALUES ('{username}', '{role}')";
                using (MySqlCommand cmd = new MySqlCommand(insertSql, coon))
                {
                    cmd.ExecuteNonQuery();
                }

                coon.Close();

                // Kullanıcı listesini güncelle
                await SendAllTestersDataToWebView();

                notificationSendToHtml($"'{username}' kullanıcısı başarıyla eklendi.", "success");
            }
            catch (Exception ex)
            {
                logger.Error($"Add user error: {ex.Message}");
                notificationSendToHtml("Kullanıcı eklenirken hata oluştu.", "error");
                if (coon.State == ConnectionState.Open)
                {
                    coon.Close();
                }
            }
        }
        // Kullanıcı güncelleyen fonksiyon
        private async Task UpdateUser(int id, string username, string role)
        {
            try
            {
                // Kullanıcı adı kontrolü (aynı ID'ye sahip olmayan başka bir kullanıcı aynı kullanıcı adını kullanıyor mu?)
                string checkSql = $"SELECT COUNT(*) FROM login WHERE user = '{username}' AND id != {id}";
                if (coon.State != ConnectionState.Open)
                {
                    coon.Open();
                }

                using (MySqlCommand cmd = new MySqlCommand(checkSql, coon))
                {
                    int count = Convert.ToInt32(cmd.ExecuteScalar());
                    if (count > 0)
                    {
                        notificationSendToHtml($"'{username}' kullanıcı adı zaten kullanılıyor!", "error");
                        coon.Close();
                        return;
                    }
                }

                // Kullanıcıyı güncelle
                string updateSql;
                    // Şifre varsa, şifreyi de güncelle
                updateSql = $"UPDATE login SET user = '{username}', role = '{role}' WHERE id = {id}";


                using (MySqlCommand cmd = new MySqlCommand(updateSql, coon))
                {
                    cmd.ExecuteNonQuery();
                }

                coon.Close();

                // Kullanıcı listesini güncelle
                await SendAllTestersDataToWebView();

                notificationSendToHtml($"'{username}' kullanıcısı başarıyla güncellendi.", "success");
            }
            catch (Exception ex)
            {
                logger.Error($"Update user error: {ex.Message}");
                notificationSendToHtml("Kullanıcı güncellenirken hata oluştu.", "error");
                if (coon.State == ConnectionState.Open)
                {
                    coon.Close();
                }
            }
        }
        /// <summary>
        /// APK yükleme formunu açar ve işlemleri yönetir
        /// </summary>
        private void OpenApkUploaderForm()
        {
            try
            {
                logger.Info("APK yükleme formu açılıyor...");

                // Yeni bir ApkUploaderForm örneği oluştur
                using (ApkUploaderForm apkUploaderForm = new ApkUploaderForm())
                {
                    // ApkUploaderForm'dan gelen event'i dinle
                    apkUploaderForm.ApkReadyToInstall += (sender, filePath) =>
                    {
                        // Yükleme işlemini burada başlat
                        InstallApk(filePath);
                    };

                    // Formu modal olarak göster
                    apkUploaderForm.ShowDialog(this);
                }
            }
            catch (Exception ex)
            {
                logger.Error($"APK yükleme formu açılırken hata oluştu: {ex.Message}", ex);
                MessageBox.Show($"APK yükleme formu açılırken hata oluştu: {ex.Message}", "Hata", MessageBoxButtons.OK, MessageBoxIcon.Error);
                _ = setToolStatusLabel("APK yükleme hatası", "red");
            }
        }
        // Kullanıcı silen fonksiyon
        private async Task DeleteUser(int id)
        {
            try
            {
                // Kullanıcı adını al (bildirim için)
                string username = "";
                string getSql = $"SELECT user FROM login WHERE id = {id}";
                if (coon.State != ConnectionState.Open)
                {
                    coon.Open();
                }

                using (MySqlCommand cmd = new MySqlCommand(getSql, coon))
                {
                    var result = cmd.ExecuteScalar();
                    if (result != null)
                    {
                        username = result.ToString();
                    }
                }

                // Kullanıcıyı sil
                string deleteSql = $"DELETE FROM login WHERE id = {id}";
                using (MySqlCommand cmd = new MySqlCommand(deleteSql, coon))
                {
                    cmd.ExecuteNonQuery();
                }

                coon.Close();

                // Kullanıcı listesini güncelle
                await SendAllTestersDataToWebView();

                notificationSendToHtml($"'{username}' kullanıcısı başarıyla silindi.", "success");
            }
            catch (Exception ex)
            {
                logger.Error($"Delete user error: {ex.Message}");
                notificationSendToHtml("Kullanıcı silinirken hata oluştu.", "error");
                if (coon.State == ConnectionState.Open)
                {
                    coon.Close();
                }
            }
        }
    }
}
