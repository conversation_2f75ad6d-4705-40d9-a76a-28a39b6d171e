﻿<?xml version="1.0" encoding="utf-8" ?>
<configuration>
  <configSections>
    <sectionGroup name="userSettings" type="System.Configuration.UserSettingsGroup, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" >
      <section name="QA_Utility.Properties.Settings" type="System.Configuration.ClientSettingsSection, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" allowExeDefinition="MachineToLocalUser" requirePermission="false" />
      <section name="logger.Properties.Settings" type="System.Configuration.ClientSettingsSection, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" allowExeDefinition="MachineToLocalUser" requirePermission="false" />
    </sectionGroup>
  </configSections>
  <appSettings>
    <!--<add key="FtpServer" value="ftp://ftp01.doracdn.com/" />
    <add key="FtpUsername" value="joygame-jgmobile" />
    <add key="FtpPassword" value="p64wwvrcww" />-->


    <!-- FTP Ayarları -->
    <add key="FtpServer" value="ftp://ftp.mncdn.com/" />
    <add key="FtpUsername" value="qa-mobiltech_999265cf" />
    <add key="FtpPassword" value="4fsR2sgh!" />
    
    <!-- Media Nova Api Key-->
    <add key="MediaNovaKey"  value="K4BuBIXmlQcjuGRrNA6S5whMibiusKn4"/>
    <add key="MediaNovaSecret"  value="$2y$10$10cf72aa048e9b4c35d140d9bd584426a5edf1a8e7dd56e217e9c01ba7809fbc"/>

    <!-- TestRail API Ayarları -->
    <add key="TestRailBaseUrl" value="https://joygame.testrail.io/" />
    <add key="TestRailUsername" value="<EMAIL>" />
    <add key="TestRailPassword" value="rqGHRmYCuBH4J6rmnhd6!" />
    <!--<add key="TestRailApiKey" value="lvC0t3XwQsbxL6sCabsn-PUK/G2PLmOBS15rlfzjo" />-->
    <add key="DbConnectionString" value="Server=185.70.86.80;database=mobile_log;User ID=admintool; Password=*********;pooling=false;SslMode=none;charset=utf8mb4;" />
  </appSettings>
  <userSettings>
    <QA_Utility.Properties.Settings>
      <setting name="testername" serializeAs="String">
        <value />
      </setting>
      <setting name="alwaysTop" serializeAs="String">
        <value>False</value>
      </setting>
      <setting name="autoGetBundle" serializeAs="String">
        <value>False</value>
      </setting>
      <setting name="rememberMe" serializeAs="String">
        <value>False</value>
      </setting>
      <setting name="redmineid" serializeAs="String">
        <value />
      </setting>
      <setting name="redminepw" serializeAs="String">
        <value />
      </setting>
      <setting name="FolderLocation" serializeAs="String">
        <value />
      </setting>
      <setting name="TestRailBaseUrl" serializeAs="String">
        <value />
      </setting>
      <setting name="TestRailUsername" serializeAs="String">
        <value />
      </setting>
      <setting name="TestRailPassword" serializeAs="String">
        <value />
      </setting>
      <setting name="TestRailEnabled" serializeAs="String">
        <value>False</value>
      </setting>
      <setting name="IcardUsername" serializeAs="String">
        <value />
      </setting>
      <setting name="IcardPassword" serializeAs="String">
        <value />
      </setting>
    </QA_Utility.Properties.Settings>
    <logger.Properties.Settings>
      <setting name="testername" serializeAs="String">
        <value />
      </setting>
    </logger.Properties.Settings>
  </userSettings>
</configuration>