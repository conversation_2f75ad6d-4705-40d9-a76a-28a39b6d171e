[1mdiff --git a/qa_utility/Form1.Designer.cs b/qa_utility/Form1.Designer.cs[m
[1mindex 45a0271..39e6e1e 100644[m
[1m--- a/qa_utility/Form1.Designer.cs[m
[1m+++ b/qa_utility/Form1.Designer.cs[m
[36m@@ -31,8 +31,6 @@[m
             components = new System.ComponentModel.Container();[m
             button1 = new Button();[m
             groupBox1 = new GroupBox();[m
[31m-            button10 = new Button();[m
[31m-            textBox1 = new TextBox();[m
             pictureBox1 = new PictureBox();[m
             groupBox5 = new GroupBox();[m
             selectTester = new TextBox();[m
[36m@@ -45,12 +43,17 @@[m
             StatusPanel = new Panel();[m
             DeviceStatusLabel = new Label();[m
             aosPanel = new Panel();[m
[31m-            button7 = new Button();[m
[32m+[m[32m            tabControl1 = new TabControl();[m
[32m+[m[32m            tabPage2 = new TabPage();[m
             button2 = new Button();[m
[31m-            button6 = new Button();[m
[31m-            button3 = new Button();[m
[31m-            button4 = new Button();[m
[32m+[m[32m            button11 = new Button();[m
[32m+[m[32m            button7 = new Button();[m
             button5 = new Button();[m
[32m+[m[32m            button4 = new Button();[m
[32m+[m[32m            button3 = new Button();[m
[32m+[m[32m            tabPage1 = new TabPage();[m
[32m+[m[32m            button10 = new Button();[m
[32m+[m[32m            textBox1 = new TextBox();[m
             iosPanel = new Panel();[m
             button8 = new Button();[m
             button9 = new Button();[m
[36m@@ -61,7 +64,7 @@[m
             toolStripProgressBar1 = new ToolStripProgressBar();[m
             save = new ContextMenuStrip(components);[m
             kaydetToolStripMenuItem = new ToolStripMenuItem();[m
[31m-            button11 = new Button();[m
[32m+[m[32m            toolStripSeparator1 = new ToolStripSeparator();[m
             groupBox1.SuspendLayout();[m
             ((System.ComponentModel.ISupportInitialize)pictureBox1).BeginInit();[m
             groupBox5.SuspendLayout();[m
[36m@@ -69,6 +72,9 @@[m
             groupBox3.SuspendLayout();[m
             StatusPanel.SuspendLayout();[m
             aosPanel.SuspendLayout();[m
[32m+[m[32m            tabControl1.SuspendLayout();[m
[32m+[m[32m            tabPage2.SuspendLayout();[m
[32m+[m[32m            tabPage1.SuspendLayout();[m
             iosPanel.SuspendLayout();[m
             toolStrip1.SuspendLayout();[m
             save.SuspendLayout();[m
[36m@@ -76,7 +82,7 @@[m
             // [m
             // button1[m
             // [m
[31m-            button1.Location = new Point(268, 3);[m
[32m+[m[32m            button1.Location = new Point(271, 6);[m
             button1.Name = "button1";[m
             button1.Size = new Size(109, 42);[m
             button1.TabIndex = 0;[m
[36m@@ -86,8 +92,6 @@[m
             // [m
             // groupBox1[m
             // [m
[31m-            groupBox1.Controls.Add(button10);[m
[31m-            groupBox1.Controls.Add(textBox1);[m
             groupBox1.Controls.Add(pictureBox1);[m
             groupBox1.Controls.Add(groupBox5);[m
             groupBox1.Controls.Add(groupBox4);[m
[36m@@ -98,29 +102,10 @@[m
             groupBox1.Dock = DockStyle.Fill;[m
             groupBox1.Location = new Point(0, 0);[m
             groupBox1.Name = "groupBox1";[m
[31m-            groupBox1.Size = new Size(857, 882);[m
[32m+[m[32m            groupBox1.Size = new Size(416, 346);[m
             groupBox1.TabIndex = 1;[m
             groupBox1.TabStop = false;[m
             // [m
[31m-            // button10[m
[31m-            // [m
[31m-            button10.Location = new Point(334, 602);[m
[31m-            button10.Name = "button10";[m
[31m-            button10.Size = new Size(75, 23);[m
[31m-            button10.TabIndex = 18;[m
[31m-            button10.Text = "button10";[m
[31m-            button10.UseVisualStyleBackColor = true;[m
[31m-            button10.Click += button10_Click;[m
[31m-            // [m
[31m-            // textBox1[m
[31m-            // [m
[31m-            textBox1.Location = new Point(6, 345);[m
[31m-            textBox1.Multiline = true;[m
[31m-            textBox1.Name = "textBox1";[m
[31m-            textBox1.Size = new Size(406, 251);[m
[31m-            textBox1.TabIndex = 5;[m
[31m-            textBox1.TextChanged += textBox1_TextChanged_1;[m
[31m-            // [m
             // pictureBox1[m
             // [m
             pictureBox1.BackColor = SystemColors.ActiveCaptionText;[m
[36m@@ -131,6 +116,7 @@[m
             pictureBox1.SizeMode = PictureBoxSizeMode.Zoom;[m
             pictureBox1.TabIndex = 13;[m
             pictureBox1.TabStop = false;[m
[32m+[m[32m            pictureBox1.Visible = false;[m
             pictureBox1.Click += pictureBox1_Click;[m
             pictureBox1.MouseClick += pictureBox1_MouseClick;[m
             pictureBox1.MouseMove += pictureBox1_MouseMove;[m
[36m@@ -243,14 +229,7 @@[m
             // aosPanel[m
             // [m
             aosPanel.BorderStyle = BorderStyle.FixedSingle;[m
[31m-            aosPanel.Controls.Add(button11);[m
[31m-            aosPanel.Controls.Add(button7);[m
[31m-            aosPanel.Controls.Add(button2);[m
[31m-            aosPanel.Controls.Add(button6);[m
[31m-            aosPanel.Controls.Add(button3);[m
[31m-            aosPanel.Controls.Add(button4);[m
[31m-            aosPanel.Controls.Add(button5);[m
[31m-            aosPanel.Controls.Add(button1);[m
[32m+[m[32m            aosPanel.Controls.Add(tabControl1);[m
             aosPanel.Enabled = false;[m
             aosPanel.Location = new Point(6, 133);[m
             aosPanel.Name = "aosPanel";[m
[36m@@ -258,9 +237,56 @@[m
             aosPanel.TabIndex = 15;[m
             aosPanel.Paint += aosPanel_Paint;[m
             // [m
[32m+[m[32m            // tabControl1[m
[32m+[m[32m            //[m[41m [m
[32m+[m[32m            tabControl1.Controls.Add(tabPage2);[m
[32m+[m[32m            tabControl1.Controls.Add(tabPage1);[m
[32m+[m[32m            tabControl1.Location = new Point(5, 3);[m
[32m+[m[32m            tabControl1.Name = "tabControl1";[m
[32m+[m[32m            tabControl1.SelectedIndex = 0;[m
[32m+[m[32m            tabControl1.Size = new Size(396, 198);[m
[32m+[m[32m            tabControl1.TabIndex = 7;[m
[32m+[m[32m            //[m[41m [m
[32m+[m[32m            // tabPage2[m
[32m+[m[32m            //[m[41m [m
[32m+[m[32m            tabPage2.Controls.Add(button2);[m
[32m+[m[32m            tabPage2.Controls.Add(button11);[m
[32m+[m[32m            tabPage2.Controls.Add(button1);[m
[32m+[m[32m            tabPage2.Controls.Add(button7);[m
[32m+[m[32m            tabPage2.Controls.Add(button5);[m
[32m+[m[32m            tabPage2.Controls.Add(button4);[m
[32m+[m[32m            tabPage2.Controls.Add(button3);[m
[32m+[m[32m            tabPage2.Location = new Point(4, 24);[m
[32m+[m[32m            tabPage2.Name = "tabPage2";[m
[32m+[m[32m            tabPage2.Padding = new Padding(3);[m
[32m+[m[32m            tabPage2.Size = new Size(388, 170);[m
[32m+[m[32m            tabPage2.TabIndex = 1;[m
[32m+[m[32m            tabPage2.Text = "Actions";[m
[32m+[m[32m            tabPage2.UseVisualStyleBackColor = true;[m
[32m+[m[32m            //[m[41m [m
[32m+[m[32m            // button2[m
[32m+[m[32m            //[m[41m [m
[32m+[m[32m            button2.Location = new Point(6, 6);[m
[32m+[m[32m            button2.Name = "button2";[m
[32m+[m[32m            button2.Size = new Size(109, 42);[m
[32m+[m[32m            button2.TabIndex = 0;[m
[32m+[m[32m            button2.Text = "Ekran Görüntüsü Al";[m
[32m+[m[32m            button2.UseVisualStyleBackColor = true;[m
[32m+[m[32m            button2.Click += button2_Click_1;[m
[32m+[m[32m            //[m[41m [m
[32m+[m[32m            // button11[m
[32m+[m[32m            //[m[41m [m
[32m+[m[32m            button11.Location = new Point(271, 102);[m
[32m+[m[32m            button11.Name = "button11";[m
[32m+[m[32m            button11.Size = new Size(109, 35);[m
[32m+[m[32m            button11.TabIndex = 6;[m
[32m+[m[32m            button11.Text = "Skip Ad";[m
[32m+[m[32m            button11.UseVisualStyleBackColor = true;[m
[32m+[m[32m            button11.Click += button11_Click;[m
[32m+[m[32m            //[m[41m [m
             // button7[m
             // [m
[31m-            button7.Location = new Point(138, 51);[m
[32m+[m[32m            button7.Location = new Point(141, 54);[m
             button7.Name = "button7";[m
             button7.Size = new Size(109, 42);[m
             button7.TabIndex = 5;[m
[36m@@ -268,29 +294,29 @@[m
             button7.UseVisualStyleBackColor = true;[m
             button7.Click += button7_Click;[m
             // [m
[31m-            // button2[m
[32m+[m[32m            // button5[m
             // [m
[31m-            button2.Location = new Point(3, 3);[m
[31m-            button2.Name = "button2";[m
[31m-            button2.Size = new Size(109, 42);[m
[31m-            button2.TabIndex = 0;[m
[31m-            button2.Text = "Ekran Görüntüsü Al";[m
[31m-            button2.UseVisualStyleBackColor = true;[m
[31m-            button2.Click += button2_Click_1;[m
[32m+[m[32m            button5.Location = new Point(271, 54);[m
[32m+[m[32m            button5.Name = "button5";[m
[32m+[m[32m            button5.Size = new Size(109, 42);[m
[32m+[m[32m            button5.TabIndex = 3;[m
[32m+[m[32m            button5.Text = "Yeniden Başlat";[m
[32m+[m[32m            button5.UseVisualStyleBackColor = true;[m
[32m+[m[32m            button5.Click += button5_Click;[m
             // [m
[31m-            // button6[m
[32m+[m[32m            // button4[m
             // [m
[31m-            button6.Location = new Point(3, 99);[m
[31m-            button6.Name = "button6";[m
[31m-            button6.Size = new Size(109, 35);[m
[31m-            button6.TabIndex = 4;[m
[31m-            button6.Text = "dev mod";[m
[31m-            button6.UseVisualStyleBackColor = true;[m
[31m-            button6.Click += button6_Click;[m
[32m+[m[32m            button4.Location = new Point(6, 54);[m
[32m+[m[32m            button4.Name = "button4";[m
[32m+[m[32m            button4.Size = new Size(109, 42);[m
[32m+[m[32m            button4.TabIndex = 2;[m
[32m+[m[32m            button4.Text = "Run Monkey";[m
[32m+[m[32m            button4.UseVisualStyleBackColor = true;[m
[32m+[m[32m            button4.Click += button4_Click;[m
             // [m
             // button3[m
             // [m
[31m-            button3.Location = new Point(138, 3);[m
[32m+[m[32m            button3.Location = new Point(141, 6);[m
             button3.Name = "button3";[m
             button3.Size = new Size(109, 42);[m
             button3.TabIndex = 1;[m
[36m@@ -298,25 +324,36 @@[m
             button3.UseVisualStyleBackColor = true;[m
             button3.Click += button3_Click;[m
             // [m
[31m-            // button4[m
[32m+[m[32m            // tabPage1[m
             // [m
[31m-            button4.Location = new Point(3, 51);[m
[31m-            button4.Name = "button4";[m
[31m-            button4.Size = new Size(109, 42);[m
[31m-            button4.TabIndex = 2;[m
[31m-            button4.Text = "Run Monkey";[m
[31m-            button4.UseVisualStyleBackColor = true;[m
[31m-            button4.Click += button4_Click;[m
[32m+[m[32m            tabPage1.Controls.Add(button10);[m
[32m+[m[32m            tabPage1.Controls.Add(textBox1);[m
[32m+[m[32m            tabPage1.Location = new Point(4, 24);[m
[32m+[m[32m            tabPage1.Name = "tabPage1";[m
[32m+[m[32m            tabPage1.Padding = new Padding(3);[m
[32m+[m[32m            tabPage1.Size = new Size(388, 170);[m
[32m+[m[32m            tabPage1.TabIndex = 0;[m
[32m+[m[32m            tabPage1.Text = "Send Text";[m
[32m+[m[32m            tabPage1.UseVisualStyleBackColor = true;[m
             // [m
[31m-            // button5[m
[32m+[m[32m            // button10[m
             // [m
[31m-            button5.Location = new Point(268, 51);[m
[31m-            button5.Name = "button5";[m
[31m-            button5.Size = new Size(109, 42);[m
[31m-            button5.TabIndex = 3;[m
[31m-            button5.Text = "Yeniden Başlat";[m
[31m-            button5.UseVisualStyleBackColor = true;[m
[31m-            button5.Click += button5_Click;[m
[32m+[m[32m            button10.Location = new Point(307, 76);[m
[32m+[m[32m            button10.Name = "button10";[m
[32m+[m[32m            button10.Size = new Size(75, 23);[m
[32m+[m[32m            button10.TabIndex = 18;[m
[32m+[m[32m            button10.Text = "Send Text";[m
[32m+[m[32m            button10.UseVisualStyleBackColor = true;[m
[32m+[m[32m            button10.Click += button10_Click;[m
[32m+[m[32m            //[m[41m [m
[32m+[m[32m            // textBox1[m
[32m+[m[32m            //[m[41m [m
[32m+[m[32m            textBox1.Location = new Point(6, 6);[m
[32m+[m[32m            textBox1.Multiline = true;[m
[32m+[m[32m            textBox1.Name = "textBox1";[m
[32m+[m[32m            textBox1.Size = new Size(376, 64);[m
[32m+[m[32m            textBox1.TabIndex = 5;[m
[32m+[m[32m            textBox1.TextChanged += textBox1_TextChanged_1;[m
             // [m
             // iosPanel[m
             // [m
[36m@@ -357,22 +394,24 @@[m
             // toolStrip1[m
             // [m
             toolStrip1.Dock = DockStyle.Bottom;[m
[31m-            toolStrip1.Items.AddRange(new ToolStripItem[] { toolStripLabel1, toolStripLabel2, toolStripProgressBar1 });[m
[31m-            toolStrip1.Location = new Point(0, 882);[m
[32m+[m[32m            toolStrip1.Items.AddRange(new ToolStripItem[] { toolStripProgressBar1, toolStripLabel2, toolStripSeparator1, toolStripLabel1 });[m
[32m+[m[32m            toolStrip1.Location = new Point(0, 346);[m
             toolStrip1.Name = "toolStrip1";[m
             toolStrip1.RenderMode = ToolStripRenderMode.Professional;[m
[31m-            toolStrip1.Size = new Size(857, 25);[m
[32m+[m[32m            toolStrip1.Size = new Size(416, 25);[m
             toolStrip1.TabIndex = 3;[m
             toolStrip1.Text = "toolStrip1";[m
             // [m
             // toolStripLabel1[m
             // [m
[32m+[m[32m            toolStripLabel1.Alignment = ToolStripItemAlignment.Right;[m
             toolStripLabel1.Name = "toolStripLabel1";[m
[31m-            toolStripLabel1.Size = new Size(48, 22);[m
[31m-            toolStripLabel1.Text = "Status : ";[m
[32m+[m[32m            toolStripLabel1.Size = new Size(39, 22);[m
[32m+[m[32m            toolStripLabel1.Text = "Status";[m
             // [m
             // toolStripLabel2[m
             // [m
[32m+[m[32m            toolStripLabel2.Alignment = ToolStripItemAlignment.Right;[m
             toolStripLabel2.Name = "toolStripLabel2";[m
             toolStripLabel2.Size = new Size(39, 22);[m
             toolStripLabel2.Text = "Ready";[m
[36m@@ -380,7 +419,6 @@[m
             // [m
             // toolStripProgressBar1[m
             // [m
[31m-            toolStripProgressBar1.Alignment = ToolStripItemAlignment.Right;[m
             toolStripProgressBar1.AutoToolTip = true;[m
             toolStripProgressBar1.Name = "toolStripProgressBar1";[m
             toolStripProgressBar1.Size = new Size(100, 22);[m
[36m@@ -400,21 +438,17 @@[m
             kaydetToolStripMenuItem.Text = "Kaydet";[m
             kaydetToolStripMenuItem.Click += kaydetToolStripMenuItem_Click;[m
             // [m
[31m-            // button11[m
[32m+[m[32m            // toolStripSeparator1[m
             // [m
[31m-            button11.Location = new Point(268, 99);[m
[31m-         