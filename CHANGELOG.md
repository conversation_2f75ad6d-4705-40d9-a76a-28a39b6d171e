# Değişiklik Günlüğü (Changelog)

Bu dosya, Joygame QA Utility projesindeki tüm önemli değişiklikleri içerir.

Bu proje [Semantik Sürümleme](https://semver.org/lang/tr/) kurallarını takip etmektedir.

## [Yayınlanmamış]

### Eklenenler
- Kapsamlı dokümantasyon dosyaları eklendi
  - Ürün Gereksinimleri Dokümanı (PRD)
  - Kullanım Kılavuzu
  - Teknik Dokümantasyon
  - Kurulum Kılavuzu
  - Geliştirici Kılavuzu
  - API Referansı
  - Sorun Giderme Kılavuzu
  - Sürüm Notları
  - Katkıda Bulunma Kılavuzu
  - Sıkça Sorulan Sorular (SSS)
  - Terimler Sözlüğü
- README.md dosyası güncellendi ve genişletildi

## [2.5.0] - 2023-12-15

### Eklenenler
- TestRail entegrasyonu için yeni API özellikleri
- iOS cihazlar için gelişmiş log yakalama
- Ekran kaydı için NVIDIA ShadowPlay desteği
- Redmine entegrasyonu için yeni özellikler

### Değişenler
- WebView2 tabanlı kullanıcı arayüzü iyileştirildi
- APK yükleme süreci optimize edildi
- Log analiz algoritmaları geliştirildi

### Düzeltilenler
- Android cihaz bağlantı sorunları giderildi
- TestRail API çağrılarındaki bellek sızıntısı düzeltildi
- Ekran kaydı sırasında oluşan çökme sorunu çözüldü

## [2.4.0] - 2023-09-20

### Eklenenler
- Google API entegrasyonu için yeni özellikler
- Çoklu cihaz yönetimi için gelişmiş arayüz
- Otomatik log analizi ve raporlama

### Değişenler
- Veritabanı bağlantı yönetimi iyileştirildi
- Kullanıcı arayüzü performansı artırıldı
- APK analiz algoritmaları güncellendi

### Düzeltilenler
- iOS cihaz algılama sorunları giderildi
- Redmine API entegrasyonundaki hata düzeltildi
- Ekran görüntüsü alma sırasındaki bellek kullanımı optimize edildi

## [2.3.0] - 2023-06-10

### Eklenenler
- LDAP kimlik doğrulama desteği
- Gelişmiş log filtreleme seçenekleri
- Yeni TestRail rapor şablonları

### Değişenler
- AutoUpdater bileşeni yeniden tasarlandı
- Veritabanı şeması güncellendi
- Kullanıcı arayüzü daha modern bir görünüme kavuşturuldu

### Düzeltilenler
- Çoklu cihaz bağlantısı sırasındaki kararlılık sorunları giderildi
- Google API kimlik doğrulama hatası düzeltildi
- APK yükleme sırasında oluşan dosya erişim hatası çözüldü

## [2.2.0] - 2023-03-05

### Eklenenler
- Redmine entegrasyonu
- iOS cihazlar için gelişmiş yönetim özellikleri
- Ekran kaydı için yeni kodek seçenekleri

### Değişenler
- ADB komut yürütme sistemi yeniden yazıldı
- Log analiz algoritmaları iyileştirildi
- Kullanıcı arayüzü daha sezgisel hale getirildi

### Düzeltilenler
- Android cihaz bağlantı sorunları giderildi
- TestRail API çağrılarındaki zamanlama hatası düzeltildi
- Ekran görüntüsü alma sırasındaki çözünürlük sorunu çözüldü

## [2.1.0] - 2022-12-10

### Eklenenler
- TestRail entegrasyonu
- Gelişmiş log analiz araçları
- Çoklu dil desteği (Türkçe ve İngilizce)

### Değişenler
- Kullanıcı arayüzü yeniden tasarlandı
- APK yükleme süreci iyileştirildi
- Veritabanı yapısı optimize edildi

### Düzeltilenler
- iOS cihaz algılama sorunları giderildi
- Log dosyası oluşturma hatası düzeltildi
- Ekran kaydı sırasındaki ses senkronizasyon sorunu çözüldü

## [2.0.0] - 2022-09-15

### Eklenenler
- WebView2 tabanlı yeni kullanıcı arayüzü
- iOS cihaz desteği
- Google API entegrasyonu
- MySQL veritabanı desteği

### Değişenler
- Mimari tamamen yeniden tasarlandı
- ADB entegrasyonu geliştirildi
- Ekran kaydı ve görüntü alma sistemi yenilendi

### Düzeltilenler
- Çeşitli kararlılık sorunları giderildi
- Bellek kullanımı optimize edildi
- Dosya yönetimi hataları düzeltildi

## [1.5.0] - 2022-06-20

### Eklenenler
- Otomatik güncelleme sistemi (AutoUpdater)
- APK analiz özellikleri
- Gelişmiş log filtreleme

### Değişenler
- Kullanıcı arayüzü iyileştirildi
- ADB komut yürütme sistemi güncellendi
- Dosya yönetimi sistemi yenilendi

### Düzeltilenler
- Android cihaz bağlantı sorunları giderildi
- Log dosyası oluşturma hatası düzeltildi
- Ekran görüntüsü alma sırasındaki çözünürlük sorunu çözüldü

## [1.0.0] - 2022-03-10

### Eklenenler
- İlk kararlı sürüm
- Android cihaz yönetimi
- APK yükleme ve kaldırma
- Temel log görüntüleme
- Ekran görüntüsü alma
- Basit ekran kaydı