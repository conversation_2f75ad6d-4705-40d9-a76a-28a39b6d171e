---
name: <PERSON><PERSON>
about: Bir hata bildirmek için bu şablonu kullanın
title: '[HATA] '
labels: 'bug'
assignees: ''

---

## <PERSON><PERSON><PERSON><PERSON> Açıklaması
Hatanın net ve özlü bir açıklamasını yapın.

## Yeniden Oluşturma Adımları
Davranışı yeniden oluşturmak için adımlar:
1. '...'
2. '....'
3. '...'
4. Hatayı görün

## Beklenen Davranış
Ne olmasını beklediğinizin net ve özlü bir açıklaması.

## Ekran Görüntüleri
Uygunsa, sorununuzu açıklamaya yardımcı olması için ekran görüntüleri ekleyin.

## Ortam
 - İşletim Sistemi: [örn. Windows 10]
 - Joygame QA Utility Sürümü: [örn. 2.5.0]
 - .NET Framework Sürümü: [örn. 4.7.2]
 - WebView2 Runtime Sürümü: [örn. 94.0.992.31]

## <PERSON><PERSON> Cihaz (eğer mobil cihazla ilgili bir sorunsa)
 - Cihaz: [örn. Samsung Galaxy S21]
 - İşletim Sistemi: [örn. Android 12]
 - Sürüm: [örn. One UI 4.0]

## Ek Bağlam
Sorun hakkında başka herhangi bir bağlam buraya ekleyin.

## Log Dosyaları
Varsa, ilgili log dosyalarını ekleyin veya içeriğini buraya yapıştırın.

```
Log içeriğini buraya yapıştırın
```

## Olası Çözüm
Sorunu nasıl çözebileceğinize dair bir fikriniz varsa, lütfen burada belirtin.