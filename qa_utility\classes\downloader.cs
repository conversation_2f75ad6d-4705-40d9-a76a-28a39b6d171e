﻿using System.Configuration;
using System.IO;
using System.Net;

namespace QA_Utility.classes
{
    internal class downloader
    {

        public void downloadLatestVersion(string server)
        {
            string ftpServerIP = server;
            string ftpUserName = ConfigurationManager.AppSettings["FtpUsername"];
            string ftpPassword = ConfigurationManager.AppSettings["FtpPassword"];
            FtpWebRequest request = (FtpWebRequest)WebRequest.Create(ftpServerIP);
            request.Method = WebRequestMethods.Ftp.ListDirectory;
            request.Credentials = new NetworkCredential(ftpUserName, ftpPassword);
            FtpWebResponse response = (FtpWebResponse)request.GetResponse();
            Stream responseStream = response.GetResponseStream();
            StreamReader reader = new StreamReader(responseStream);
            while (!reader.EndOfStream)
            {
                string fileName = reader.ReadLine();
                if (fileName != "." && fileName != "..")
                {
                    string fileUrl = ftpServerIP + fileName;
                    WebClient client = new WebClient();
                    client.Credentials = new NetworkCredential(ftpUserName, ftpPassword);
                    client.DownloadFile(fileUrl, fileName);
                }
            }
        }
    }
}
