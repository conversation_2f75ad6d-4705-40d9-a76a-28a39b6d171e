# Joygame QA Utility - <PERSON><PERSON>lum Kılavuzu

## İçindekiler

1. [<PERSON><PERSON><PERSON> Gereksinimleri](#1-sistem-gere<PERSON><PERSON>mleri)
2. [<PERSON><PERSON>](#2-ön-ko<PERSON><PERSON><PERSON>)
3. [<PERSON><PERSON><PERSON>](#3-k<PERSON><PERSON><PERSON>ad<PERSON><PERSON>)
4. [<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>](#4-yapılandırma)
5. [<PERSON><PERSON>](#5-sorun-giderme)
6. [<PERSON><PERSON><PERSON><PERSON><PERSON>](#6-günce<PERSON><PERSON>)
7. [Kaldırma](#7-kaldırma)

## 1. Sistem Gereksinimleri

### 1.1 Donanım Gereksinimleri

- **İşlemci**: Intel Core i3 veya dengi (2.0 GHz veya üzeri)
- **RAM**: En az 4 GB (8 GB önerilir)
- **Disk Alanı**: En az 500 MB boş alan
- **Ekran Çözünürlüğü**: 1280x720 veya üzeri
- **USB Bağlantı Noktaları**: <PERSON><PERSON> cihaz bağlantısı için en az 1 USB portu

### 1.2 Yazılım Gereksinimleri

- **İşletim Sistemi**: Windows 10 veya üzeri (64-bit önerilir)
- **.NET Framework**: 4.7.2 veya üzeri
- **WebView2 Runtime**: En son sürüm
- **Visual C++ Redistributable**: 2015-2019 (x86 ve x64)
- **MySQL Connector**: 8.0 veya üzeri (veritabanı bağlantısı için)

## 2. Ön Koşullar

### 2.1 .NET Framework Kurulumu

1. [Microsoft .NET Framework indirme sayfasına](https://dotnet.microsoft.com/download/dotnet-framework) gidin.
2. .NET Framework 4.7.2 veya üzeri sürümü indirin.
3. İndirilen kurulum dosyasını çalıştırın ve ekrandaki talimatları izleyin.

### 2.2 WebView2 Runtime Kurulumu

1. [Microsoft Edge WebView2 indirme sayfasına](https://developer.microsoft.com/en-us/microsoft-edge/webview2/) gidin.
2. "Evergreen Standalone Installer" seçeneğini seçin ve indirin.
3. İndirilen kurulum dosyasını çalıştırın ve ekrandaki talimatları izleyin.

### 2.3 Visual C++ Redistributable Kurulumu

1. [Microsoft Visual C++ Redistributable indirme sayfasına](https://support.microsoft.com/en-us/help/2977003/the-latest-supported-visual-c-downloads) gidin.
2. "Visual Studio 2015, 2017 ve 2019" için x86 ve x64 sürümlerini indirin.
3. Her iki kurulum dosyasını da çalıştırın ve ekrandaki talimatları izleyin.

### 2.4 MySQL Connector Kurulumu

1. [MySQL Connector/NET indirme sayfasına](https://dev.mysql.com/downloads/connector/net/) gidin.
2. En son sürümü indirin.
3. Kurulum dosyasını çalıştırın ve ekrandaki talimatları izleyin.

## 3. Kurulum Adımları

### 3.1 Kaynak Koddan Kurulum

#### 3.1.1 Kaynak Kodu İndirme

1. Git istemcisini kullanarak repoyu klonlayın:
   ```
   git clone https://github.com/joygame/joygame_utility.git
   ```

2. Veya GitHub'dan ZIP olarak indirin ve istediğiniz bir konuma çıkartın.

#### 3.1.2 Projeyi Derleme

1. Visual Studio 2019 veya üzeri sürümü açın.
2. `QA Utility.sln` dosyasını açın.
3. NuGet Paket Yöneticisi'ni kullanarak eksik paketleri yükleyin:
   - Solution Explorer'da projeye sağ tıklayın.
   - "Restore NuGet Packages" seçeneğini seçin.
4. Projeyi derleyin:
   - Build menüsünden "Build Solution" seçeneğini seçin veya F6 tuşuna basın.

### 3.2 Hazır Kurulum Paketi ile Kurulum

1. Hazır kurulum paketini indirin.
2. İndirilen `.exe` veya `.msi` dosyasını çalıştırın.
3. Ekrandaki kurulum sihirbazı adımlarını takip edin:
   - Lisans sözleşmesini kabul edin.
   - Kurulum konumunu seçin (varsayılan: `C:\Program Files\Joygame\QA Utility`).
   - Başlat menüsü klasörünü seçin.
   - Kurulumu başlatın.

### 3.3 Mobil Cihaz Araçlarının Kurulumu

#### 3.3.1 Android Araçları

Android cihazlarla çalışmak için gerekli araçlar uygulama içinde `platform-tools` klasöründe bulunmaktadır. Ek kurulum gerekmez.

#### 3.3.2 iOS Araçları

iOS cihazlarla çalışmak için gerekli araçlar uygulama içinde `ioslog` klasöründe bulunmaktadır. Ek kurulum gerekmez.

## 4. Yapılandırma

### 4.1 Yapılandırma Dosyaları

Uygulama, aşağıdaki yapılandırma dosyalarını kullanır:

- **App.config**: Ana uygulama yapılandırması
- **log4net.config**: Loglama yapılandırması

### 4.2 Veritabanı Yapılandırması

1. MySQL veritabanı sunucusuna erişim bilgilerinizi `App.config` dosyasında güncelleyin:

   ```xml
   <connectionStrings>
     <add name="DbConnectionString" connectionString="Server=YOUR_SERVER;database=YOUR_DATABASE;User ID=YOUR_USERNAME;Password=YOUR_PASSWORD;pooling=false;SslMode=none;charset=utf8mb4;" />
   </connectionStrings>
   ```

2. Veritabanı şemasını oluşturmak için SQL betiklerini çalıştırın (varsa).

### 4.3 API Yapılandırması

#### 4.3.1 TestRail API

1. TestRail API erişim bilgilerinizi `App.config` dosyasında güncelleyin:

   ```xml
   <appSettings>
     <add key="TestRailUrl" value="https://your-instance.testrail.io/" />
     <add key="TestRailUsername" value="your-username" />
     <add key="TestRailPassword" value="your-api-key" />
   </appSettings>
   ```

#### 4.3.2 Redmine API

1. Redmine API erişim bilgilerinizi `App.config` dosyasında güncelleyin:

   ```xml
   <appSettings>
     <add key="RedmineUrl" value="https://your-redmine-instance.com/" />
     <add key="RedmineApiKey" value="your-api-key" />
   </appSettings>
   ```

#### 4.3.3 Google API

1. Google API kimlik bilgilerinizi `client_secret.json` dosyasına yerleştirin.

### 4.4 FTP Yapılandırması

1. FTP sunucu bilgilerinizi `App.config` dosyasında güncelleyin:

   ```xml
   <appSettings>
     <add key="FtpServer" value="your-ftp-server" />
     <add key="FtpUsername" value="your-username" />
     <add key="FtpPassword" value="your-password" />
   </appSettings>
   ```

## 5. Sorun Giderme

### 5.1 Genel Sorunlar

#### 5.1.1 Uygulama Başlatma Sorunları

**Sorun**: Uygulama başlatılamıyor veya hata veriyor.

**Çözüm**:
1. .NET Framework'ün doğru sürümünün yüklü olduğunu kontrol edin.
2. WebView2 Runtime'ın yüklü olduğunu kontrol edin.
3. Log dosyalarını kontrol edin (`logs` klasöründe).
4. Uygulamayı yönetici olarak çalıştırmayı deneyin.

#### 5.1.2 Veritabanı Bağlantı Sorunları

**Sorun**: Veritabanına bağlanılamıyor.

**Çözüm**:
1. MySQL sunucusunun çalıştığını kontrol edin.
2. Bağlantı dizesindeki bilgilerin doğru olduğunu kontrol edin.
3. Güvenlik duvarı ayarlarını kontrol edin.
4. MySQL kullanıcısının gerekli izinlere sahip olduğunu kontrol edin.

### 5.2 Mobil Cihaz Sorunları

#### 5.2.1 Android Cihaz Algılama Sorunları

**Sorun**: Android cihaz algılanmıyor.

**Çözüm**:
1. USB hata ayıklama modunun etkin olduğunu kontrol edin.
2. USB kablosunu değiştirmeyi deneyin.
3. Cihaz sürücülerinin yüklü olduğunu kontrol edin.
4. ADB'yi yeniden başlatın: `adb kill-server` ve `adb start-server` komutlarını çalıştırın.

#### 5.2.2 iOS Cihaz Algılama Sorunları

**Sorun**: iOS cihaz algılanmıyor.

**Çözüm**:
1. iTunes'un yüklü olduğunu kontrol edin.
2. USB kablosunu değiştirmeyi deneyin.
3. Cihazın bilgisayara güvendiğini kontrol edin (güven uyarısını onaylayın).
4. iOS cihaz sürücülerini yeniden yükleyin.

## 6. Güncelleme

### 6.1 Otomatik Güncelleme

Uygulama, AutoUpdater bileşeni sayesinde otomatik olarak güncellenebilir:

1. Uygulama başlatıldığında, güncellemeler otomatik olarak kontrol edilir.
2. Yeni bir güncelleme varsa, kullanıcıya bildirilir.
3. Kullanıcı onaylarsa, güncelleme indirilir ve uygulanır.

### 6.2 Manuel Güncelleme

1. En son sürümü GitHub'dan indirin.
2. Mevcut kurulumu kaldırın (isteğe bağlı).
3. Yeni sürümü kurun.

## 7. Kaldırma

### 7.1 Kurulum Paketi ile Yüklenen Uygulamayı Kaldırma

1. Windows Denetim Masası'nı açın.
2. "Program Ekle veya Kaldır" veya "Programlar ve Özellikler" seçeneğini açın.
3. Listeden "Joygame QA Utility"yi bulun ve "Kaldır" düğmesine tıklayın.
4. Ekrandaki talimatları izleyin.

### 7.2 Manuel Olarak Yüklenen Uygulamayı Kaldırma

1. Uygulama klasörünü silin.
2. (İsteğe bağlı) Aşağıdaki konumlardaki uygulama verilerini temizleyin:
   - `%APPDATA%\Joygame\QA Utility`
   - `%LOCALAPPDATA%\Joygame\QA Utility`

---

**Not**: Bu kurulum kılavuzu, Joygame QA Utility'nin temel kurulum ve yapılandırma adımlarını kapsar. Özel kurulum senaryoları için geliştirme ekibiyle iletişime geçin.