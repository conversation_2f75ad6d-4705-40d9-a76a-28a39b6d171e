# Katkıda Bulunma

Joygame QA Utility projesine katkıda bulunmak istediğiniz için teşekkür ederiz! Katkıda bulunma sürecini kolaylaştırmak için lütfen aşağıdaki adımları izleyin.

## Detaylı Katkıda Bulunma Kılavuzu

Detaylı katkıda bulunma kılavuzu için lütfen [Docs/contributing.md](./Docs/contributing.md) dosyasına bakın. Bu dosya, aşağıdaki konularda kapsamlı bilgiler içerir:

- Geliştirme ortamının kurulumu
- Kod standartları ve stil kılavuzları
- <PERSON><PERSON><PERSON> stratejisi (GitFlow)
- Commit mesajı kuralları
- Pull request süreci
- Test gereksinimleri
- Dokümantasyon standartları
- Sürüm oluşturma ve etiketleme

## Hızlı Başlangıç

1. Projeyi fork edin
2. <PERSON><PERSON> bir dal o<PERSON> (`git checkout -b feature/amazing-feature`)
3. Değişikliklerini<PERSON> commit edin (`git commit -m 'feat: Add some amazing feature'`)
4. <PERSON>ınızı push edin (`git push origin feature/amazing-feature`)
5. Bir Pull Request açın

## Davranış Kuralları

Bu proje, [Davranış Kuralları](./CODE_OF_CONDUCT.md)'na tabidir. Projeye katılarak, bu kurallara uymayı kabul etmiş olursunuz.

## Sorunlar ve Özellik İstekleri

Yeni bir sorun veya özellik isteği oluşturmadan önce, lütfen benzer bir sorun veya isteğin zaten var olup olmadığını kontrol edin. Sorun veya özellik isteği oluştururken, mümkün olduğunca fazla bilgi sağlayın.

## Lisans

Bu projeye katkıda bulunarak, katkılarınızın projenin [MIT Lisansı](./LICENSE) altında lisanslanacağını kabul etmiş olursunuz.