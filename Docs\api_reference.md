# Joygame QA Utility - API Referans Dokümanı

## İçindekiler

1. [<PERSON><PERSON><PERSON>](#1-giri<PERSON>)
2. [TestRail API](#2-testrail-api)
3. [Redmine API](#3-redmine-api)
4. [Google API](#4-google-api)
5. [LDAP Entegrasyonu](#5-ldap-entegrasyonu)
6. [ADB (Android Debug Bridge)](#6-adb-android-debug-bridge)
7. [iOS Cihaz Yönetimi](#7-ios-cihaz-yönetimi)
8. [MySQL Veritabanı](#8-mysql-veritabanı)
9. [FTP Entegrasyonu](#9-ftp-entegrasyonu)
10. [NVIDIA ShadowPlay](#10-nvidia-shadowplay)

## 1. <PERSON><PERSON><PERSON>, Joygame QA Utility uygulamasının kullandığı API'ler ve entegrasyonlar hakkında teknik bilgiler sağlar. Her API için kimlik doğru<PERSON>a yö<PERSON>, endpoint'le<PERSON>, yaygın kullanım durumları ve örnek kod parçaları içerir.

## 2. TestRail API

### 2.1 Genel Bakış

TestRail API, test durumlarını, test sonuçlarını ve test planlarını programatik olarak yönetmek için kullanılır. Joygame QA Utility, test sonuçlarını otomatik olarak TestRail'e göndermek ve test durumlarını almak için bu API'yi kullanır.

### 2.2 Kimlik Doğrulama

**Kimlik Doğrulama Yöntemi**: Basic Authentication (API Anahtarı)

**Yapılandırma**:
```xml
<appSettings>
  <add key="TestRailUrl" value="https://your-instance.testrail.io/" />
  <add key="TestRailUsername" value="your-username" />
  <add key="TestRailPassword" value="your-api-key" />
</appSettings>
```

### 2.3 Kullanılan Kütüphane

**Kütüphane**: Redmine.Net.Api

**NuGet Paketi**: `TestRail`

### 2.4 Temel Endpoint'ler

| Endpoint | Açıklama |
|----------|----------|
| `/get_case/:case_id` | Belirli bir test durumunu alır |
| `/get_cases/:project_id` | Bir projedeki tüm test durumlarını alır |
| `/add_result/:test_id` | Bir test için sonuç ekler |
| `/get_runs/:project_id` | Bir projedeki test çalıştırmalarını alır |

### 2.5 Örnek Kullanım

```csharp
// TestRail istemcisini başlatma
var client = new TestRailClient(testRailUrl, testRailUsername, testRailPassword);

// Test durumlarını alma
var testCases = client.GetCases(projectId, suiteId);

// Test sonucu gönderme
var result = new TestResult
{
    StatusId = 1, // Passed
    Comment = "Test başarıyla tamamlandı",
    ElapsedTimeInSeconds = 30
};

client.AddResult(testId, result);
```

## 3. Redmine API

### 3.1 Genel Bakış

Redmine API, sorunları, projeleri ve kullanıcıları programatik olarak yönetmek için kullanılır. Joygame QA Utility, hata raporlarını otomatik olarak Redmine'a göndermek ve mevcut sorunları görüntülemek için bu API'yi kullanır.

### 3.2 Kimlik Doğrulama

**Kimlik Doğrulama Yöntemi**: API Anahtarı

**Yapılandırma**:
```xml
<appSettings>
  <add key="RedmineUrl" value="https://your-redmine-instance.com/" />
  <add key="RedmineApiKey" value="your-api-key" />
</appSettings>
```

### 3.3 Kullanılan Kütüphane

**Kütüphane**: redmine-net-api

**NuGet Paketi**: `redmine-api`

### 3.4 Temel Endpoint'ler

| Endpoint | Açıklama |
|----------|----------|
| `/issues.json` | Sorunları listeler veya yeni sorun oluşturur |
| `/issues/:id.json` | Belirli bir sorunu alır, günceller veya siler |
| `/projects.json` | Projeleri listeler veya yeni proje oluşturur |
| `/users.json` | Kullanıcıları listeler |

### 3.5 Örnek Kullanım

```csharp
// Redmine yöneticisini başlatma
var manager = new RedmineManager(redmineUrl, redmineApiKey);

// Yeni bir sorun oluşturma
var issue = new Issue
{
    Subject = "Uygulama çöküyor",
    Description = "Uygulama başlatıldıktan sonra çöküyor",
    ProjectId = 1,
    TrackerId = 1, // Hata
    PriorityId = 2 // Normal
};

var createdIssue = manager.CreateObject(issue);

// Sorunları listeleme
var issues = manager.GetObjects<Issue>(new NameValueCollection
{
    { "project_id", "1" },
    { "status_id", "open" }
});
```

## 4. Google API

### 4.1 Genel Bakış

Google API, Google hizmetlerine programatik erişim sağlar. Joygame QA Utility, Google Drive, Google Sheets ve Google Play Store entegrasyonları için bu API'yi kullanır.

### 4.2 Kimlik Doğrulama

**Kimlik Doğrulama Yöntemi**: OAuth 2.0

**Yapılandırma**:
- `client_secret.json` dosyası (Google Cloud Console'dan alınır)

### 4.3 Kullanılan Kütüphane

**Kütüphane**: Google.Apis

**NuGet Paketleri**:
- `Google.Apis.Drive.v3`
- `Google.Apis.Sheets.v4`
- `Google.Apis.AndroidPublisher.v3`

### 4.4 Temel Servisler

| Servis | Açıklama |
|--------|----------|
| Drive API | Dosya yükleme, indirme ve yönetme |
| Sheets API | Elektronik tablo verilerini okuma ve yazma |
| Android Publisher API | Google Play Store uygulamalarını yönetme |

### 4.5 Örnek Kullanım

```csharp
// Google Drive servisini başlatma
var credential = GoogleWebAuthorizationBroker.AuthorizeAsync(
    new ClientSecrets
    {
        ClientId = "your-client-id",
        ClientSecret = "your-client-secret"
    },
    new[] { DriveService.Scope.Drive },
    "user",
    CancellationToken.None).Result;

var service = new DriveService(new BaseClientService.Initializer
{
    HttpClientInitializer = credential,
    ApplicationName = "Joygame QA Utility"
});

// Dosya yükleme
var fileMetadata = new Google.Apis.Drive.v3.Data.File
{
    Name = "screenshot.png",
    Parents = new List<string> { folderId }
};

FilesResource.CreateMediaUpload request;
using (var stream = new FileStream(filePath, FileMode.Open))
{
    request = service.Files.Create(fileMetadata, stream, "image/png");
    request.Upload();
}

var file = request.ResponseBody;
```

## 5. LDAP Entegrasyonu

### 5.1 Genel Bakış

LDAP (Lightweight Directory Access Protocol) entegrasyonu, kullanıcı kimlik doğrulaması için kullanılır. Joygame QA Utility, kullanıcıların şirket kimlik bilgileriyle oturum açmasını sağlamak için LDAP'ı kullanır.

### 5.2 Yapılandırma

```xml
<appSettings>
  <add key="LdapServer" value="ldap://your-ldap-server:389" />
  <add key="LdapDomain" value="your-domain.com" />
  <add key="LdapBaseDn" value="DC=your-domain,DC=com" />
</appSettings>
```

### 5.3 Kullanılan Kütüphane

**Kütüphane**: System.DirectoryServices

### 5.4 Örnek Kullanım

```csharp
// LDAP kimlik doğrulayıcısını başlatma
var authenticator = new LdapAuthenticator(ldapServer, ldapDomain, ldapBaseDn);

// Kullanıcı kimlik doğrulaması
bool isAuthenticated = authenticator.Authenticate(username, password);

if (isAuthenticated)
{
    // Kullanıcı bilgilerini alma
    var userInfo = authenticator.GetUserInfo(username);
    string displayName = userInfo.DisplayName;
    string email = userInfo.Email;
}
```

## 6. ADB (Android Debug Bridge)

### 6.1 Genel Bakış

ADB, Android cihazlarla iletişim kurmak için kullanılan bir komut satırı aracıdır. Joygame QA Utility, Android cihazları yönetmek, uygulamaları yüklemek, kaldırmak ve log'ları almak için ADB'yi kullanır.

### 6.2 Konum

ADB araçları, uygulama içinde `platform-tools` klasöründe bulunur.

### 6.3 Temel Komutlar

| Komut | Açıklama |
|-------|----------|
| `adb devices` | Bağlı cihazları listeler |
| `adb install <apk>` | APK yükler |
| `adb uninstall <package>` | Uygulamayı kaldırır |
| `adb shell am start -n <package>/<activity>` | Uygulamayı başlatır |
| `adb shell am force-stop <package>` | Uygulamayı durdurur |
| `adb logcat` | Log'ları görüntüler |
| `adb pull <remote> <local>` | Cihazdan dosya alır |
| `adb push <local> <remote>` | Cihaza dosya gönderir |
| `adb shell screencap` | Ekran görüntüsü alır |
| `adb shell screenrecord` | Ekran kaydı yapar |

### 6.4 Örnek Kullanım

```csharp
// ADB komutunu çalıştırma
private string ExecuteAdbCommand(string command)
{
    var process = new Process
    {
        StartInfo = new ProcessStartInfo
        {
            FileName = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "platform-tools", "adb.exe"),
            Arguments = command,
            UseShellExecute = false,
            RedirectStandardOutput = true,
            CreateNoWindow = true
        }
    };

    process.Start();
    string output = process.StandardOutput.ReadToEnd();
    process.WaitForExit();

    return output;
}

// Bağlı cihazları alma
string devices = ExecuteAdbCommand("devices");

// APK yükleme
string installResult = ExecuteAdbCommand($"install -r \"{apkPath}\"");

// Uygulamayı başlatma
string startResult = ExecuteAdbCommand($"shell am start -n {packageName}/{activityName}");
```

## 7. iOS Cihaz Yönetimi

### 7.1 Genel Bakış

iOS cihaz yönetimi, libimobiledevice kütüphanesi ve ilgili araçlar kullanılarak gerçekleştirilir. Joygame QA Utility, iOS cihazları yönetmek, uygulamaları yüklemek, kaldırmak ve log'ları almak için bu araçları kullanır.

### 7.2 Konum

iOS araçları, uygulama içinde `ioslog` klasöründe bulunur.

### 7.3 Temel Araçlar

| Araç | Açıklama |
|------|----------|
| `idevice_id` | Bağlı cihazların UDID'lerini listeler |
| `ideviceinstaller` | Uygulamaları yükler ve kaldırır |
| `idevicescreenshot` | Ekran görüntüsü alır |
| `idevicesyslog` | Sistem log'larını görüntüler |
| `ideviceinfo` | Cihaz bilgilerini görüntüler |

### 7.4 Örnek Kullanım

```csharp
// iOS komutunu çalıştırma
private string ExecuteIosCommand(string tool, string arguments)
{
    var process = new Process
    {
        StartInfo = new ProcessStartInfo
        {
            FileName = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "ioslog", tool),
            Arguments = arguments,
            UseShellExecute = false,
            RedirectStandardOutput = true,
            CreateNoWindow = true
        }
    };

    process.Start();
    string output = process.StandardOutput.ReadToEnd();
    process.WaitForExit();

    return output;
}

// Bağlı cihazları alma
string devices = ExecuteIosCommand("idevice_id", "-l");

// Uygulama listesini alma
string apps = ExecuteIosCommand("ideviceinstaller", "-l");

// Ekran görüntüsü alma
string screenshotResult = ExecuteIosCommand("idevicescreenshot", $"\"{outputPath}\"");
```

## 8. MySQL Veritabanı

### 8.1 Genel Bakış

MySQL veritabanı, uygulama verilerini depolamak için kullanılır. Joygame QA Utility, kullanıcı ayarlarını, test sonuçlarını ve diğer verileri saklamak için MySQL'i kullanır.

### 8.2 Bağlantı Dizesi

```xml
<connectionStrings>
  <add name="DbConnectionString" connectionString="Server=your-server;database=your-database;User ID=your-username;Password=your-password;pooling=false;SslMode=none;charset=utf8mb4;" />
</connectionStrings>
```

### 8.3 Kullanılan Kütüphane

**Kütüphane**: MySql.Data

**NuGet Paketi**: `MySql.Data`

### 8.4 Örnek Kullanım

```csharp
// Veritabanı bağlantısı oluşturma
string connectionString = ConfigurationManager.ConnectionStrings["DbConnectionString"].ConnectionString;
using (var connection = new MySqlConnection(connectionString))
{
    connection.Open();

    // Veri okuma
    string query = "SELECT * FROM users WHERE username = @username";
    using (var command = new MySqlCommand(query, connection))
    {
        command.Parameters.AddWithValue("@username", username);
        using (var reader = command.ExecuteReader())
        {
            if (reader.Read())
            {
                string displayName = reader.GetString("display_name");
                string email = reader.GetString("email");
            }
        }
    }

    // Veri yazma
    string insertQuery = "INSERT INTO test_results (test_id, result, timestamp) VALUES (@testId, @result, @timestamp)";
    using (var command = new MySqlCommand(insertQuery, connection))
    {
        command.Parameters.AddWithValue("@testId", testId);
        command.Parameters.AddWithValue("@result", result);
        command.Parameters.AddWithValue("@timestamp", DateTime.Now);
        command.ExecuteNonQuery();
    }
}
```

## 9. FTP Entegrasyonu

### 9.1 Genel Bakış

FTP entegrasyonu, dosyaları uzak sunucuya yüklemek ve indirmek için kullanılır. Joygame QA Utility'nin AutoUpdater bileşeni, güncellemeleri FTP sunucusundan indirmek için bu entegrasyonu kullanır.

### 9.2 Yapılandırma

```xml
<appSettings>
  <add key="FtpServer" value="ftp://your-ftp-server" />
  <add key="FtpUsername" value="your-username" />
  <add key="FtpPassword" value="your-password" />
  <add key="FtpUpdateFolder" value="/updates" />
</appSettings>
```

### 9.3 Kullanılan Kütüphane

**Kütüphane**: System.Net

### 9.4 Örnek Kullanım

```csharp
// FTP'den dosya indirme
private void DownloadFileFromFtp(string remoteFile, string localFile)
{
    string ftpServer = ConfigurationManager.AppSettings["FtpServer"];
    string ftpUsername = ConfigurationManager.AppSettings["FtpUsername"];
    string ftpPassword = ConfigurationManager.AppSettings["FtpPassword"];
    string ftpUpdateFolder = ConfigurationManager.AppSettings["FtpUpdateFolder"];

    string remoteFilePath = $"{ftpServer}{ftpUpdateFolder}/{remoteFile}";

    try
    {
        var request = (FtpWebRequest)WebRequest.Create(remoteFilePath);
        request.Method = WebRequestMethods.Ftp.DownloadFile;
        request.Credentials = new NetworkCredential(ftpUsername, ftpPassword);

        using (var response = (FtpWebResponse)request.GetResponse())
        using (var responseStream = response.GetResponseStream())
        using (var fileStream = new FileStream(localFile, FileMode.Create))
        {
            byte[] buffer = new byte[8192];
            int bytesRead;
            while ((bytesRead = responseStream.Read(buffer, 0, buffer.Length)) > 0)
            {
                fileStream.Write(buffer, 0, bytesRead);
            }
        }
    }
    catch (Exception ex)
    {
        // Hata işleme
        logger.Error($"FTP indirme hatası: {ex.Message}");
    }
}

// FTP'ye dosya yükleme
private void UploadFileToFtp(string localFile, string remoteFile)
{
    string ftpServer = ConfigurationManager.AppSettings["FtpServer"];
    string ftpUsername = ConfigurationManager.AppSettings["FtpUsername"];
    string ftpPassword = ConfigurationManager.AppSettings["FtpPassword"];
    string ftpUpdateFolder = ConfigurationManager.AppSettings["FtpUpdateFolder"];

    string remoteFilePath = $"{ftpServer}{ftpUpdateFolder}/{remoteFile}";

    try
    {
        var request = (FtpWebRequest)WebRequest.Create(remoteFilePath);
        request.Method = WebRequestMethods.Ftp.UploadFile;
        request.Credentials = new NetworkCredential(ftpUsername, ftpPassword);

        using (var fileStream = new FileStream(localFile, FileMode.Open))
        using (var requestStream = request.GetRequestStream())
        {
            byte[] buffer = new byte[8192];
            int bytesRead;
            while ((bytesRead = fileStream.Read(buffer, 0, buffer.Length)) > 0)
            {
                requestStream.Write(buffer, 0, bytesRead);
            }
        }

        using (var response = (FtpWebResponse)request.GetResponse())
        {
            // İşlem tamamlandı
        }
    }
    catch (Exception ex)
    {
        // Hata işleme
        logger.Error($"FTP yükleme hatası: {ex.Message}");
    }
}
```

## 10. NVIDIA ShadowPlay

### 10.1 Genel Bakış

NVIDIA ShadowPlay, ekran kaydı ve ekran görüntüsü almak için kullanılan bir teknolojidir. Joygame QA Utility, yüksek kaliteli ekran kayıtları ve ekran görüntüleri almak için NVIDIA ShadowPlay'i kullanır.

### 10.2 Kullanılan Kütüphane

**Kütüphane**: Özel uygulama içi sınıf (`NvidiaShadowPlayHelper.cs`)

### 10.3 Örnek Kullanım

```csharp
// ShadowPlay yardımcısını başlatma
var shadowPlayHelper = new NvidiaShadowPlayHelper();

// Ekran kaydını başlatma
bool isRecordingStarted = shadowPlayHelper.StartRecording();

// Ekran kaydını durdurma
string recordingFilePath = shadowPlayHelper.StopRecording();

// Ekran görüntüsü alma
string screenshotFilePath = shadowPlayHelper.TakeScreenshot();
```

---

**Not**: Bu API referans dokümanı, Joygame QA Utility'nin kullandığı temel API'leri ve entegrasyonları kapsar. Daha fazla bilgi için kaynak kodunu inceleyin veya geliştirme ekibiyle iletişime geçin.