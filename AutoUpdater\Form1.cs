﻿using log4net;
using log4net.Config;
using MySql.Data.MySqlClient;
using System.Configuration;
using System.Data;
using System.Diagnostics;
using System.Net;
using System.Security.Cryptography;
namespace AutoUpdater
{
    public partial class Form1 : Form
    {
        /// <summary>
        /// List of file hashes.
        /// </summary>
        private readonly DataTable haslist = new();

        /// <summary>
        /// MySQL connection object.
        /// </summary>
        private readonly MySqlConnection coon = new MySqlConnection(
            $"Server={ConfigurationManager.AppSettings["MySqlServer"]};" +
            $"database={ConfigurationManager.AppSettings["MySqlDatabase"]};" +
            $"User ID={ConfigurationManager.AppSettings["MySqlUser"]};" +
            $"Password={ConfigurationManager.AppSettings["MySqlPassword"]};" +
            $"pooling=false;SslMode=none;charset=utf8mb4;"
        );

        /// <summary>
        /// Logger instance for logging.
        /// </summary>
        private static readonly ILog logger = LogManager.GetLogger(typeof(Form1));

        /// <summary>
        /// FTP server IP address.
        /// </summary>
        private string ftpServerIP = ConfigurationManager.AppSettings["FtpServerIP"];

        /// <summary>
        /// FTP username.
        /// </summary>
        private string ftpUserName = ConfigurationManager.AppSettings["FtpUsername"];

        /// <summary>
        /// FTP password.
        /// </summary>
        private string ftpPassword = ConfigurationManager.AppSettings["FtpPassword"];

        /// <summary>
        /// Initializes a new instance of the <see cref="Form1"/> class.
        /// </summary>
        public Form1()
        {
            InitializeComponent();
            // Load log4net configuration
            XmlConfigurator.Configure(new FileInfo("log4net.config"));

            // Log an info message at startup
            logger.Info("Uygulama başlatıldı.");
        }

        /// <summary>
        /// Kills processes by name.
        /// </summary>
        /// <param name="processName">Name of the process to kill.</param>
        private void KillProcessesByName(string processName)
        {
            try
            {
                Process[] processes = Process.GetProcessesByName(processName);

                foreach (Process process in processes)
                {
                    // Check the file path of the process
                    string processPath = string.Empty;

                    try
                    {
                        processPath = process.MainModule?.FileName;
                    }
                    catch
                    {
                        // Some processes may not be accessible, skip them
                        continue;
                    }

                    // Target only the exe run by your application
                    if (processPath.Contains("QA Utility.exe", StringComparison.OrdinalIgnoreCase))
                    {
                        process.Kill();
                        logger.Info($"{processName} süreci sonlandırıldı: {processPath}");
                    }
                }
            }
            catch (Exception ex)
            {
                logger.Error($"Süreç sonlandırılırken hata oluştu: {ex.Message}");
            }
        }

        /// <summary>
        /// Handles the Load event of the Form1 control.
        /// </summary>
        /// <param name="sender">The source of the event.</param>
        /// <param name="e">The <see cref="EventArgs"/> instance containing the event data.</param>
        private void Form1_Load(object sender, EventArgs e)
        {
            Control.CheckForIllegalCrossThreadCalls = false;

            KillProcessesByName("QA Utility");
            KillProcessesByName("adb");

            runDownloadProcess();
        }

        /// <summary>
        /// Indicates whether to retry the download process.
        /// </summary>
        private bool tekrardenensinmi = false;

        /// <summary>
        /// Runs the download process asynchronously.
        /// </summary>
        private async void runDownloadProcess()
        {
            await getAllHashesFromSqlAsync(); // Asynchronous data fetching
            progressBar1.Value = 0;
            progressBar1.Maximum = haslist.Rows.Count;

            _ = Task.Run(() => downloadFromHashDB());
        }

        /// <summary>
        /// Downloads files from the hash database asynchronously.
        /// </summary>
        private async Task downloadFromHashDB()
        {
            int maxConcurrentDownloads = 10; // Maximum 10 concurrent downloads
            SemaphoreSlim semaphore = new SemaphoreSlim(maxConcurrentDownloads);
            List<Task> downloadTasks = new List<Task>();

            progressBar1.Maximum = haslist.Rows.Count + 2;

            foreach (DataRow item in haslist.Rows)
            {
                await semaphore.WaitAsync();

                var task = Task.Run(async () =>
                {
                    try
                    {
                        string filePath = Path.Combine(Application.StartupPath, item["path"].ToString(), item["filename"].ToString());
                        string serverpath = Path.Combine(item["path"].ToString(), item["filename"].ToString());

                        if (File.Exists(filePath))
                        {
                            setStatusText($"{filePath} file checking.");
                            string localhash = GetLocalFileHash(filePath);
                            string serverhash = item["filehash"].ToString();

                            if (localhash != serverhash)
                            {
                                File.Delete(filePath);
                                await DownloadFileAsync(ftpServerIP + serverpath, ftpUserName, ftpPassword, filePath);
                                // UI update
                                progressBar1.Value++;
                                setStatusText($"{filePath} file downloaded.");
                            }
                        }
                        else
                        {
                            // Create directory if it exists
                            if (item["path"].ToString().Length > 1)
                            {
                                if (!Directory.Exists(item["path"].ToString()))
                                {
                                    Directory.CreateDirectory(item["path"].ToString());
                                }
                            }

                            await DownloadFileAsync(ftpServerIP + serverpath, ftpUserName, ftpPassword, filePath);
                            // UI update
                            progressBar1.Value++;
                            setStatusText($"{filePath} file downloaded.");
                        }
                    }
                    catch (Exception ex)
                    {
                        setStatusText($"Error downloading: {ex.Message}", true);
                    }
                    finally
                    {
                        semaphore.Release();
                    }
                });

                downloadTasks.Add(task);
            }

            await Task.WhenAll(downloadTasks);
            if (tekrardenensinmi)
            {
                progressBar1.Value = 0;
                tekrardenensinmi = false;
                await downloadFromHashDB(); // Added await because it's asynchronous
            }
            else
            {
                Process.Start("QA Utility.exe");
                Application.Exit();
            }
        }

        /// <summary>
        /// Downloads a file asynchronously from the FTP server.
        /// </summary>
        /// <param name="url">The URL of the file to download.</param>
        /// <param name="username">The FTP username.</param>
        /// <param name="password">The FTP password.</param>
        /// <param name="localPath">The local path to save the downloaded file.</param>
        /// <param name="timeoutMilliseconds">The timeout in milliseconds.</param>
        /// <returns>A task that represents the asynchronous download operation.</returns>
        /// <exception cref="TimeoutException">Thrown when the download times out.</exception>
        private async Task DownloadFileAsync(string url, string username, string password, string localPath, int timeoutMilliseconds = 3000)
        {
            tekrardenensinmi = true;
            FtpWebRequest request = (FtpWebRequest)WebRequest.Create(url);
            request.Method = WebRequestMethods.Ftp.DownloadFile;
            request.Credentials = new NetworkCredential(username, password);

            try
            {
                using (FtpWebResponse response = (FtpWebResponse)await request.GetResponseAsync())
                using (Stream responseStream = response.GetResponseStream())
                using (FileStream fileStream = new FileStream(localPath, FileMode.Create))
                {
                    // Apply timeout
                    var timeoutTask = Task.Delay(timeoutMilliseconds);
                    var copyTask = responseStream.CopyToAsync(fileStream);

                    if (await Task.WhenAny(copyTask, timeoutTask) == timeoutTask)
                    {
                        request.Abort();
                        setStatusText($"{localPath} dosyası indirme zaman aşımına uğradı.");
                        throw new TimeoutException($"Dosya indirirken zaman aşımı: {localPath}");
                    }

                    await copyTask;
                }
            }
            catch (Exception ex)
            {
                setStatusText($"{localPath} dosyası indirilirken hata oluştu: {ex.Message}", true);
                throw;
            }
        }

        /// <summary>
        /// Retrieves all file hashes from the SQL database.
        /// </summary>
        private void getAllHashesFromSql2()
        {
            string sql = "Select * from fileHashes";
            MySqlDataAdapter verial = new(sql, coon);
            _ = verial.Fill(haslist);
        }

        /// <summary>
        /// Retrieves all file hashes from the SQL database asynchronously.
        /// </summary>
        /// <returns>A task that represents the asynchronous operation.</returns>
        private async Task getAllHashesFromSqlAsync()
        {
            string sql = "SELECT * FROM fileHashes";

            try
            {
                if (coon.State != ConnectionState.Open)
                    await coon.OpenAsync();

                using var adapter = new MySqlDataAdapter(sql, coon);

                // Perform the DataTable fill operation asynchronously using Task.Run
                await Task.Run(() => adapter.Fill(haslist));

                setStatusText("Veriler başarıyla yüklendi.");
            }
            catch (Exception ex)
            {
                setStatusText($"Veri yüklenirken hata oluştu: {ex.Message}", true);
            }
            finally
            {
                await coon.CloseAsync(); // Close the connection
            }
        }

        /// <summary>
        /// Gets the MD5 hash of a local file.
        /// </summary>
        /// <param name="localPath">The local file path.</param>
        /// <returns>The MD5 hash of the file.</returns>
        private string GetLocalFileHash(string localPath)
        {
            using MD5 md5 = MD5.Create();
            using FileStream fileStream = File.OpenRead(localPath);
            byte[] hashData = md5.ComputeHash(fileStream);

            return BitConverter.ToString(hashData).Replace("-", "").ToLowerInvariant();
        }

        /// <summary>
        /// Sets the status text on the UI and logs the status.
        /// </summary>
        /// <param name="status">The status text.</param>
        /// <param name="error">Indicates whether the status is an error.</param>
        private void setStatusText(string status, bool error = false)
        {
            if (InvokeRequired)
            {
                Invoke((MethodInvoker)delegate { setStatusText(status, error); });
                return;
            }

            if (error)
            {
                logger.Error(status);
            }
            else
            {
                logger.Info(status);
            }

            labelStatus.Text = status;
        }
    }
}