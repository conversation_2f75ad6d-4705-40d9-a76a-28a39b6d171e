# Katkıda Bulunanlar ve Teşekkürler

Bu belge, Joygame QA Utility projesine katkıda bulunanları ve kullanılan üçüncü taraf kütüphaneleri ve araçları listelemektedir.

## Proje Ekibi

### Proje Lideri
- Abdulkadir AKTAŞ

### Çekirdek Geliştirme Ekibi
- [Geliştirici Adı 1]
- [Geliştirici Adı 2]
- [Geliştirici Adı 3]

### Katkıda Bulunanlar
- [Katkıda Bulunan 1]
- [Katkıda Bulunan 2]
- [Katkıda Bulunan 3]

### Dokümantasyon
- [Dokümantasyon Yazarı 1]
- [Dokümantasyon Yazarı 2]

### Test ve QA
- [Test Uzmanı 1]
- [Test Uzmanı 2]

## Üçüncü Taraf Kütüphaneler ve Araçlar

### .NET Kütüphaneleri

#### log4net
- **Lisans**: Apache License 2.0
- **Açıklama**: Loglama kütüphanesi
- **Web Sitesi**: https://logging.apache.org/log4net/

#### Newtonsoft.Json
- **Lisans**: MIT License
- **Açıklama**: JSON çerçevesi
- **Web Sitesi**: https://www.newtonsoft.com/json

#### MySql.Data
- **Lisans**: GPL License
- **Açıklama**: MySQL veritabanı bağlantı kütüphanesi
- **Web Sitesi**: https://dev.mysql.com/downloads/connector/net/

#### Microsoft.Web.WebView2
- **Lisans**: Microsoft Software License
- **Açıklama**: WebView2 kontrol kütüphanesi
- **Web Sitesi**: https://developer.microsoft.com/en-us/microsoft-edge/webview2/

#### SharpZipLib
- **Lisans**: MIT License
- **Açıklama**: Zip, GZip, Tar ve BZip2 kütüphanesi
- **Web Sitesi**: https://github.com/icsharpcode/SharpZipLib

#### RestSharp
- **Lisans**: Apache License 2.0
- **Açıklama**: REST API istemci kütüphanesi
- **Web Sitesi**: https://restsharp.dev/

#### Google.Apis
- **Lisans**: Apache License 2.0
- **Açıklama**: Google API istemci kütüphaneleri
- **Web Sitesi**: https://github.com/googleapis/google-api-dotnet-client

### JavaScript Kütüphaneleri

#### Tailwind CSS
- **Lisans**: MIT License
- **Açıklama**: Utility-first CSS çerçevesi
- **Web Sitesi**: https://tailwindcss.com/

#### Font Awesome
- **Lisans**: Font Awesome Free License
- **Açıklama**: İkon kütüphanesi
- **Web Sitesi**: https://fontawesome.com/

#### jQuery
- **Lisans**: MIT License
- **Açıklama**: JavaScript kütüphanesi
- **Web Sitesi**: https://jquery.com/

### Araçlar ve Bileşenler

#### Android Debug Bridge (ADB)
- **Lisans**: Apache License 2.0
- **Açıklama**: Android cihazlarla iletişim kurmak için komut satırı aracı
- **Web Sitesi**: https://developer.android.com/studio/command-line/adb

#### libimobiledevice
- **Lisans**: LGPL License
- **Açıklama**: iOS cihazlarla iletişim kurmak için kütüphane
- **Web Sitesi**: https://libimobiledevice.org/

#### FFmpeg
- **Lisans**: LGPL License
- **Açıklama**: Multimedya işleme kütüphanesi
- **Web Sitesi**: https://ffmpeg.org/

## Özel Teşekkürler

Aşağıdaki kişi ve kuruluşlara özel teşekkürlerimizi sunarız:

- Joygame yönetim ekibine, bu projeye verdikleri destek için
- Tüm test kullanıcılarına, değerli geri bildirimleri için
- Açık kaynak topluluğuna, ilham ve kaynaklar için

## Lisans Bilgileri

Joygame QA Utility, MIT Lisansı altında lisanslanmıştır. Daha fazla bilgi için [LICENSE](./LICENSE) dosyasına bakın.

Üçüncü taraf kütüphaneler ve araçlar, kendi lisansları altında dağıtılmaktadır. Lütfen her bir kütüphane veya aracın lisans bilgilerini kontrol edin.