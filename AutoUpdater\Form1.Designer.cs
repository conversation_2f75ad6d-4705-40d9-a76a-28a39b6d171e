﻿namespace AutoUpdater
{
    partial class Form1
    {
        /// <summary>
        ///  Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        ///  Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        ///  Required method for Designer support - do not modify
        ///  the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(Form1));
            progressBar1 = new ProgressBar();
            labelStatus = new Label();
            SuspendLayout();
            // 
            // progressBar1
            // 
            progressBar1.BackColor = Color.RosyBrown;
            progressBar1.Dock = DockStyle.Bottom;
            progressBar1.Location = new Point(0, 60);
            progressBar1.Name = "progressBar1";
            progressBar1.Size = new Size(457, 26);
            progressBar1.TabIndex = 1;
            // 
            // labelStatus
            // 
            labelStatus.BackColor = Color.Gainsboro;
            labelStatus.Dock = DockStyle.Fill;
            labelStatus.Font = new Font("Segoe UI", 8F, FontStyle.Bold, GraphicsUnit.Point);
            labelStatus.ForeColor = Color.DarkGreen;
            labelStatus.ImageAlign = ContentAlignment.TopCenter;
            labelStatus.Location = new Point(0, 0);
            labelStatus.Name = "labelStatus";
            labelStatus.Size = new Size(457, 60);
            labelStatus.TabIndex = 2;
            labelStatus.Text = "downloadText";
            labelStatus.TextAlign = ContentAlignment.MiddleCenter;
            // 
            // Form1
            // 
            AutoScaleDimensions = new SizeF(7F, 15F);
            AutoScaleMode = AutoScaleMode.Font;
            BackColor = Color.Black;
            ClientSize = new Size(457, 86);
            Controls.Add(labelStatus);
            Controls.Add(progressBar1);
            DoubleBuffered = true;
            FormBorderStyle = FormBorderStyle.Fixed3D;
            Icon = (Icon)resources.GetObject("$this.Icon");
            IsMdiContainer = true;
            MaximizeBox = false;
            MinimizeBox = false;
            Name = "Form1";
            StartPosition = FormStartPosition.CenterScreen;
            Text = "QA Utility Updater";
            TopMost = true;
            Load += Form1_Load;
            ResumeLayout(false);
        }

        #endregion
        private ProgressBar progressBar1;
        private Label labelStatus;
    }
}