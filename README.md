# Joygame QA Utility

Joygame QA Utility, mobil oyun ve uygulamaların test edilmesi için tasarlanmış kapsamlı bir QA (Kalite Güvence) aracıdır. Bu araç, QA ekiplerinin Android ve iOS cihazlarla etkile<PERSON>, APK yüklemesini, ekran kaydı almasını, log yönetimini ve TestRail, Redmine gibi servislerle entegrasyon sağlamasını kolaylaştırır.

## Özellikler

- **Mobil Cihaz Yönetimi**: Android ve iOS cihazlarla kolay bağlantı ve yönetim
- **APK Yükleme ve Yönetimi**: Sürükle-bırak APK yükleme, uygulama başlatma ve kaldırma
- **Ekran Kaydı ve Görüntüsü**: Yüksek kaliteli ekran kaydı ve ekran görüntüsü alma
- **Log Yönetimi**: Gerçek zamanlı log görüntüleme, filtreleme ve analiz
- **TestRail Entegrasyonu**: Test durumlarını görüntüleme ve sonuçları gönderme
- **Redmine Entegrasyonu**: Sorun oluşturma, güncelleme ve takip etme
- **Google API Entegrasyonu**: Google Drive, Sheets ve Play Store entegrasyonu
- **Otomatik Güncelleme**: Uygulama güncellemelerini otomatik kontrol etme ve yükleme

## Proje Yapısı

- **AutoUpdater/**: Uygulama güncelleyici aracı.
- **qa_utility/**: QA süreçleri için ana uygulama ve yardımcı araçlar.
  - `classes/`: Google API, LDAP, Redmine, TestRail gibi servislerle entegrasyon sınıfları.
  - `flashlight/`: Ekran kaydı ve görüntü alma araçları.
  - `ioslog/`: iOS cihaz loglama ve yönetim araçları.
  - `platform-tools/`: Android platform araçları (ör. adb).
  - `Resources/`: Web arayüzü ve diğer kaynaklar.

## Sistem Gereksinimleri

- **İşletim Sistemi**: Windows 10 veya üzeri (64-bit önerilir)
- **İşlemci**: Intel Core i3 veya dengi (2.0 GHz veya üzeri)
- **RAM**: En az 4 GB (8 GB önerilir)
- **Disk Alanı**: En az 500 MB boş alan
- **Ekran Çözünürlüğü**: 1280x720 veya üzeri
- **.NET Framework**: 4.7.2 veya üzeri
- **WebView2 Runtime**: En son sürüm

## Kurulum

### Ön Koşullar

1. [.NET Framework 4.7.2](https://dotnet.microsoft.com/download/dotnet-framework/net472) veya üzeri
2. [WebView2 Runtime](https://developer.microsoft.com/en-us/microsoft-edge/webview2/)
3. [Visual C++ Redistributable](https://support.microsoft.com/en-us/help/2977003/the-latest-supported-visual-c-downloads)
4. [MySQL Connector/NET](https://dev.mysql.com/downloads/connector/net/)

### Kurulum Adımları

1. Projeyi klonlayın:
   ```sh
   git clone https://github.com/joygame/joygame_utility.git
   ```
2. Visual Studio ile `QA Utility.sln` dosyasını açın.
3. Gerekli NuGet paketlerini yükleyin.
4. Uygulamayı derleyin ve çalıştırın.

Detaylı kurulum talimatları için [Kurulum Kılavuzu](./Docs/installation_guide.md) belgesine bakın.

## Kullanım

### Mobil Cihaz Bağlantısı

#### Android

1. Android cihazınızda USB hata ayıklama modunu etkinleştirin
2. Cihazı USB kablosuyla bilgisayarınıza bağlayın
3. Joygame QA Utility'de "Android" sekmesini seçin
4. "Cihazları Yenile" düğmesine tıklayın

#### iOS

1. iTunes'un bilgisayarınızda yüklü olduğundan emin olun
2. iOS cihazı USB kablosuyla bilgisayarınıza bağlayın
3. Joygame QA Utility'de "iOS" sekmesini seçin
4. "Cihazları Yenile" düğmesine tıklayın

### APK Yükleme

1. "APK Yükle" düğmesine tıklayın veya APK dosyasını uygulama penceresine sürükleyip bırakın
2. Bağlı bir Android cihaz seçin
3. "Yükle" düğmesine tıklayın

Daha fazla bilgi için [Kullanım Kılavuzu](./Docs/user_guide.md) belgesine bakın.

## Dokümantasyon

Daha fazla bilgi için aşağıdaki dokümantasyon dosyalarına bakın:

- [Kullanım Kılavuzu](./Docs/user_guide.md)
- [Teknik Dokümantasyon](./Docs/technical_docs.md)
- [Kurulum Kılavuzu](./Docs/installation_guide.md)
- [Geliştirici Kılavuzu](./Docs/developer_guide.md)
- [API Referansı](./Docs/api_reference.md)
- [Sorun Giderme Kılavuzu](./Docs/troubleshooting_guide.md)
- [Sürüm Notları](./Docs/release_notes.md)
- [Katkıda Bulunma](./Docs/contributing.md)
- [Sıkça Sorulan Sorular](./Docs/faq.md)
- [Terimler Sözlüğü](./Docs/glossary.md)
- [Ürün Gereksinimleri Dokümanı](./Docs/prd.md)

## Katkı

Katkıda bulunmak için lütfen [Katkıda Bulunma](./Docs/contributing.md) kılavuzunu inceleyin, bir fork oluşturun ve pull request gönderin.

## Lisans

Bu proje MIT lisansı ile lisanslanmıştır. Ayrıntılar için `LICENSE` dosyasına bakınız.

## İletişim

Sorularınız veya geri bildirimleriniz için lütfen [<EMAIL>](mailto:<EMAIL>) adresine e-posta gönderin.

