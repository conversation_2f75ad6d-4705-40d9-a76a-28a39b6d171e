# Joygame QA Utility - <PERSON><PERSON> Giderme Kılavuzu

## İçindekiler

1. [<PERSON><PERSON><PERSON>](#1-g<PERSON><PERSON>)
2. [<PERSON>y<PERSON><PERSON>a Başlatma Sorunları](#2-uygulama-başlatma-sorunları)
3. [Kullanıcı Arayüzü Sorunları](#3-kullanıcı-arayüzü-sorunları)
4. [Mobil Cihaz Bağlantı Sorunları](#4-mobil-cihaz-bağlantı-sorunları)
5. [APK Yükleme ve Kurulum Sorunları](#5-apk-yükleme-ve-kurulum-sorunları)
6. [<PERSON><PERSON><PERSON> Kaydı ve Görüntüsü Sorunları](#6-ekran-kaydı-ve-görüntüsü-sorunları)
7. [Log <PERSON><PERSON><PERSON>runları](#7-log-kayıt-sorunları)
8. [TestRail Entegrasyon Sorunları](#8-testrail-entegrasyon-sorunları)
9. [Redmine Entegrasyon Sorunları](#9-redmine-entegrasyon-sorunları)
10. [Google API Sorunları](#10-google-api-sorunları)
11. [Güncelleme Sorunları](#11-güncelleme-sorunları)
12. [Veritabanı Bağlantı Sorunları](#12-veritabanı-bağlantı-sorunları)
13. [Performans Sorunları](#13-performans-sorunları)
14. [Hata Raporlama](#14-hata-raporlama)

## 1. Giriş

Bu sorun giderme kılavuzu, Joygame QA Utility uygulamasını kullanırken karşılaşabileceğiniz yaygın sorunları ve bunların çözümlerini içerir. Her bölüm, belirli bir sorun kategorisini ele alır ve adım adım çözüm yolları sunar.

Sorunları giderirken aşağıdaki genel adımları izlemeniz önerilir:

1. Sorunu tam olarak tanımlayın
2. Hata mesajlarını not edin
3. Log dosyalarını kontrol edin
4. Bu kılavuzdaki ilgili bölümü bulun
5. Önerilen çözümleri sırayla deneyin
6. Sorun devam ederse, daha fazla yardım için geliştirme ekibiyle iletişime geçin

## 2. Uygulama Başlatma Sorunları

### 2.1 Uygulama Başlatılamıyor

**Belirtiler**:
- Uygulama açılmıyor veya hemen kapanıyor
- "Uygulama çalıştırılamadı" hatası alınıyor
- Beyaz ekran görünüyor ve sonra uygulama kapanıyor

**Olası Nedenler ve Çözümler**:

1. **Eksik veya Bozuk .NET Framework**
   - .NET Framework 4.7.2 veya üzerinin yüklü olduğunu kontrol edin
   - Gerekirse .NET Framework'ü yeniden yükleyin

2. **Eksik WebView2 Runtime**
   - WebView2 Runtime'ın yüklü olduğunu kontrol edin
   - [Microsoft Edge WebView2](https://developer.microsoft.com/en-us/microsoft-edge/webview2/) sayfasından indirip yükleyin

3. **Yapılandırma Dosyası Sorunları**
   - `App.config` dosyasının varlığını ve doğruluğunu kontrol edin
   - Dosyayı varsayılan ayarlarla yeniden oluşturun

4. **Dosya İzinleri**
   - Uygulamayı yönetici olarak çalıştırmayı deneyin
   - Uygulama klasörünün okuma/yazma izinlerini kontrol edin

5. **Başka Bir Örnek Zaten Çalışıyor**
   - Görev Yöneticisi'nde "QA Utility.exe" sürecini kontrol edin
   - Varsa, süreci sonlandırın ve uygulamayı yeniden başlatın

6. **Log Dosyalarını Kontrol Edin**
   - `logs` klasöründeki log dosyalarını inceleyin
   - Hata mesajlarını not edin ve geliştirme ekibine bildirin

### 2.2 "Mutex Already Exists" Hatası

**Belirtiler**:
- "Another instance of the application is already running" mesajı görünüyor

**Çözüm**:
1. Görev Yöneticisi'ni açın
2. "QA Utility.exe" sürecini bulun ve sonlandırın
3. Uygulamayı yeniden başlatın

### 2.3 Veritabanı Bağlantı Hatası

**Belirtiler**:
- "Cannot connect to database" hatası alınıyor
- Uygulama başlatılıyor ancak bazı özellikler çalışmıyor

**Çözüm**:
1. MySQL sunucusunun çalıştığını kontrol edin
2. `App.config` dosyasındaki veritabanı bağlantı dizesini kontrol edin
3. Veritabanı kullanıcı adı ve şifresinin doğru olduğunu doğrulayın
4. Güvenlik duvarı ayarlarını kontrol edin

## 3. Kullanıcı Arayüzü Sorunları

### 3.1 WebView2 İçeriği Görünmüyor

**Belirtiler**:
- Ana pencere açılıyor ancak içerik boş veya beyaz görünüyor
- "WebView2 failed to load" hatası alınıyor

**Çözüm**:
1. WebView2 Runtime'ın yüklü olduğunu kontrol edin
2. Uygulamayı yeniden başlatın
3. Geçici internet dosyalarını temizleyin:
   - `%LOCALAPPDATA%\Microsoft\Edge\User Data` klasörünü yedekleyin ve silin
4. İnternet bağlantınızı kontrol edin

### 3.2 Arayüz Öğeleri Düzgün Görüntülenmiyor

**Belirtiler**:
- Düğmeler, menüler veya diğer arayüz öğeleri yanlış hizalanmış
- Yazı tipleri veya renkler bozuk görünüyor

**Çözüm**:
1. Ekran çözünürlüğünüzün en az 1280x720 olduğunu kontrol edin
2. Windows görüntü ölçeklendirme ayarlarını %100 olarak ayarlayın
3. Uygulamayı yeniden başlatın
4. Tarayıcı önbelleğini temizleyin:
   - Uygulama içinde F12 tuşuna basın (geliştirici araçları)
   - "Application" sekmesine gidin
   - "Clear storage" seçeneğini tıklayın

### 3.3 Sekme Geçişleri Çalışmıyor

**Belirtiler**:
- Sekmeler arasında geçiş yapılamıyor
- Sekme içeriği yüklenmiyor

**Çözüm**:
1. Uygulamayı yeniden başlatın
2. JavaScript konsolunda hata olup olmadığını kontrol edin (F12)
3. WebView2 önbelleğini temizleyin

## 4. Mobil Cihaz Bağlantı Sorunları

### 4.1 Android Cihaz Algılanmıyor

**Belirtiler**:
- "No device connected" mesajı görünüyor
- Cihaz listesi boş

**Çözüm**:
1. USB kablosunu kontrol edin ve gerekirse değiştirin
2. Cihazda USB hata ayıklama modunun etkin olduğunu kontrol edin:
   - Ayarlar > Geliştirici Seçenekleri > USB Hata Ayıklama
3. Cihaz sürücülerinin yüklü olduğunu kontrol edin
4. ADB'yi yeniden başlatın:
   - Komut isteminde `adb kill-server` ve ardından `adb start-server` komutlarını çalıştırın
5. Farklı bir USB portu deneyin
6. Cihazı yeniden başlatın

### 4.2 iOS Cihaz Algılanmıyor

**Belirtiler**:
- iOS cihaz listesi boş
- "Cannot connect to iOS device" hatası alınıyor

**Çözüm**:
1. USB kablosunu kontrol edin ve gerekirse değiştirin
2. iTunes'un yüklü olduğunu kontrol edin
3. Cihazın bilgisayara güvendiğini kontrol edin:
   - Cihazda "Bu bilgisayara güveniyor musunuz?" uyarısını onaylayın
4. iOS cihaz sürücülerini yeniden yükleyin
5. Cihazı yeniden başlatın
6. Farklı bir USB portu deneyin

### 4.3 Cihaz Bağlantısı Kesiliyor

**Belirtiler**:
- Cihaz başlangıçta algılanıyor ancak bağlantı aniden kesiliyor
- "Device disconnected" mesajı görünüyor

**Çözüm**:
1. USB kablosunu değiştirin
2. Farklı bir USB portu deneyin
3. USB güç yönetimi ayarlarını kontrol edin:
   - Aygıt Yöneticisi > USB Hub > Özellikler > Güç Yönetimi
   - "Bilgisayar güç tasarrufu yapmak için bu aygıtı kapatabilir" seçeneğini devre dışı bırakın
4. Cihazı şarj modunda değil, dosya aktarım modunda bağlayın

## 5. APK Yükleme ve Kurulum Sorunları

### 5.1 APK Yüklenemiyor

**Belirtiler**:
- Sürükle-bırak işlemi çalışmıyor
- "Failed to upload APK" hatası alınıyor

**Çözüm**:
1. APK dosyasının geçerli olduğunu kontrol edin
2. Dosya izinlerini kontrol edin
3. Farklı bir APK dosyası deneyin
4. Uygulamayı yeniden başlatın

### 5.2 APK Kurulumu Başarısız

**Belirtiler**:
- "Installation failed" hatası alınıyor
- ADB hata mesajları görünüyor

**Olası Nedenler ve Çözümler**:

1. **Yetersiz Depolama Alanı**
   - Cihazda yeterli depolama alanı olduğunu kontrol edin
   - Eski uygulamaları veya dosyaları kaldırın

2. **İmza Sorunları**
   - Cihazda "Bilinmeyen kaynaklardan yükleme" seçeneğinin etkin olduğunu kontrol edin
   - Aynı uygulamanın farklı bir imza ile yüklü olup olmadığını kontrol edin ve kaldırın

3. **APK Bozuk**
   - Farklı bir APK dosyası deneyin
   - APK'yı yeniden indirin

4. **ADB Sorunları**
   - ADB'yi yeniden başlatın: `adb kill-server` ve `adb start-server`
   - Cihazı yeniden başlatın

### 5.3 Uygulama Başlatılamıyor

**Belirtiler**:
- APK kurulumu başarılı ancak uygulama başlatılamıyor
- "App not installed" veya "App crashed" hatası alınıyor

**Çözüm**:
1. Uygulamanın cihazla uyumlu olduğunu kontrol edin
2. Uygulama verilerini temizleyin:
   - Ayarlar > Uygulamalar > [Uygulama Adı] > Depolama > Verileri Temizle
3. Cihazı yeniden başlatın
4. Logcat çıktısını kontrol edin ve hata mesajlarını analiz edin

## 6. Ekran Kaydı ve Görüntüsü Sorunları

### 6.1 Ekran Kaydı Başlatılamıyor

**Belirtiler**:
- "Failed to start recording" hatası alınıyor
- Kayıt düğmesi çalışmıyor

**Çözüm**:
1. Cihazın ekran kaydını desteklediğini kontrol edin
2. Android 10 veya üzeri için izinleri kontrol edin
3. Cihazda yeterli depolama alanı olduğunu kontrol edin
4. ADB bağlantısını yeniden başlatın
5. NVIDIA ShadowPlay için:
   - NVIDIA GeForce Experience'ın yüklü olduğunu kontrol edin
   - ShadowPlay'in etkin olduğunu kontrol edin

### 6.2 Ekran Görüntüsü Alınamıyor

**Belirtiler**:
- "Failed to capture screenshot" hatası alınıyor
- Ekran görüntüsü düğmesi çalışmıyor

**Çözüm**:
1. Cihaz bağlantısını kontrol edin
2. ADB'yi yeniden başlatın
3. Cihazı yeniden başlatın
4. Farklı bir ekran görüntüsü yöntemi deneyin (cihaz tuş kombinasyonu)

### 6.3 Kayıt Dosyası Bulunamıyor

**Belirtiler**:
- Kayıt tamamlandı ancak dosya bulunamıyor
- "Recording file not found" hatası alınıyor

**Çözüm**:
1. Kayıt klasörünün varlığını ve yazma izinlerini kontrol edin
2. Depolama alanının yeterli olduğunu kontrol edin
3. Kayıt ayarlarını kontrol edin
4. Cihazda doğrudan dosya yöneticisini kullanarak kaydı arayın

## 7. Log Kayıt Sorunları

### 7.1 Logcat Başlatılamıyor

**Belirtiler**:
- "Failed to start logcat" hatası alınıyor
- Log çıktısı görünmüyor

**Çözüm**:
1. ADB bağlantısını kontrol edin
2. ADB'yi yeniden başlatın
3. Cihazı yeniden başlatın
4. Komut isteminde manuel olarak logcat'i çalıştırmayı deneyin:
   - `adb logcat > log.txt`

### 7.2 iOS Log Alınamıyor

**Belirtiler**:
- "Failed to capture iOS logs" hatası alınıyor
- Log çıktısı boş

**Çözüm**:
1. Cihaz bağlantısını kontrol edin
2. Cihazın bilgisayara güvendiğini kontrol edin
3. iTunes'un yüklü olduğunu kontrol edin
4. iOS araçlarını yeniden yükleyin

### 7.3 Log Dosyaları Çok Büyük

**Belirtiler**:
- Log dosyaları çok hızlı büyüyor
- Disk alanı azalıyor

**Çözüm**:
1. Log filtreleme seçeneklerini kullanın
2. Daha kısa log kayıt süreleri kullanın
3. Eski log dosyalarını düzenli olarak temizleyin
4. Log4net yapılandırmasını güncelleyin:
   - `log4net.config` dosyasında maksimum dosya boyutunu ve yedekleme sayısını ayarlayın

## 8. TestRail Entegrasyon Sorunları

### 8.1 TestRail'e Bağlanılamıyor

**Belirtiler**:
- "Cannot connect to TestRail" hatası alınıyor
- TestRail sekmesi yüklenmiyor

**Çözüm**:
1. İnternet bağlantınızı kontrol edin
2. TestRail URL'sinin doğru olduğunu kontrol edin
3. API kimlik bilgilerinin doğru olduğunu kontrol edin:
   - `App.config` dosyasındaki TestRail kullanıcı adı ve API anahtarını kontrol edin
4. TestRail sunucusunun çalıştığını kontrol edin
5. Proxy ayarlarını kontrol edin

### 8.2 Test Sonuçları Gönderilemiyor

**Belirtiler**:
- "Failed to submit test results" hatası alınıyor
- Sonuçlar TestRail'de görünmüyor

**Çözüm**:
1. API kimlik bilgilerinin doğru olduğunu kontrol edin
2. Kullanıcının TestRail'de yeterli izinlere sahip olduğunu kontrol edin
3. Test durumu ID'sinin doğru olduğunu kontrol edin
4. API yanıtını kontrol edin ve hata mesajlarını analiz edin

### 8.3 Test Durumları Alınamıyor

**Belirtiler**:
- "Failed to retrieve test cases" hatası alınıyor
- Test durumları listesi boş

**Çözüm**:
1. Proje ve test paketi ID'lerinin doğru olduğunu kontrol edin
2. Kullanıcının TestRail'de yeterli izinlere sahip olduğunu kontrol edin
3. TestRail API sürümünün uyumlu olduğunu kontrol edin
4. API yanıtını kontrol edin ve hata mesajlarını analiz edin

## 9. Redmine Entegrasyon Sorunları

### 9.1 Redmine'a Bağlanılamıyor

**Belirtiler**:
- "Cannot connect to Redmine" hatası alınıyor
- Redmine sekmesi yüklenmiyor

**Çözüm**:
1. İnternet bağlantınızı kontrol edin
2. Redmine URL'sinin doğru olduğunu kontrol edin
3. API anahtarının doğru olduğunu kontrol edin:
   - `App.config` dosyasındaki Redmine API anahtarını kontrol edin
4. Redmine sunucusunun çalıştığını kontrol edin
5. Proxy ayarlarını kontrol edin

### 9.2 Sorun Oluşturulamıyor

**Belirtiler**:
- "Failed to create issue" hatası alınıyor
- Oluşturulan sorun Redmine'da görünmüyor

**Çözüm**:
1. API anahtarının doğru olduğunu kontrol edin
2. Kullanıcının Redmine'da yeterli izinlere sahip olduğunu kontrol edin
3. Proje ID'sinin doğru olduğunu kontrol edin
4. Zorunlu alanların doldurulduğunu kontrol edin
5. API yanıtını kontrol edin ve hata mesajlarını analiz edin

### 9.3 Sorunlar Listelenemiyor

**Belirtiler**:
- "Failed to retrieve issues" hatası alınıyor
- Sorun listesi boş

**Çözüm**:
1. API anahtarının doğru olduğunu kontrol edin
2. Kullanıcının Redmine'da yeterli izinlere sahip olduğunu kontrol edin
3. Proje ID'sinin doğru olduğunu kontrol edin
4. Filtreleme parametrelerini kontrol edin
5. API yanıtını kontrol edin ve hata mesajlarını analiz edin

## 10. Google API Sorunları

### 10.1 Google Hesabına Bağlanılamıyor

**Belirtiler**:
- "Failed to authenticate with Google" hatası alınıyor
- OAuth yetkilendirme penceresi açılmıyor veya hata veriyor

**Çözüm**:
1. İnternet bağlantınızı kontrol edin
2. `client_secret.json` dosyasının varlığını ve doğruluğunu kontrol edin
3. Google Cloud Console'da API'nin etkin olduğunu kontrol edin
4. OAuth yönlendirme URI'larının doğru yapılandırıldığını kontrol edin
5. Kimlik bilgilerini yeniden oluşturun

### 10.2 Google Drive'a Dosya Yüklenemiyor

**Belirtiler**:
- "Failed to upload file to Google Drive" hatası alınıyor
- Dosya Google Drive'da görünmüyor

**Çözüm**:
1. Google hesabı kimlik doğrulamasını kontrol edin
2. Kullanıcının Google Drive'da yeterli izinlere sahip olduğunu kontrol edin
3. Dosya boyutunu kontrol edin (çok büyük olabilir)
4. Dosya yolunun doğru olduğunu kontrol edin
5. API yanıtını kontrol edin ve hata mesajlarını analiz edin

### 10.3 Google Sheets Verisi Alınamıyor

**Belirtiler**:
- "Failed to retrieve Google Sheets data" hatası alınıyor
- Veri boş veya eksik

**Çözüm**:
1. Google hesabı kimlik doğrulamasını kontrol edin
2. Elektronik tablo ID'sinin doğru olduğunu kontrol edin
3. Kullanıcının elektronik tabloya erişim izni olduğunu kontrol edin
4. Elektronik tablo aralığının doğru olduğunu kontrol edin
5. API yanıtını kontrol edin ve hata mesajlarını analiz edin

## 11. Güncelleme Sorunları

### 11.1 Güncellemeler Kontrol Edilemiyor

**Belirtiler**:
- "Failed to check for updates" hatası alınıyor
- AutoUpdater çalışmıyor

**Çözüm**:
1. İnternet bağlantınızı kontrol edin
2. FTP sunucu ayarlarını kontrol edin:
   - `App.config` dosyasındaki FTP sunucu, kullanıcı adı ve şifreyi kontrol edin
3. Güncelleme sunucusunun çalıştığını kontrol edin
4. Proxy ayarlarını kontrol edin
5. Güvenlik duvarı ayarlarını kontrol edin

### 11.2 Güncellemeler İndirilemiyor

**Belirtiler**:
- "Failed to download updates" hatası alınıyor
- İndirme başlıyor ancak tamamlanmıyor

**Çözüm**:
1. İnternet bağlantınızı kontrol edin
2. Disk alanının yeterli olduğunu kontrol edin
3. Geçici klasörün yazma izinlerini kontrol edin
4. FTP kimlik bilgilerinin doğru olduğunu kontrol edin
5. Güvenlik duvarı ayarlarını kontrol edin

### 11.3 Güncellemeler Yüklenemiyor

**Belirtiler**:
- "Failed to install updates" hatası alınıyor
- Güncelleme indiriliyor ancak yüklenemiyor

**Çözüm**:
1. Uygulamanın kapalı olduğunu kontrol edin
2. Dosya izinlerini kontrol edin
3. Uygulamayı yönetici olarak çalıştırmayı deneyin
4. Virüs tarayıcısının güncellemeyi engellemediğini kontrol edin
5. Güncelleme dosyalarının bütünlüğünü kontrol edin

## 12. Veritabanı Bağlantı Sorunları

### 12.1 Veritabanına Bağlanılamıyor

**Belirtiler**:
- "Cannot connect to database" hatası alınıyor
- Veritabanı bağlantısı gerektiren özellikler çalışmıyor

**Çözüm**:
1. MySQL sunucusunun çalıştığını kontrol edin
2. Bağlantı dizesinin doğru olduğunu kontrol edin:
   - `App.config` dosyasındaki sunucu, veritabanı, kullanıcı adı ve şifreyi kontrol edin
3. Veritabanının varlığını kontrol edin
4. Kullanıcının veritabanına erişim izni olduğunu kontrol edin
5. Güvenlik duvarı ayarlarını kontrol edin

### 12.2 Veritabanı Sorguları Başarısız

**Belirtiler**:
- "Database query failed" hatası alınıyor
- Veri kaydedilemiyor veya alınamıyor

**Çözüm**:
1. Veritabanı şemasının doğru olduğunu kontrol edin
2. SQL sorgusunu kontrol edin
3. Kullanıcının yeterli izinlere sahip olduğunu kontrol edin
4. Veritabanı günlüklerini kontrol edin
5. Uygulama günlüklerini kontrol edin

### 12.3 Veritabanı Bağlantısı Zaman Aşımına Uğruyor

**Belirtiler**:
- "Database connection timeout" hatası alınıyor
- Bağlantı başlangıçta çalışıyor ancak sonra kesiliyor

**Çözüm**:
1. MySQL sunucusunun yükünü kontrol edin
2. Bağlantı havuzu ayarlarını kontrol edin
3. Zaman aşımı değerlerini artırın
4. Ağ bağlantısını kontrol edin
5. MySQL sunucusunu yeniden başlatın

## 13. Performans Sorunları

### 13.1 Uygulama Yavaş Çalışıyor

**Belirtiler**:
- Kullanıcı arayüzü tepki vermiyor veya gecikmeli tepki veriyor
- İşlemler beklenenden uzun sürüyor

**Çözüm**:
1. Bilgisayarın sistem kaynaklarını kontrol edin (CPU, RAM)
2. Arka planda çalışan diğer uygulamaları kapatın
3. Geçici dosyaları temizleyin:
   - `%LOCALAPPDATA%\Microsoft\Edge\User Data` klasörünü temizleyin
4. Log dosyalarının boyutunu kontrol edin ve gerekirse temizleyin
5. Uygulamayı yeniden başlatın

### 13.2 Yüksek CPU Kullanımı

**Belirtiler**:
- CPU kullanımı %80'in üzerinde
- Bilgisayar fan sesi artıyor
- Uygulama tepki vermiyor

**Çözüm**:
1. Görev Yöneticisi'nde hangi sürecin CPU'yu kullandığını kontrol edin
2. ADB veya logcat süreçlerini kontrol edin
3. WebView2 içeriğini kontrol edin (JavaScript döngüleri)
4. Uygulamayı yeniden başlatın
5. Bilgisayarı yeniden başlatın

### 13.3 Bellek Sızıntısı

**Belirtiler**:
- Bellek kullanımı zamanla artıyor
- Uygulama uzun süre çalıştıktan sonra yavaşlıyor veya çöküyor

**Çözüm**:
1. Uygulamayı düzenli olarak yeniden başlatın
2. Log kayıt süreçlerini kontrol edin ve gerekirse durdurun
3. Uzun süren işlemleri tamamladıktan sonra uygulamayı yeniden başlatın
4. Uygulamanın en son sürümünü kullandığınızdan emin olun

## 14. Hata Raporlama

Bu kılavuzda çözüm bulamadığınız sorunlar için, aşağıdaki bilgileri içeren bir hata raporu oluşturun ve geliştirme ekibine gönderin:

1. **Sorunun Açıklaması**:
   - Ne oldu?
   - Ne yapıyordunuz?
   - Hata mesajı neydi?

2. **Sistem Bilgileri**:
   - İşletim Sistemi ve Sürümü
   - Uygulama Sürümü
   - Donanım Özellikleri (RAM, CPU)

3. **Log Dosyaları**:
   - `logs` klasöründeki ilgili log dosyalarını ekleyin
   - Hata oluştuğu andaki ekran görüntüsünü ekleyin

4. **Yeniden Oluşturma Adımları**:
   - Sorunu yeniden oluşturmak için adım adım talimatlar
   - Sorunun ne sıklıkla oluştuğu (her zaman, bazen, nadiren)

5. **Ek Bilgiler**:
   - Bağlı cihaz bilgileri (varsa)
   - Yakın zamanda yapılan değişiklikler veya güncellemeler

Hata raporunu şu adrese gönderin: `<EMAIL>`

---

**Not**: Bu sorun giderme kılavuzu, Joygame QA Utility'nin yaygın sorunlarını kapsar. Daha fazla yardım için geliştirme ekibiyle iletişime geçin.