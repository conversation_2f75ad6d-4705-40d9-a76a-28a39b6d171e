# Joygame QA Utility - Katkıda Bulunma Kılavuzu

## İçindekiler

1. [<PERSON><PERSON><PERSON>](#1-g<PERSON><PERSON>)
2. [<PERSON><PERSON><PERSON><PERSON><PERSON>](#2-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ural<PERSON>)
3. [Ba<PERSON>lar<PERSON>](#3-ba<PERSON><PERSON><PERSON>)
4. [<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Ortamı](#4-geliştirme-ortamı)
5. [<PERSON><PERSON>](#5-kod-standartları)
6. [Branching Stratejisi](#6-branching-stratejisi)
7. [<PERSON>mmit <PERSON>](#7-commit-mesajları)
8. [Pull Request Süreci](#8-pull-request-süreci)
9. [Test Etme](#9-test-etme)
10. [Dokümantasyon](#10-dokümantasyon)
11. [<PERSON><PERSON><PERSON><PERSON><PERSON> Yönetimi](#11-sürüm-yönetimi)
12. [<PERSON><PERSON>](#12-sorun-raporlama)
13. [<PERSON><PERSON><PERSON><PERSON><PERSON>](#13-il<PERSON><PERSON><PERSON>)

## 1. <PERSON><PERSON><PERSON>

Joygame QA Utility projesine katkıda bulunmak istediğiniz için teşekkür ederiz! <PERSON><PERSON>, projeye katkıda bulunmak isteyenler için süreçleri, standartları ve beklentileri açıklar. Katkıda bulunmadan önce lütfen bu kılavuzu dikkatlice okuyun.

Katkıda bulunmanın birçok yolu vardır:

- Hata raporlama
- Yeni özellikler önerme
- Kod katkısında bulunma
- Dokümantasyon iyileştirme
- Test etme ve geri bildirim sağlama

## 2. Davranış Kuralları

Joygame QA Utility projesi, açık ve kapsayıcı bir topluluk olmayı hedefler. Tüm katılımcılardan aşağıdaki davranış kurallarına uymaları beklenir:

- Saygılı ve profesyonel bir dil kullanın
- Yapıcı geri bildirim sağlayın
- Farklı görüşlere açık olun
- Kişisel saldırılardan kaçının
- Topluluk üyelerine yardımcı olun

Bu kurallara uymayan davranışlar, projeye katkıda bulunma ayrıcalığının geçici veya kalıcı olarak kaybedilmesine neden olabilir.

## 3. Başlarken

### 3.1 Projeyi Forklama ve Klonlama

1. GitHub'da projeyi forklayın
2. Yerel makinenize klonlayın:
   ```bash
   git clone https://github.com/[kullanıcı-adınız]/joygame_utility.git
   cd joygame_utility
   ```

3. Upstream remote'u ekleyin:
   ```bash
   git remote add upstream https://github.com/joygame/joygame_utility.git
   ```

### 3.2 Geliştirme Dalı Oluşturma

Yeni bir özellik veya hata düzeltmesi üzerinde çalışmaya başlamadan önce, güncel `develop` dalından yeni bir dal oluşturun:

```bash
git checkout develop
git pull upstream develop
git checkout -b feature/yeni-ozellik-adi
# veya
git checkout -b bugfix/hata-aciklamasi
```

## 4. Geliştirme Ortamı

### 4.1 Gerekli Yazılımlar

- Visual Studio 2019 veya üzeri (Community Edition yeterlidir)
- .NET Framework 4.7.2 SDK
- Git
- MySQL Server (yerel geliştirme için)
- Node.js ve npm (WebView2 içeriği geliştirmek için, isteğe bağlı)

### 4.2 Bağımlılıkları Yükleme

1. Visual Studio'da çözümü açın: `QA Utility.sln`
2. NuGet paketlerini geri yükleyin:
   - Solution Explorer'da çözüme sağ tıklayın
   - "Restore NuGet Packages" seçeneğini seçin

### 4.3 Veritabanı Kurulumu

1. MySQL Server'ı kurun ve yapılandırın
2. Veritabanını oluşturun:
   ```sql
   CREATE DATABASE joygame_qa_utility;
   ```

3. Veritabanı kullanıcısı oluşturun:
   ```sql
   CREATE USER 'qa_user'@'localhost' IDENTIFIED BY 'password';
   GRANT ALL PRIVILEGES ON joygame_qa_utility.* TO 'qa_user'@'localhost';
   FLUSH PRIVILEGES;
   ```

4. `App.config` dosyasında veritabanı bağlantı dizesini güncelleyin

## 5. Kod Standartları

### 5.1 C# Kod Standartları

- **İsimlendirme Kuralları**:
  - Sınıflar ve metotlar için PascalCase (örn. `GoogleApiService`)
  - Değişkenler için camelCase (örn. `deviceInfo`)
  - Sabitler için UPPER_CASE (örn. `MAX_RETRY_COUNT`)
  - Özel alanlar için `_` öneki (örn. `_logger`)

- **Kod Düzeni**:
  - Her sınıf ayrı bir dosyada olmalıdır
  - Namespace, using direktifleri, sınıf tanımı sıralamasına uyun
  - Metotlar arasında bir boş satır bırakın
  - Satır uzunluğu 120 karakteri geçmemelidir

- **Yorum Standartları**:
  - Tüm public API'ler için XML belgeleme yorumları kullanın
  - Karmaşık kod blokları için açıklayıcı yorumlar ekleyin
  - TODO yorumları için format: `// TODO: [Açıklama] ([İsim], [Tarih])`

- **Hata Yönetimi**:
  - Uygun try-catch blokları kullanın
  - Hataları günlüğe kaydedin (log4net)
  - Kullanıcıya anlaşılır hata mesajları gösterin

### 5.2 JavaScript Kod Standartları

- **İsimlendirme Kuralları**:
  - Fonksiyonlar ve değişkenler için camelCase (örn. `showNotification`)
  - Sınıflar için PascalCase (örn. `NotificationManager`)
  - Sabitler için UPPER_CASE (örn. `MAX_RETRY_COUNT`)

- **Kod Düzeni**:
  - Modüler JavaScript kullanın
  - İlgili fonksiyonları gruplandırın
  - Fonksiyonlar arasında bir boş satır bırakın
  - Satır uzunluğu 100 karakteri geçmemelidir

- **Yorum Standartları**:
  - Fonksiyonlar için JSDoc yorumları kullanın
  - Karmaşık kod blokları için açıklayıcı yorumlar ekleyin

### 5.3 Kod Analizi

Kodunuzu göndermeden önce aşağıdaki araçları kullanarak analiz edin:

- Visual Studio Code Analysis
- StyleCop (C# için)
- ESLint (JavaScript için)

## 6. Branching Stratejisi

Proje, GitFlow branching modelini takip eder:

- **main**: Kararlı, üretim hazır kod
- **develop**: Geliştirme dalı, bir sonraki sürüm için entegrasyon
- **feature/xxx**: Yeni özellikler için
- **bugfix/xxx**: Hata düzeltmeleri için
- **release/x.x.x**: Sürüm hazırlığı için
- **hotfix/xxx**: Acil üretim hata düzeltmeleri için

Yeni bir özellik veya hata düzeltmesi üzerinde çalışırken:

1. `develop` dalından yeni bir dal oluşturun
2. Değişikliklerinizi yapın ve commit edin
3. Değişikliklerinizi test edin
4. Pull request oluşturun

## 7. Commit Mesajları

Commit mesajları aşağıdaki formata uymalıdır:

```
<type>(<scope>): <subject>

<body>

<footer>
```

### 7.1 Type

- **feat**: Yeni bir özellik
- **fix**: Bir hata düzeltmesi
- **docs**: Sadece dokümantasyon değişiklikleri
- **style**: Kod davranışını etkilemeyen değişiklikler (boşluk, biçimlendirme, vb.)
- **refactor**: Hata düzeltmesi veya özellik eklemeyen kod değişikliği
- **perf**: Performansı artıran bir kod değişikliği
- **test**: Eksik testleri ekleme veya mevcut testleri düzeltme
- **chore**: Derleme süreci veya yardımcı araçlardaki değişiklikler

### 7.2 Scope

Değişikliğin kapsamını belirtir (örn. `android`, `ios`, `ui`, `api`, `db`).

### 7.3 Subject

Değişikliğin kısa bir açıklaması (50 karakter veya daha az).

### 7.4 Body

Değişikliğin daha detaylı açıklaması. Neden ve nasıl değiştirildiğini açıklayın.

### 7.5 Footer

Önemli değişiklikleri belirtin ve kapatılan sorunları referans verin (örn. `Closes #123`).

### 7.6 Örnek

```
feat(android): APK yükleme işlevi eklendi

Kullanıcılar artık sürükle-bırak yöntemiyle APK dosyalarını yükleyebilir.
Bu, test sürecini hızlandıracak ve kullanıcı deneyimini iyileştirecek.

Closes #123
```

## 8. Pull Request Süreci

### 8.1 Pull Request Oluşturma

1. Değişikliklerinizi commit edin ve dalınızı GitHub'a push edin:
   ```bash
   git push origin feature/yeni-ozellik-adi
   ```

2. GitHub'da bir pull request oluşturun:
   - Base: `develop`
   - Compare: `feature/yeni-ozellik-adi`

3. Pull request başlığı ve açıklaması için commit mesajı formatını takip edin

### 8.2 Pull Request İncelemesi

- Pull request'iniz en az bir geliştirici tarafından incelenecektir
- İnceleme yorumlarına göre değişiklikler yapın
- Tüm otomatik testlerin geçtiğinden emin olun
- Kod kalitesi kontrollerini geçtiğinden emin olun

### 8.3 Merge Etme

Pull request'iniz onaylandıktan ve tüm testler geçtikten sonra, bir proje yöneticisi tarafından `develop` dalına merge edilecektir.

## 9. Test Etme

### 9.1 Birim Testleri

- Yeni özellikler ve hata düzeltmeleri için birim testleri yazın
- MSTest veya NUnit kullanın
- Testler `qa_utility.Tests` projesinde olmalıdır
- Test kapsamı en az %70 olmalıdır

### 9.2 Entegrasyon Testleri

- API entegrasyonları için entegrasyon testleri yazın
- Gerçek veritabanı yerine test veritabanı kullanın
- Mock nesneleri kullanarak harici servisleri taklit edin

### 9.3 Manuel Test Planı

- Yeni özellikler için test senaryoları oluşturun
- Değişikliklerinizin yan etkilerini test edin
- Farklı cihazlarda ve işletim sistemlerinde test edin

## 10. Dokümantasyon

### 10.1 Kod Dokümantasyonu

- Tüm public sınıflar, metotlar ve özellikler için XML belgeleme yorumları ekleyin
- Karmaşık algoritmaları açıklayın
- Örnekler ve kullanım senaryoları ekleyin

### 10.2 Kullanıcı Dokümantasyonu

- Yeni özellikler için kullanıcı kılavuzunu güncelleyin
- Ekran görüntüleri ve örnekler ekleyin
- Adım adım talimatlar sağlayın

### 10.3 API Dokümantasyonu

- API değişiklikleri için API referans dokümanını güncelleyin
- Parametreleri, dönüş değerlerini ve örnekleri belirtin
- Hata durumlarını ve işleme stratejilerini açıklayın

## 11. Sürüm Yönetimi

### 11.1 Semantic Versioning

Proje, Semantic Versioning (SemVer) standardını takip eder: `MAJOR.MINOR.PATCH`

- **MAJOR**: Geriye dönük uyumlu olmayan API değişiklikleri
- **MINOR**: Geriye dönük uyumlu yeni özellikler
- **PATCH**: Geriye dönük uyumlu hata düzeltmeleri

### 11.2 Sürüm Notları

- Her sürüm için sürüm notları oluşturun
- Yeni özellikleri, iyileştirmeleri ve hata düzeltmelerini listeleyin
- Önemli değişiklikleri ve yükseltme talimatlarını belirtin

## 12. Sorun Raporlama

### 12.1 Hata Raporları

Bir hata raporu oluştururken aşağıdaki bilgileri sağlayın:

- Hatanın kısa ve net bir açıklaması
- Hatayı yeniden oluşturmak için adımlar
- Beklenen davranış ve gerçek davranış
- Ekran görüntüleri veya videolar (mümkünse)
- Ortam bilgileri (işletim sistemi, uygulama sürümü, cihaz)

### 12.2 Özellik İstekleri

Bir özellik isteği oluştururken aşağıdaki bilgileri sağlayın:

- Özelliğin kısa ve net bir açıklaması
- Özelliğin neden gerekli olduğunu açıklayan bir gerekçe
- Olası uygulama yaklaşımları
- Mockup'lar veya örnekler (mümkünse)

## 13. İletişim

### 13.1 İletişim Kanalları

- **GitHub Issues**: Hata raporları ve özellik istekleri için
- **E-posta**: `<EMAIL>` (geliştirme ekibi)
- **Slack**: `#qa-utility-dev` kanalı (dahili geliştirme ekibi)

### 13.2 Toplantılar

- Haftalık geliştirme toplantıları: Her Çarşamba, 10:00 (Türkiye saati)
- Sprint planlama: Her ayın ilk Pazartesi günü
- Sürüm değerlendirme: Her sürümden sonra

---

Bu katkıda bulunma kılavuzu, Joygame QA Utility projesine katkıda bulunmak isteyenler için temel kuralları ve süreçleri açıklar. Sorularınız veya önerileriniz varsa, lütfen geliştirme ekibiyle iletişime geçin.

Katkılarınız için teşekkür ederiz!