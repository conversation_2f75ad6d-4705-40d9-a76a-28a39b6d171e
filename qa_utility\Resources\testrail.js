// filepath: c:\git\joygame_utility\qa_utility\Resources\testrail.js
// TestRail Integration JavaScript Functions

// Global variables for TestRail
let testRailConnectionStatus = false;
let testRailProjects = [];
let testRailSuites = [];
let testRailRuns = [];
let testRailCases = [];
let selectedProject = null;
let selectedSuite = null;
let testRailSelectedRun = null;

// Initialize TestRail integration
function initTestRail() {
    checkTestRailConnection();

    // Set up event listeners for TestRail UI elements
    document.getElementById('testRailProjectSelect').addEventListener('change', handleProjectChange);
    document.getElementById('testRailSuiteSelect').addEventListener('change', handleSuiteChange);
    document.getElementById('testRailSearch').addEventListener('input', handleTestRailRunSearch);
    document.getElementById('testRailRefreshBtn').addEventListener('click', refreshTestRailData);
}

// Check TestRail connection status
function checkTestRailConnection() {
    // Update UI to show checking status
    updateConnectionStatus('checking');
    
    // Call C# method to check TestRail connection
    window.chrome.webview.postMessage({
        action: 'checkTestRailConnection',
        type:'testrail',
    });
}

// Load TestRail projects
function loadTestRailProjects() {
    // Clear project select dropdown
    const projectSelect = document.getElementById('testRailProjectSelect');
    projectSelect.innerHTML = '<option value="">Loading projects...</option>';
    
    // Call C# method to load projects
    window.chrome.webview.postMessage({
        action: 'getTestRailProjects',
        type:'testrail',
    });
}

// Handle TestRail project change
function handleProjectChange() {
    const projectId = document.getElementById('testRailProjectSelect').value;
    
    if (!projectId) {
        // No project selected
        clearTestRailUI();
        return;
    }
    
    selectedProject = testRailProjects.find(p => p.id == projectId);
    
    // Show project info
    showProjectInfo(selectedProject);
    
    // Load suites for the selected project
    loadTestRailSuites(projectId);
    
    // Load test runs for the selected project
    loadTestRailRuns(projectId);
}

// Load TestRail suites for a project
function loadTestRailSuites(projectId) {
    // Clear suite select dropdown
    const suiteSelect = document.getElementById('testRailSuiteSelect');
    suiteSelect.innerHTML = '<option value="">Loading suites...</option>';
    
    // Show suite container
    document.getElementById('testRailSuiteContainer').style.display = 'block';
    
    // Call C# method to load suites
    window.chrome.webview.postMessage({
        action: 'getTestRailSuites',
        type:'testrail',
        projectId: projectId
    });
}

// Load TestRail runs for a project
function loadTestRailRuns(projectId) {
    // Call C# method to load runs
    window.chrome.webview.postMessage({
        action: 'getTestRailRuns',
        type:'testrail',
        projectId: projectId
    });
}

// Handle TestRail suite change
function handleSuiteChange() {
    const projectId = document.getElementById('testRailProjectSelect').value;
    const suiteId = document.getElementById('testRailSuiteSelect').value;
    
    if (!projectId) {
        return;
    }
    
    // Note: Test cases functionality is disabled for now
    // loadTestRailCases(projectId, suiteId);
}

// Handle TestRail run selection from the table
function selectTestRailRun(runId) {
    if (!runId) {
        testRailSelectedRun = null;
        return;
    }
    
    testRailSelectedRun = testRailRuns.find(r => r.id == runId);
    
    // Test case table functionality is disabled
    // if (testRailCases.length > 0) {
    //     displayTestCases(testRailCases);
    // }
    
    // Highlight the selected row in the table
    const rows = document.querySelectorAll('#testRailRunsTableBody tr');
    rows.forEach(row => {
        if (row.dataset.runId == runId) {
            row.classList.add('bg-gray-600');
        } else {
            row.classList.remove('bg-gray-600');
        }
    });
}

// Load TestRail cases for a project and suite
function loadTestRailCases(projectId, suiteId) {
    
    // Call C# method to load cases
    window.chrome.webview.postMessage({
        action: 'getTestRailCases',
        type:'testrail',
        projectId: projectId,
        suiteId: suiteId || null
    });
}

// Handle TestRail search
function handleTestRailSearch() {
    const searchText = document.getElementById('testRailSearch').value.toLowerCase();
    
    // Filter the test cases based on the search text
    const filteredCases = testRailCases.filter(c => 
        c.title.toLowerCase().includes(searchText) || 
        c.id.toString().includes(searchText)
    );
    
    // Display the filtered cases
    displayTestCases(filteredCases);
}

// Refresh TestRail data
function refreshTestRailData() {
    const projectId = document.getElementById('testRailProjectSelect').value;
    const suiteId = document.getElementById('testRailSuiteSelect').value;
    
    if (!projectId) {
        return;
    }
    
    // Reload everything
    //loadTestRailCases(projectId, suiteId);
    loadTestRailRuns(projectId);
}

// Display TestRail projects in the dropdown
function displayTestRailProjects(projects) {
    testRailProjects = projects;
    
    const projectSelect = document.getElementById('testRailProjectSelect');
    projectSelect.innerHTML = '<option value="">Select a project</option>';
    
    projects.forEach(project => {
        const option = document.createElement('option');
        option.value = project.id;
        option.textContent = project.name;
        projectSelect.appendChild(option);
    });
    
    // Enable the project select
    projectSelect.disabled = false;
    
    // Enable the refresh button
    document.getElementById('testRailRefreshBtn').disabled = false;
}

// Display TestRail suites in the dropdown
function displayTestRailSuites(suites) {
    testRailSuites = suites;
    
    const suiteSelect = document.getElementById('testRailSuiteSelect');
    suiteSelect.innerHTML = '<option value="">All Suites</option>';
    
    suites.forEach(suite => {
        const option = document.createElement('option');
        option.value = suite.id;
        option.textContent = suite.name;
        suiteSelect.appendChild(option);
    });
    
    // Show the suite container
    document.getElementById('testRailSuiteContainer').style.display = 'block';
}

// Display TestRail runs in the table
function displayTestRailRuns(runs) {
    testRailRuns = runs;
    
    // Now display runs in the table
    const runsTableBody = document.getElementById('testRailRunsTableBody');
    runsTableBody.innerHTML = '';
    
    if (runs.length === 0) {
        runsTableBody.innerHTML = `
            <tr>
                <td colspan="6" class="px-4 py-8 text-center text-gray-400">
                    <i class="fas fa-info-circle mr-2"></i>
                    No test runs found
                </td>
            </tr>
        `;
        return;
    }
    
    runs.forEach(run => {
        const tr = document.createElement('tr');
        tr.className = 'hover:bg-gray-700 transition-colors';
        
        // ID Column
        const idTd = document.createElement('td');
        idTd.className = 'px-4 py-3 text-gray-300';
        idTd.textContent = run.id;
        tr.appendChild(idTd);
        
        // Name Column
        const nameTd = document.createElement('td');
        nameTd.className = 'px-4 py-3 text-gray-300';
        nameTd.textContent = run.name;
        tr.appendChild(nameTd);
        
        // Created On Column
        const createdOnTd = document.createElement('td');
        createdOnTd.className = 'px-4 py-3 text-gray-300';
        createdOnTd.textContent = new Date(run.created_on * 1000).toLocaleString();
        tr.appendChild(createdOnTd);
        
        // Completed Column
        const completedTd = document.createElement('td');
        completedTd.className = 'px-4 py-3 text-gray-300';
        const completedPercentage = run.completed_on ? '100%' : 
            (run.passed_count + run.failed_count + run.blocked_count + run.retest_count) * 100 / 
            (run.passed_count + run.failed_count + run.blocked_count + run.retest_count + run.untested_count);
        completedTd.textContent = completedPercentage ? Math.round(completedPercentage) + '%' : '0%';
        tr.appendChild(completedTd);
        
        // Status Column
        const statusTd = document.createElement('td');
        statusTd.className = 'px-4 py-3';
        
        const statusDiv = document.createElement('div');
        statusDiv.className = 'flex items-center space-x-1';
        
        if (run.passed_count > 0) {
            const passedBadge = document.createElement('span');
            passedBadge.className = 'px-2 py-1 bg-green-600 text-white text-xs rounded';
            passedBadge.textContent = `${run.passed_count} passed`;
            statusDiv.appendChild(passedBadge);
        }
        
        if (run.failed_count > 0) {
            const failedBadge = document.createElement('span');
            failedBadge.className = 'px-2 py-1 bg-red-600 text-white text-xs rounded';
            failedBadge.textContent = `${run.failed_count} failed`;
            statusDiv.appendChild(failedBadge);
        }
        
        if (run.blocked_count > 0) {
            const blockedBadge = document.createElement('span');
            blockedBadge.className = 'px-2 py-1 bg-yellow-600 text-white text-xs rounded';
            blockedBadge.textContent = `${run.blocked_count} blocked`;
            statusDiv.appendChild(blockedBadge);
        }
        
        if (run.retest_count > 0) {
            const retestBadge = document.createElement('span');
            retestBadge.className = 'px-2 py-1 bg-orange-600 text-white text-xs rounded';
            retestBadge.textContent = `${run.retest_count} retest`;
            statusDiv.appendChild(retestBadge);
        }
        
        if (run.untested_count > 0) {
            const untestedBadge = document.createElement('span');
            untestedBadge.className = 'px-2 py-1 bg-gray-500 text-white text-xs rounded';
            untestedBadge.textContent = `${run.untested_count} untested`;
            statusDiv.appendChild(untestedBadge);
        }
        
        statusTd.appendChild(statusDiv);
        tr.appendChild(statusTd);
        
        // Actions Column
        const actionsTd = document.createElement('td');
        actionsTd.className = 'px-4 py-3 text-gray-300';
        
        const viewButton = document.createElement('button');
        viewButton.className = 'bg-custom hover:bg-custom/90 text-white px-3 py-1 rounded mr-2';
        viewButton.textContent = 'View';
        viewButton.addEventListener('click', () => openurl("https://joygame.testrail.io/index.php?/runs/view/" + run.id.toString()));
        actionsTd.appendChild(viewButton);
        
        tr.appendChild(actionsTd);
          // Add data-runId attribute to the row for easy selection
        tr.dataset.runId = run.id;
        
        // Make the entire row clickable to select this run
        tr.addEventListener('click', () => selectTestRailRun(run.id));
        
        // Add the row to the table
        runsTableBody.appendChild(tr);
    });
}
// handleTestRailRunSearch
function handleTestRailRunSearch() {
    const searchText = document.getElementById('testRailSearch').value.toLowerCase();
    
    // If the search text is empty, display all runs instead of filtering
    if (!searchText) {
        displayTestRailRuns(testRailRuns);
        return;
    }
    
    const filteredRuns = testRailRuns.filter(run => run.name.toLowerCase().includes(searchText));
    displayTestRailRuns(filteredRuns);
}


// Display TestRail cases in the table
function displayTestCases(cases) {
    testRailCases = cases;
    
    // This function is disabled as the testRailCasesTableBody element doesn't exist anymore
    // console.log('Received ' + cases.length + ' test cases');
    
    if (cases.length === 0) {
        
        return;
    }
    
    cases.forEach(testCase => {
        const tr = document.createElement('tr');
        tr.className = 'hover:bg-gray-700 transition-colors';
        
        // ID Column
        const idTd = document.createElement('td');
        idTd.className = 'px-4 py-3 text-gray-300';
        idTd.textContent = testCase.id;
        tr.appendChild(idTd);
        
        // Title Column
        const titleTd = document.createElement('td');
        titleTd.className = 'px-4 py-3 text-gray-300';
        titleTd.textContent = testCase.title;
        tr.appendChild(titleTd);
        
        // Suite Column
        const suiteTd = document.createElement('td');
        suiteTd.className = 'px-4 py-3 text-gray-400';
        const suite = testRailSuites.find(s => s.id === testCase.suiteId);
        suiteTd.textContent = suite ? suite.name : 'N/A';
        tr.appendChild(suiteTd);
        
        // Priority Column
        const priorityTd = document.createElement('td');
        priorityTd.className = 'px-4 py-3';
        
        // Placeholder for priority
        let priorityBadge = document.createElement('span');
        priorityBadge.className = 'px-2 py-1 rounded text-xs font-medium';
        
        // Set priority color based on priority_id
        if (testCase.priorityId) {
            switch(testCase.priorityId) {
                case 1: // Critical
                    priorityBadge.className += ' bg-red-800 text-red-100';
                    priorityBadge.textContent = 'Critical';
                    break;
                case 2: // High
                    priorityBadge.className += ' bg-orange-800 text-orange-100';
                    priorityBadge.textContent = 'High';
                    break;
                case 3: // Medium
                    priorityBadge.className += ' bg-yellow-700 text-yellow-100';
                    priorityBadge.textContent = 'Medium';
                    break;
                case 4: // Low
                    priorityBadge.className += ' bg-green-700 text-green-100';
                    priorityBadge.textContent = 'Low';
                    break;
                default:
                    priorityBadge.className += ' bg-gray-700 text-gray-100';
                    priorityBadge.textContent = 'Unknown';
            }
        } else {
            priorityBadge.className += ' bg-gray-700 text-gray-100';
            priorityBadge.textContent = 'N/A';
        }
        
        priorityTd.appendChild(priorityBadge);
        tr.appendChild(priorityTd);
        
        // Type Column
        const typeTd = document.createElement('td');
        typeTd.className = 'px-4 py-3 text-gray-400';
        
        // Placeholder for type
        if (testCase.typeId) {
            switch(testCase.typeId) {
                case 1:
                    typeTd.textContent = 'Functional';
                    break;
                case 2:
                    typeTd.textContent = 'Performance';
                    break;
                case 3:
                    typeTd.textContent = 'UI/UX';
                    break;
                default:
                    typeTd.textContent = 'Other';
            }
        } else {
            typeTd.textContent = 'N/A';
        }
        
        tr.appendChild(typeTd);
        
        // Actions Column
        const actionsTd = document.createElement('td');
        actionsTd.className = 'px-4 py-3';
        
        // View Details Button
        const viewBtn = document.createElement('button');
        viewBtn.className = 'bg-gray-600 hover:bg-gray-500 text-gray-100 px-3 py-1 rounded mr-2';
        viewBtn.innerHTML = '<i class="fas fa-eye mr-1"></i> View';
        viewBtn.addEventListener('click', () => showTestCaseDetails(testCase));
        actionsTd.appendChild(viewBtn);
        
        // Add Result Button (Only visible if run is selected)
        if (testRailSelectedRun) {
            const addResultBtn = document.createElement('button');
            addResultBtn.className = 'bg-custom hover:bg-custom/90 text-white px-3 py-1 rounded';
            addResultBtn.innerHTML = '<i class="fas fa-plus mr-1"></i> Add Result';
            addResultBtn.addEventListener('click', () => showAddResultDialog(testRailSelectedRun.id, testCase.id));
            actionsTd.appendChild(addResultBtn);
        }
        
        tr.appendChild(actionsTd);
        
        casesTableBody.appendChild(tr);
    });
}

// Show project information in the UI
function showProjectInfo(project) {
    const projectInfoDiv = document.getElementById('testRailProjectInfo');
    const projectDetailsDiv = document.getElementById('testRailProjectDetails');
    
    projectDetailsDiv.innerHTML = `
        <div class="mb-1"><strong>Name:</strong> ${project.name}</div>
        ${project.announcement ? `<div class="mb-1"><strong>Announcement:</strong> ${project.announcement}</div>` : ''}
        <div class="mb-1"><strong>Status:</strong> ${project.isCompleted ? 'Completed' : 'Active'}</div>
        ${project.url ? `<div><a onclick="openurl('${project.url}')" class="text-custom hover:underline">Open in TestRail <i class="fas fa-external-link-alt text-xs"></i></a></div>` : ''}
    `;
    
    projectInfoDiv.style.display = 'block';
}

function openurl(url) {
    window.chrome.webview.postMessage({
        action: 'openurl',
        type: 'testrail',
        url: url
    });
}


// Show test case details in a modal
function showTestCaseDetails(testCase) {
    // Create modal in DOM if it doesn't exist
    let detailsModal = document.getElementById('testCaseDetailsModal');
    
    if (!detailsModal) {
        detailsModal = document.createElement('div');
        detailsModal.id = 'testCaseDetailsModal';
        detailsModal.className = 'fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center hidden';
        detailsModal.innerHTML = `
            <div class="bg-gray-800 p-6 rounded shadow-lg max-w-4xl w-full max-h-[90vh] overflow-auto">
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-xl font-semibold text-gray-100" id="modalTestCaseTitle"></h2>
                    <button id="closeTestCaseDetailsModal" class="text-gray-400 hover:text-gray-200">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div id="testCaseDetailsContent"></div>
            </div>
        `;
        
        document.body.appendChild(detailsModal);
        
        // Close modal event
        document.getElementById('closeTestCaseDetailsModal').addEventListener('click', () => {
            detailsModal.classList.add('hidden');
        });
    }
    
    // Update modal content
    document.getElementById('modalTestCaseTitle').textContent = `Test Case #${testCase.id}: ${testCase.title}`;
    
    const detailsContent = document.getElementById('testCaseDetailsContent');
    detailsContent.innerHTML = `
        <div class="grid grid-cols-2 gap-4 mb-6">
            <div class="col-span-2 lg:col-span-1">
                <div class="mb-4">
                    <h3 class="font-medium text-gray-300 mb-2">Details</h3>
                    <div class="bg-gray-700 p-3 rounded">
                        <div class="mb-1"><strong>ID:</strong> ${testCase.id}</div>
                        <div class="mb-1"><strong>Suite:</strong> ${testRailSuites.find(s => s.id === testCase.suiteId)?.name || 'N/A'}</div>
                        <div class="mb-1">
                            <strong>Priority:</strong> 
                            ${getPriorityBadge(testCase.priorityId)}
                        </div>
                        <div class="mb-1"><strong>Type:</strong> ${getTestCaseType(testCase.typeId)}</div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="mb-6">
            <h3 class="font-medium text-gray-300 mb-2">Preconditions</h3>
            <div class="bg-gray-700 p-3 rounded whitespace-pre-wrap text-gray-300">
                ${testCase.preconditions || 'No preconditions specified'}
            </div>
        </div>
        
        <div class="mb-6">
            <h3 class="font-medium text-gray-300 mb-2">Steps</h3>
            <div class="bg-gray-700 p-3 rounded whitespace-pre-wrap text-gray-300">
                ${testCase.steps || 'No steps specified'}
            </div>
        </div>
        
        <div>
            <h3 class="font-medium text-gray-300 mb-2">Expected Result</h3>
            <div class="bg-gray-700 p-3 rounded whitespace-pre-wrap text-gray-300">
                ${testCase.expectedResult || 'No expected result specified'}
            </div>
        </div>
    `;
    
    // Show modal
    detailsModal.classList.remove('hidden');
}

// Get priority badge HTML
function getPriorityBadge(priorityId) {
    let badgeClass = '';
    let badgeText = '';
    
    if (priorityId) {
        switch(priorityId) {
            case 1: // Critical
                badgeClass = 'bg-red-800 text-red-100';
                badgeText = 'Critical';
                break;
            case 2: // High
                badgeClass = 'bg-orange-800 text-orange-100';
                badgeText = 'High';
                break;
            case 3: // Medium
                badgeClass = 'bg-yellow-700 text-yellow-100';
                badgeText = 'Medium';
                break;
            case 4: // Low
                badgeClass = 'bg-green-700 text-green-100';
                badgeText = 'Low';
                break;
            default:
                badgeClass = 'bg-gray-700 text-gray-100';
                badgeText = 'Unknown';
        }
    } else {
        badgeClass = 'bg-gray-700 text-gray-100';
        badgeText = 'N/A';
    }
    
    return `<span class="px-2 py-1 rounded text-xs font-medium ${badgeClass}">${badgeText}</span>`;
}

// Get test case type text
function getTestCaseType(typeId) {
    if (typeId) {
        switch(typeId) {
            case 1: return 'Functional';
            case 2: return 'Performance';
            case 3: return 'UI/UX';
            default: return 'Other';
        }
    }
    return 'N/A';
}

// Show dialog to add a test result
function showAddResultDialog(runId, caseId) {
    // Create modal in DOM if it doesn't exist
    let addResultModal = document.getElementById('addTestResultModal');
    
    if (!addResultModal) {
        addResultModal = document.createElement('div');
        addResultModal.id = 'addTestResultModal';
        addResultModal.className = 'fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center hidden';
        addResultModal.innerHTML = `
            <div class="bg-gray-800 p-6 rounded shadow-lg max-w-md w-full">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-lg font-semibold text-gray-100">Add Test Result</h2>
                    <button id="closeAddResultModal" class="text-gray-400 hover:text-gray-200">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                
                <form id="addResultForm">
                    <div class="mb-4">
                        <label class="block text-gray-300 mb-2">Status</label>
                        <select id="testResultStatus" class="w-full bg-gray-700 border-gray-600 rounded text-gray-100 px-3 py-2">
                            <option value="1">Passed</option>
                            <option value="2">Blocked</option>
                            <option value="3">Untested</option>
                            <option value="4">Retest</option>
                            <option value="5">Failed</option>
                        </select>
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-gray-300 mb-2">Comment</label>
                        <textarea id="testResultComment" rows="4" class="w-full bg-gray-700 border-gray-600 rounded text-gray-100 px-3 py-2" placeholder="Enter comments..."></textarea>
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-gray-300 mb-2">Version</label>
                        <input type="text" id="testResultVersion" class="w-full bg-gray-700 border-gray-600 rounded text-gray-100 px-3 py-2" placeholder="e.g. 1.2.3">
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-gray-300 mb-2">Elapsed Time</label>
                        <input type="text" id="testResultElapsed" class="w-full bg-gray-700 border-gray-600 rounded text-gray-100 px-3 py-2" placeholder="e.g. 30s, 1m 45s, 2h 30m">
                    </div>
                    
                    <div class="mb-6">
                        <label class="block text-gray-300 mb-2">Defects</label>
                        <input type="text" id="testResultDefects" class="w-full bg-gray-700 border-gray-600 rounded text-gray-100 px-3 py-2" placeholder="e.g. BUG-123, BUG-456">
                    </div>
                    
                    <div class="flex justify-end">
                        <button type="button" id="cancelAddResult" class="bg-gray-600 hover:bg-gray-500 text-white px-4 py-2 rounded mr-2">Cancel</button>
                        <button type="submit" id="submitAddResult" class="bg-custom hover:bg-custom/90 text-white px-4 py-2 rounded">Submit Result</button>
                    </div>
                </form>
            </div>
        `;
        
        document.body.appendChild(addResultModal);
        
        // Close modal events
        document.getElementById('closeAddResultModal').addEventListener('click', () => {
            addResultModal.classList.add('hidden');
        });
        
        document.getElementById('cancelAddResult').addEventListener('click', () => {
            addResultModal.classList.add('hidden');
        });
        
        // Submit form event
        document.getElementById('addResultForm').addEventListener('submit', (e) => {
            e.preventDefault();
            submitTestResult();
        });
    }
    
    // Set the current runId and caseId as data attributes
    addResultModal.dataset.runId = runId;
    addResultModal.dataset.caseId = caseId;
    
    // Reset form fields
    document.getElementById('testResultStatus').value = '1';
    document.getElementById('testResultComment').value = '';
    document.getElementById('testResultVersion').value = '';
    document.getElementById('testResultElapsed').value = '';
    document.getElementById('testResultDefects').value = '';
    
    // Show modal
    addResultModal.classList.remove('hidden');
}

// Submit test result to TestRail
function submitTestResult() {
    const modal = document.getElementById('addTestResultModal');
    const runId = modal.dataset.runId;
    const caseId = modal.dataset.caseId;
    
    const result = {
        statusId: parseInt(document.getElementById('testResultStatus').value),
        comment: document.getElementById('testResultComment').value,
        version: document.getElementById('testResultVersion').value,
        elapsed: document.getElementById('testResultElapsed').value,
        defects: document.getElementById('testResultDefects').value
    };
    
    // Call C# method to add result
    window.chrome.webview.postMessage({
        action: 'addTestRailResult',
        type: 'testrail',
        runId: runId,
        caseId: caseId,
        result: result
    });
    
    // Hide modal
    modal.classList.add('hidden');
    
    // Show success notification
    showNotification('Result submitted successfully!', 'success');
}

// Update connection status in the UI
function updateConnectionStatus(status) {
    const statusIcon = document.getElementById('testRailStatusIcon');
    const statusText = document.getElementById('testRailStatusText');
    const projectSelect = document.getElementById('testRailProjectSelect');
    
    switch(status) {
        case 'connected':
            statusIcon.className = 'w-3 h-3 rounded-full bg-green-500 mr-2';
            statusText.textContent = 'Connected to TestRail';
            statusText.className = 'text-sm text-green-400';
            projectSelect.disabled = false;
            testRailConnectionStatus = true;
            loadTestRailProjects();
            break;
            
        case 'disconnected':
            statusIcon.className = 'w-3 h-3 rounded-full bg-red-500 mr-2';
            statusText.textContent = 'Could not connect to TestRail';
            statusText.className = 'text-sm text-red-400';
            projectSelect.disabled = true;
            testRailConnectionStatus = false;
            break;
            
        default: // checking
            statusIcon.className = 'w-3 h-3 rounded-full bg-gray-400 mr-2';
            statusText.textContent = 'Checking connection...';
            statusText.className = 'text-sm text-gray-300';
            projectSelect.disabled = true;
            break;
    }
}

// Clear TestRail UI
function clearTestRailUI() {
    // Clear all TestRail-related data
    document.getElementById('testRailSuiteContainer').style.display = 'none';
    document.getElementById('testRailRunContainer').style.display = 'none';
    document.getElementById('testRailProjectInfo').style.display = 'none';
    
    const casesTableBody = document.getElementById('testRailCasesTableBody');
    casesTableBody.innerHTML = `
        <tr>
            <td colspan="6" class="px-4 py-8 text-center text-gray-400">
                <i class="fas fa-info-circle mr-2"></i>
                Select a project to view test cases
            </td>
        </tr>
    `;
    
    testRailSuites = [];
    testRailCases = [];
    testRailRuns = [];
    selectedProject = null;
    selectedSuite = null;
    testRailSelectedRun = null;
}

// Show notification
function showNotification2(message, type = 'info') {
    let notificationDiv = document.createElement('div');
    notificationDiv.className = 'fixed top-4 right-4 p-4 rounded shadow-lg z-50';
    
    // Set background and text colors based on notification type
    if (type === 'success') {
        notificationDiv.className += ' bg-green-600 text-white';
    } else if (type === 'error') {
        notificationDiv.className += ' bg-red-600 text-white';
    } else {
        notificationDiv.className += ' bg-blue-600 text-white';
    }
    
    notificationDiv.innerHTML = `
        <div class="flex items-center">
            <i class="fas ${type === 'success' ? 'fa-check-circle' : type === 'error' ? 'fa-exclamation-circle' : 'fa-info-circle'} mr-2"></i>
            <span>${message}</span>
        </div>
    `;
    
    document.body.appendChild(notificationDiv);
    
    // Remove notification after 3 seconds
    setTimeout(() => {
        notificationDiv.classList.add('opacity-0');
        notificationDiv.style.transition = 'opacity 0.5s';
        
        setTimeout(() => {
            notificationDiv.remove();
        }, 500);
    }, 3000);
}

// Handle messages from C#
function handleTestRailMessage(data) {
    // console.log('Received TestRail message:', data.action);
    
    switch(data.action) {
        case 'connectionStatus':
            updateConnectionStatus(data.status === 'true' ? 'connected' : 'disconnected');
            break;
            
        case 'projectsLoaded':
            displayTestRailProjects(data.projects);
            break;
            
        case 'suitesLoaded':
            displayTestRailSuites(data.suites);
            break;
            
        case 'runsLoaded':
            displayTestRailRuns(data.runs);
            break;
            
        case 'casesLoaded':
            displayTestCases(data.cases);
            break;
            
        case 'error':
            console.error('TestRail error:', data.message);
            showNotification(data.message, 'error');
            break;
    }
}
