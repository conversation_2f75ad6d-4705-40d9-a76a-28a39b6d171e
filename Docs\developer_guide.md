# Joygame QA Utility - Geliştirici Kılavuzu

## İçindekiler

1. [Geliştirme Ortamının <PERSON>](#1-geliştirme-ortamının-kurulumu)
2. [<PERSON><PERSON>](#2-proje-yap<PERSON><PERSON><PERSON>)
3. [<PERSON><PERSON><PERSON>](#3-mimar<PERSON>-genel-bak<PERSON>)
4. [<PERSON><PERSON>](#4-kod-standartları)
5. [Geliştirme İş Akışı](#5-geliştirme-iş-akışı)
6. [Test Etme](#6-test-etme)
7. [<PERSON><PERSON><PERSON> ve <PERSON>tı<PERSON>](#7-derleme-ve-dağıtım)
8. [API Referansları](#8-api-referansları)
9. [Sorun Giderme](#9-sorun-giderme)

## 1. Geliştirme Ortamının Kurulumu

### 1.1 Gerekli Yazılımlar

- **Visual Studio 2019** veya üzeri (Community Edition yeterlidir)
- **Git** (<PERSON><PERSON><PERSON><PERSON>m kontrolü i<PERSON>)
- **.NET Framework 4.7.2** SDK
- **MySQL Server** (<PERSON><PERSON> geliştirme için)
- **Node.js** ve **npm** (WebView2 içeriği geliştirmek için, isteğe bağlı)

### 1.2 Geliştirme Ortamı Kurulumu

1. Visual Studio'yu kurun ve aşağıdaki iş yüklerini ekleyin:
   - .NET Desktop Development
   - Universal Windows Platform development
   - Web development tools

2. Git'i kurun ve yapılandırın:
   ```bash
   git config --global user.name "Adınız"
   git config --global user.email "<EMAIL>"
   ```

3. Projeyi klonlayın:
   ```bash
   git clone https://github.com/joygame/joygame_utility.git
   cd joygame_utility
   ```

4. Visual Studio'da `QA Utility.sln` dosyasını açın.

5. NuGet paketlerini geri yükleyin:
   - Solution Explorer'da çözüme sağ tıklayın
   - "Restore NuGet Packages" seçeneğini seçin

6. Projeyi derleyin (F6 veya Build > Build Solution).

### 1.3 Veritabanı Kurulumu

1. MySQL Server'ı kurun ve yapılandırın.

2. Veritabanını oluşturun:
   ```sql
   CREATE DATABASE joygame_qa_utility;
   ```

3. Veritabanı kullanıcısı oluşturun ve izinleri ayarlayın:
   ```sql
   CREATE USER 'qa_user'@'localhost' IDENTIFIED BY 'password';
   GRANT ALL PRIVILEGES ON joygame_qa_utility.* TO 'qa_user'@'localhost';
   FLUSH PRIVILEGES;
   ```

4. Veritabanı şemasını oluşturun (SQL betikleri varsa).

5. `App.config` dosyasında veritabanı bağlantı dizesini güncelleyin.

## 2. Proje Yapısı

### 2.1 Çözüm Yapısı

```
joygame_utility/
├── QA Utility.sln           # Visual Studio çözüm dosyası
├── AutoUpdater/             # Otomatik güncelleme bileşeni
│   ├── Form1.cs             # Ana form
│   ├── Program.cs           # Giriş noktası
│   └── ...                  # Diğer dosyalar
├── qa_utility/              # Ana QA Utility uygulaması
│   ├── Form1.cs             # Ana form
│   ├── Program.cs           # Giriş noktası
│   ├── ApkUploaderForm.cs   # APK yükleme formu
│   ├── Resources/           # Web arayüzü kaynakları
│   │   ├── index.html       # Ana HTML sayfası
│   │   ├── scripts.js       # JavaScript dosyaları
│   │   └── ...              # Diğer web kaynakları
│   ├── classes/             # Yardımcı sınıflar ve servisler
│   │   ├── GoogleApiService.cs
│   │   ├── LdapAuthenticator.cs
│   │   ├── NvidiaShadowPlayHelper.cs
│   │   └── ...              # Diğer sınıflar
│   ├── ioslog/              # iOS log araçları
│   ├── platform-tools/      # Android platform araçları
│   └── ...                  # Diğer dosyalar
└── Docs/                    # Dokümantasyon
```

### 2.2 Önemli Bileşenler

- **Form1.cs**: Ana uygulama formu, WebView2 kontrolünü içerir ve JavaScript-C# iletişimini yönetir.
- **classes/**: Yardımcı sınıflar, servisler ve API istemcileri.
- **Resources/**: WebView2 tarafından kullanılan HTML, CSS ve JavaScript dosyaları.
- **AutoUpdater/**: Otomatik güncelleme bileşeni.

## 3. Mimari Genel Bakış

### 3.1 Uygulama Mimarisi

Joygame QA Utility, aşağıdaki ana bileşenlerden oluşan bir Windows Forms uygulamasıdır:

1. **Kullanıcı Arayüzü Katmanı**:
   - Windows Forms (Form1.cs)
   - WebView2 (HTML/CSS/JavaScript)

2. **İş Mantığı Katmanı**:
   - Servisler (GoogleApiService, LdapAuthenticator, vb.)
   - Yardımcı Sınıflar (NvidiaShadowPlayHelper, vb.)

3. **Veri Erişim Katmanı**:
   - MySQL Connector
   - Dosya Sistemi Erişimi

4. **Entegrasyon Katmanı**:
   - TestRail API İstemcisi
   - Redmine API İstemcisi
   - Google API İstemcisi

5. **Mobil Cihaz İletişim Katmanı**:
   - ADB (Android Debug Bridge)
   - libimobiledevice (iOS)

### 3.2 WebView2 ve C# İletişimi

Uygulama, modern web teknolojileri ile .NET Framework'ü birleştiren hibrit bir mimari kullanır:

1. **WebView2**: Modern web arayüzünü (HTML, CSS, JavaScript) görüntüler.

2. **JavaScript-C# İletişimi**:
   - JavaScript'ten C#'a: `window.chrome.webview.postMessage()`
   - C#'tan JavaScript'e: `webView.ExecuteScriptAsync()`

3. **Mesaj İşleme**: Form1.cs içindeki `WebView_WebMessageReceived` metodu, WebView2'den gelen mesajları işler.

## 4. Kod Standartları

### 4.1 C# Kod Standartları

- **İsimlendirme Kuralları**:
  - Sınıflar ve metotlar için PascalCase (örn. `GoogleApiService`)
  - Değişkenler için camelCase (örn. `deviceInfo`)
  - Sabitler için UPPER_CASE (örn. `MAX_RETRY_COUNT`)

- **Kod Düzeni**:
  - Her sınıf ayrı bir dosyada olmalıdır
  - Namespace, using direktifleri, sınıf tanımı sıralamasına uyun
  - Metotlar arasında bir boş satır bırakın

- **Yorum Standartları**:
  - Tüm public API'ler için XML belgeleme yorumları kullanın
  - Karmaşık kod blokları için açıklayıcı yorumlar ekleyin

- **Hata Yönetimi**:
  - Uygun try-catch blokları kullanın
  - Hataları günlüğe kaydedin (log4net)
  - Kullanıcıya anlaşılır hata mesajları gösterin

### 4.2 JavaScript Kod Standartları

- **İsimlendirme Kuralları**:
  - Fonksiyonlar ve değişkenler için camelCase (örn. `showNotification`)
  - Sınıflar için PascalCase (örn. `NotificationManager`)

- **Kod Düzeni**:
  - Modüler JavaScript kullanın
  - İlgili fonksiyonları gruplandırın
  - Fonksiyonlar arasında bir boş satır bırakın

- **Yorum Standartları**:
  - Fonksiyonlar için JSDoc yorumları kullanın
  - Karmaşık kod blokları için açıklayıcı yorumlar ekleyin

## 5. Geliştirme İş Akışı

### 5.1 Branching Stratejisi

- **main**: Kararlı, üretim hazır kod
- **develop**: Geliştirme dalı, bir sonraki sürüm için entegrasyon
- **feature/xxx**: Yeni özellikler için
- **bugfix/xxx**: Hata düzeltmeleri için
- **release/x.x.x**: Sürüm hazırlığı için

### 5.2 Commit Mesajları

Commit mesajları aşağıdaki formata uymalıdır:

```
<type>(<scope>): <subject>

<body>

<footer>
```

Örnek:
```
feat(android): APK yükleme işlevi eklendi

Kullanıcılar artık sürükle-bırak yöntemiyle APK dosyalarını yükleyebilir.

Resolves: #123
```

### 5.3 Pull Request Süreci

1. Feature/bugfix dalınızı develop dalından oluşturun
2. Değişikliklerinizi yapın ve commit edin
3. Kodunuzu test edin
4. Pull request oluşturun
5. Code review sürecini tamamlayın
6. Onaylandıktan sonra merge edin

## 6. Test Etme

### 6.1 Birim Testleri

- MSTest veya NUnit kullanarak birim testleri yazın
- Testler `qa_utility.Tests` projesinde olmalıdır
- Her public sınıf ve metot için test yazın

### 6.2 Entegrasyon Testleri

- API entegrasyonları için entegrasyon testleri yazın
- Gerçek veritabanı yerine test veritabanı kullanın
- Mock nesneleri kullanarak harici servisleri taklit edin

### 6.3 Manuel Test Planı

- Yeni özellikler için test senaryoları oluşturun
- Regresyon testleri için kontrol listesi kullanın
- Hata raporlarını Redmine'da belgelendirin

## 7. Derleme ve Dağıtım

### 7.1 Derleme Süreci

1. Sürüm numarasını `AssemblyInfo.cs` dosyasında güncelleyin
2. Release yapılandırmasında projeyi derleyin
3. Çıktı dosyalarını `bin/Release` klasöründen alın

### 7.2 Dağıtım Paketi Oluşturma

1. Gerekli tüm dosyaları içeren bir dağıtım klasörü oluşturun
2. Bağımlılıkları ekleyin (DLL'ler, platform-tools, ioslog)
3. Yapılandırma dosyalarını güncelleyin
4. Kurulum paketi oluşturun (MSI veya Setup.exe)

### 7.3 Güncelleme Sunucusu

1. Derlenen dosyaları FTP sunucusuna yükleyin
2. Sürüm bilgisi dosyasını güncelleyin
3. Değişiklik günlüğünü güncelleyin

## 8. API Referansları

### 8.1 TestRail API

- **Kütüphane**: Redmine.Net.Api
- **Endpoint**: https://your-instance.testrail.io/
- **Kimlik Doğrulama**: API Anahtarı
- **Yaygın Kullanım Durumları**:
  - Test durumlarını alma
  - Test sonuçlarını gönderme
  - Test planlarını yönetme

### 8.2 Redmine API

- **Kütüphane**: redmine-net-api
- **Endpoint**: https://your-redmine-instance.com/
- **Kimlik Doğrulama**: API Anahtarı
- **Yaygın Kullanım Durumları**:
  - Sorunları alma ve oluşturma
  - Projeleri listeleme
  - Kullanıcı bilgilerini alma

### 8.3 Google API

- **Kütüphane**: Google.Apis
- **Kimlik Doğrulama**: OAuth 2.0
- **Yaygın Kullanım Durumları**:
  - Google Drive entegrasyonu
  - Google Sheets veri okuma/yazma
  - Google Play Store entegrasyonu

## 9. Sorun Giderme

### 9.1 Geliştirme Sorunları

#### 9.1.1 WebView2 Sorunları

**Sorun**: WebView2 içeriği yüklenmiyor veya JavaScript hataları oluşuyor.

**Çözüm**:
1. WebView2 Runtime'ın yüklü olduğunu kontrol edin
2. Tarayıcı konsolunu açın: `webView.CoreWebView2.OpenDevToolsWindow()`
3. JavaScript hatalarını kontrol edin
4. Ağ isteklerini ve yanıtlarını izleyin

#### 9.1.2 ADB Sorunları

**Sorun**: ADB komutları çalışmıyor veya cihazlar algılanmıyor.

**Çözüm**:
1. ADB sürümünü kontrol edin: `adb version`
2. ADB sunucusunu yeniden başlatın: `adb kill-server && adb start-server`
3. USB hata ayıklama modunun etkin olduğunu kontrol edin
4. Farklı bir USB kablosu veya port deneyin

### 9.2 Derleme Sorunları

**Sorun**: Proje derlenemiyor veya derleme hataları oluşuyor.

**Çözüm**:
1. NuGet paketlerini geri yükleyin
2. Referans yollarını kontrol edin
3. Derleme yapılandırmasını (Debug/Release) kontrol edin
4. Geçici dosyaları temizleyin: `Clean Solution` ve `Rebuild Solution`

---

**Not**: Bu geliştirici kılavuzu, Joygame QA Utility'nin temel geliştirme süreçlerini kapsar. Daha fazla bilgi için kaynak kodunu inceleyin veya geliştirme ekibiyle iletişime geçin.