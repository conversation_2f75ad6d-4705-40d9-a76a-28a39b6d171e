# Joygame QA Utility - <PERSON><PERSON><PERSON>

## İçindekiler

1. [<PERSON><PERSON><PERSON>](#1-g<PERSON><PERSON>)
2. [<PERSON><PERSON>m Mimarisi Genel Bakış](#2-sistem-mimarisi-genel-bakı<PERSON>)
3. [<PERSON><PERSON><PERSON><PERSON><PERSON>](#3-<PERSON><PERSON><PERSON><PERSON><PERSON>)
4. [<PERSON><PERSON>şı](#4-veri-akışı)
5. [Entegrasyon Noktaları](#5-entegrasyon-noktaları)
6. [<PERSON><PERSON><PERSON><PERSON>](#6-güvenlik-mimarisi)
7. [Ölçeklenebilirlik ve Performans](#7-ölçeklenebilirlik-ve-performans)
8. [<PERSON><PERSON> Toleransı ve Kurtarma](#8-hata-toleransı-ve-kurtarma)
9. [Gelecektek<PERSON>](#9-gelecekteki-mimari-gelişmeler)

## 1. <PERSON><PERSON><PERSON>, Joygame QA Utility uygulamasının mimari yapısını detaylı bir ş<PERSON>ilde a<PERSON>. <PERSON><PERSON><PERSON>, a<PERSON><PERSON><PERSON><PERSON><PERSON>, veri ak<PERSON><PERSON> ve entegrasyon noktaları hakkında bilgi <PERSON>.

## 2. Sistem Mimarisi Genel Bakış

Joygame QA Utility, çok katmanlı bir mimariye sahip hibrit bir masaüstü uygulamasıdır. Ana uygulama .NET Framework üzerine inşa edilmiş olup, modern web teknolojileri ile entegre edilmiştir.

### 2.1 Mimari Katmanlar

```
+-------------------+
|    Sunum Katmanı  |
| (Windows Forms &  |
|    WebView2)      |
+--------+----------+
         |
+--------v----------+
|   İş Mantığı      |
|     Katmanı       |
+--------+----------+
         |
+--------v----------+
|    Veri Erişim    |
|     Katmanı       |
+--------+----------+
         |
+--------v----------+
| Harici Servisler  |
| ve Entegrasyonlar |
+-------------------+
```

### 2.2 Ana Bileşenler

- **QA Utility (Ana Uygulama)**: Kullanıcı arayüzü ve temel işlevleri içerir
- **AutoUpdater**: Bağımsız bir uygulama olarak çalışan güncelleme bileşeni
- **WebView2 İçeriği**: Modern web teknolojileri ile oluşturulmuş kullanıcı arayüzü
- **Mobil Cihaz Bağlantı Katmanı**: Android ve iOS cihazlarla iletişim sağlar
- **API Entegrasyon Katmanı**: Harici servislerle iletişim sağlar

## 3. Bileşenler

### 3.1 QA Utility (Ana Uygulama)

#### 3.1.1 Form1.cs

Ana uygulama penceresi ve kullanıcı arayüzü kontrollerini içerir. WebView2 kontrolünü barındırır ve harici servislerle iletişimi yönetir.

**Sorumluluklar**:
- Kullanıcı arayüzünü başlatma ve yönetme
- WebView2 kontrolünü yapılandırma
- Mobil cihaz bağlantılarını yönetme
- Harici servislerle iletişimi koordine etme

#### 3.1.2 Program.cs

Uygulamanın giriş noktasını içerir. Uygulama başlatma mantığını, tek örnek (singleton) kontrolünü ve hata işleme mekanizmalarını yönetir.

**Sorumluluklar**:
- Uygulama başlatma
- Mutex ile tek örnek kontrolü
- Global hata işleme
- Komut satırı argümanlarını işleme

### 3.2 AutoUpdater

Bağımsız bir uygulama olarak çalışan güncelleme bileşeni. Ana uygulamadan bağımsız olarak çalışır ve güncellemeleri indirir, doğrular ve uygular.

**Sorumluluklar**:
- Güncelleme kontrolü
- Dosya indirme ve doğrulama
- Dosya hash kontrolü
- Ana uygulamayı güncelleme

### 3.3 WebView2 İçeriği

Modern web teknolojileri (HTML, CSS, JavaScript) ile oluşturulmuş kullanıcı arayüzü. Ana uygulama içinde WebView2 kontrolü aracılığıyla görüntülenir.

**Dosyalar**:
- `index.html`: Ana HTML yapısı
- `styles.css`: Stil tanımlamaları
- `scripts.js`: JavaScript işlevleri
- `testrail.js`: TestRail entegrasyonu için JavaScript kodları
- `contextMenuHelper.js`: Sağ tıklama menüsü işlevleri

### 3.4 Sınıf Kütüphaneleri

#### 3.4.1 GoogleApiService.cs

Google API'leri ile iletişim için kullanılan sınıf. OAuth2 kimlik doğrulama, Google Drive ve Google Sheets entegrasyonlarını yönetir.

**Sorumluluklar**:
- OAuth2 kimlik doğrulama
- Google Drive dosya işlemleri
- Google Sheets veri işlemleri

#### 3.4.2 LdapAuthenticator.cs

LDAP protokolü üzerinden kimlik doğrulama işlemlerini yöneten sınıf.

**Sorumluluklar**:
- LDAP bağlantısı kurma
- Kullanıcı kimlik doğrulama
- Kullanıcı bilgilerini alma

#### 3.4.3 NvidiaShadowPlayHelper.cs

NVIDIA ShadowPlay ile entegrasyon sağlayan yardımcı sınıf.

**Sorumluluklar**:
- ShadowPlay durumunu kontrol etme
- Ekran kaydı başlatma ve durdurma
- Son 30 saniyeyi kaydetme

#### 3.4.4 TestRail Sınıfları

TestRail API ile iletişim için kullanılan sınıflar.

**Sorumluluklar**:
- TestRail API bağlantısı
- Test durumlarını alma
- Test sonuçlarını gönderme

#### 3.4.5 Redmine.cs

Redmine API ile iletişim için kullanılan sınıf.

**Sorumluluklar**:
- Redmine API bağlantısı
- Sorunları alma ve oluşturma
- Ekleri yönetme

## 4. Veri Akışı

### 4.1 Mobil Cihaz Testi Veri Akışı

```
+-------------+     +-------------+     +-------------+
| Kullanıcı   |---->| QA Utility  |---->| Mobil Cihaz |
| Arayüzü     |     | İş Mantığı  |     | Bağlantısı  |
+-------------+     +-------------+     +------+------+
      ^                    |                  |
      |                    v                  v
+-----+------+     +-------------+     +-------------+
| Sonuçları  |<----| Log ve      |<----| Android/iOS |
| Görüntüle  |     | Medya       |     | Cihaz      |
+------------+     | İşleme      |     +-------------+
                   +-------------+
```

### 4.2 TestRail Entegrasyonu Veri Akışı

```
+-------------+     +-------------+     +-------------+
| Kullanıcı   |---->| TestRail    |---->| TestRail    |
| Arayüzü     |     | API Sınıfı  |     | Sunucusu    |
+-------------+     +-------------+     +------+------+
      ^                                       |
      |                                       v
+-----+------+                          +-------------+
| Test       |<-------------------------| Test        |
| Sonuçları  |                          | Verileri    |
+------------+                          +-------------+
```

## 5. Entegrasyon Noktaları

### 5.1 Harici API Entegrasyonları

| API | Entegrasyon Noktası | Kullanım Amacı |
|-----|---------------------|----------------|
| TestRail API | TestRail sınıfları | Test durumlarını ve sonuçlarını yönetme |
| Redmine API | Redmine.cs | Sorun takibi ve proje yönetimi |
| Google API | GoogleApiService.cs | Google Drive ve Sheets entegrasyonu |
| LDAP | LdapAuthenticator.cs | Kullanıcı kimlik doğrulama |

### 5.2 Mobil Cihaz Entegrasyonları

| Platform | Araç | Entegrasyon Noktası |
|----------|------|---------------------|
| Android | ADB (Android Debug Bridge) | platform-tools/adb.exe |
| iOS | libimobiledevice | ioslog/idevice* araçları |

### 5.3 JavaScript-C# Köprüsü

WebView2 içeriği ile C# kodu arasındaki iletişim, JavaScript-C# köprüsü aracılığıyla sağlanır.

**C# tarafından JavaScript'e veri gönderme**:
```csharp
await webView.ExecuteScriptAsync($"updateDeviceList({deviceListJson});");
```

**JavaScript'ten C# tarafına veri gönderme**:
```javascript
window.chrome.webview.postMessage({action: 'getDeviceInfo', deviceId: selectedDeviceId});
```

**C# tarafında mesajları işleme**:
```csharp
webView.WebMessageReceived += (sender, args) => {
    var message = JsonConvert.DeserializeObject<WebViewMessage>(args.WebMessageAsJson);
    // İlgili işlemleri gerçekleştir
};
```

## 6. Güvenlik Mimarisi

### 6.1 Kimlik Doğrulama ve Yetkilendirme

- **LDAP Kimlik Doğrulama**: Kurumsal kimlik doğrulama için LDAP protokolü kullanılır
- **API Anahtarı Güvenliği**: TestRail, Redmine ve Google API anahtarları şifrelenmiş olarak saklanır
- **Rol Tabanlı Erişim**: Kullanıcı rollerine göre özellik erişimi kısıtlanır

### 6.2 Veri Güvenliği

- **Hassas Veri Şifreleme**: API anahtarları ve kimlik bilgileri şifrelenmiş olarak saklanır
- **Güvenli İletişim**: HTTPS protokolü kullanılarak güvenli API iletişimi sağlanır
- **Yerel Veri Koruması**: Yerel dosyalar ve veritabanı erişimi kısıtlanır

## 7. Ölçeklenebilirlik ve Performans

### 7.1 Performans Optimizasyonları

- **Asenkron İşlemler**: Uzun süren işlemler için asenkron programlama kullanılır
- **Önbellek Mekanizmaları**: Sık kullanılan veriler için önbellek kullanılır
- **Verimli Kaynak Kullanımı**: Sistem kaynakları verimli bir şekilde yönetilir

### 7.2 Ölçeklenebilirlik Stratejileri

- **Modüler Mimari**: Yeni özelliklerin kolayca eklenebilmesi için modüler yapı
- **Eklenti Sistemi**: Gelecekte eklenti desteği için altyapı
- **Yapılandırılabilir Bileşenler**: Farklı kullanım senaryolarına uyum sağlayabilen bileşenler

## 8. Hata Toleransı ve Kurtarma

### 8.1 Hata İşleme Stratejisi

- **Global Hata İşleme**: Beklenmeyen hatalar için global hata işleme mekanizması
- **Yapısal Loglama**: Hataların etkili bir şekilde loglanması ve analiz edilmesi
- **Kullanıcı Bildirimleri**: Hatalar hakkında kullanıcıya anlamlı bildirimler

### 8.2 Kurtarma Mekanizmaları

- **Otomatik Yeniden Deneme**: Geçici hatalar için otomatik yeniden deneme mekanizması
- **Durum Kaydetme**: Uygulama durumunun düzenli olarak kaydedilmesi
- **Güncelleme Geri Alma**: Başarısız güncellemeler için geri alma mekanizması

## 9. Gelecekteki Mimari Gelişmeler

### 9.1 Planlanan Mimari İyileştirmeler

- **.NET 6 Geçişi**: Modern .NET platformuna geçiş
- **Mikro Servis Mimarisi**: Bazı bileşenlerin mikro servis olarak yeniden tasarlanması
- **Konteyner Desteği**: Docker konteyner desteği

### 9.2 Teknoloji Yol Haritası

- **Çapraz Platform Desteği**: macOS ve Linux desteği
- **Bulut Entegrasyonu**: Azure ve AWS entegrasyonları
- **Yapay Zeka ve Makine Öğrenimi**: Test sonuçlarının analizi için AI/ML entegrasyonu

---

## Ek: Mimari Diyagramlar

### Bileşen Diyagramı

```
+---------------------+      +---------------------+
|                     |      |                     |
|    QA Utility       |<---->|    AutoUpdater      |
|    (Ana Uygulama)   |      |    (Güncelleme)     |
|                     |      |                     |
+----------+----------+      +---------------------+
           |
           v
+----------+----------+
|                     |
|    WebView2         |
|    (Web Arayüzü)    |
|                     |
+----------+----------+
           |
           v
+----------+----------+      +---------------------+
|                     |      |                     |
|    Harici Servisler |<---->|    Mobil Cihazlar   |
|    (API Bağlantıları)|      |    (ADB/iOS Araçları)|
|                     |      |                     |
+---------------------+      +---------------------+
```

### Sınıf İlişkileri Diyagramı

```
+---------------+     +---------------+     +---------------+
|               |     |               |     |               |
|    Form1      |<--->| WebView2      |<--->| JavaScript    |
|               |     | Controller    |     | Bridge        |
+-------+-------+     +---------------+     +---------------+
        |
        v
+-------+-------+     +---------------+     +---------------+
|               |     |               |     |               |
| DeviceManager |<--->| AdbHelper     |     | iOSHelper     |
|               |     |               |     |               |
+-------+-------+     +---------------+     +---------------+
        |
        v
+-------+-------+     +---------------+     +---------------+
|               |     |               |     |               |
| ApiManager    |<--->| TestRailApi   |<--->| RedmineApi    |
|               |     |               |     |               |
+---------------+     +---------------+     +---------------+
                                            |               |
                                            | GoogleApi     |
                                            |               |
                                            +---------------+
```